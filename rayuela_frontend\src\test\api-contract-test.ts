/**
 * Test to verify API contract consistency between backend and frontend
 * This test ensures that the RegisterResponse type has the correct structure
 */

import { RegisterResponse } from '../lib/generated/rayuelaAPI';

// Type-level test to ensure RegisterResponse has the correct structure
type TestRegisterResponse = {
  accessToken: string;
  tokenType: string;
  accountId: number;
  userId: number;
  isAdmin: boolean;
  apiKey: string;  // This should be camelCase, not api_key
  message: string;
};

// This will cause a TypeScript error if the types don't match
const _typeTest: RegisterResponse = {} as TestRegisterResponse;
const _reverseTypeTest: TestRegisterResponse = {} as RegisterResponse;

// Runtime test to verify the structure
export function testRegisterResponseStructure() {
  // Mock response that simulates what the backend should return
  const mockBackendResponse = {
    accessToken: "test_token",
    tokenType: "bearer",
    accountId: 123,
    userId: 456,
    isAdmin: true,
    apiKey: "test_api_key_123",  // camelCase field
    message: "Registration successful"
  };

  // This should work without type casting
  const response: RegisterResponse = mockBackendResponse;
  
  // Verify we can access the apiKey field
  const apiKey = response.apiKey;
  
  console.log('✅ RegisterResponse type test passed');
  console.log('✅ API Key field accessible as camelCase:', apiKey);
  
  return {
    success: true,
    apiKey: apiKey,
    message: 'API contract test passed'
  };
}

// Export for potential use in other tests
export { RegisterResponse };
