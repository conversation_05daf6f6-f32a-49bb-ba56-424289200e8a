import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Copy, Check } from 'lucide-react';

interface InitialApiKeyModalProps {
  apiKey: string;
  onContinue: (path: string) => void;
}

export function InitialApiKeyModal({ apiKey, onContinue }: InitialApiKeyModalProps) {
  const [copied, setCopied] = useState(false);
  const [confirmed, setConfirmed] = useState(false);

  const handleCopy = async () => {
    await navigator.clipboard.writeText(apiKey);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold">¡Tu primera API Key está lista!</h2>
        <p className="text-muted-foreground mt-2">
          Esta es tu primera API Key. Puedes generar más claves para diferentes 
          entornos o equipos en la sección 'API Keys' de tu dashboard.
        </p>
      </div>

      <div className="bg-muted p-4 rounded-lg">
        <div className="flex items-center justify-between">
          <code className="text-sm font-mono">{apiKey}</code>
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopy}
            className="ml-2"
          >
            {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            {copied ? 'Copiado' : 'Copiar'}
          </Button>
        </div>
      </div>

      <div className="bg-warning-light border border-warning/20 p-4 rounded-lg">
        <p className="text-sm text-warning-foreground">
          ⚠️ <strong>Importante:</strong> Esta clave no se mostrará de nuevo.
          Si la pierdes, puedes generar una nueva en la sección API Keys.
        </p>
      </div>

      {/* Email verification notice */}
      <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
        <p className="text-sm text-blue-800">
          📧 Antes de poder usar tu API Key, <strong>debes verificar tu dirección de correo electrónico</strong>.
          Revisa tu bandeja de entrada y sigue el enlace de verificación. Hasta entonces, cualquier llamada a la API
          devolverá un error <em>EmailNotVerifiedError</em>.
        </p>
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox 
          id="confirmed" 
          checked={confirmed}
          onCheckedChange={(val) => setConfirmed(val === true)}
        />
        <label htmlFor="confirmed" className="text-sm">
          He guardado mi API Key de forma segura
        </label>
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={() => onContinue('/dashboard/api-keys')}
          disabled={!confirmed}
          className="flex-1"
        >
          Ir a Gestión de API Keys
        </Button>
        <Button 
          variant="outline"
          onClick={() => onContinue('/dashboard')}
          disabled={!confirmed}
          className="flex-1"
        >
          Continuar al Dashboard
        </Button>
      </div>
    </div>
  );
}
