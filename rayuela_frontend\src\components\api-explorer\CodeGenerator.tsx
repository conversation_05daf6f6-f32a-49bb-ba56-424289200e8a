"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Copy, 
  CheckCircle, 
  Download, 
  Code2, 
  FileText,
  Zap,
  Package
} from "lucide-react";

interface CodeTemplate {
  id: string;
  name: string;
  description: string;
  language: string;
  framework?: string;
  category: 'basic' | 'integration' | 'production';
}

interface GeneratorProps {
  endpoint: any;
  parameters: Record<string, any>;
  apiKey: string;
}

const CODE_TEMPLATES: CodeTemplate[] = [
  // JavaScript Templates
  {
    id: 'js-basic',
    name: 'Basic JavaScript',
    description: 'Simple fetch request',
    language: 'javascript',
    category: 'basic'
  },
  {
    id: 'js-sdk',
    name: '<PERSON><PERSON><PERSON>',
    description: 'Using official SDK (recommended)',
    language: 'javascript',
    category: 'basic'
  },
  {
    id: 'js-react',
    name: 'React Hook',
    description: 'Custom React hook for recommendations',
    language: 'javascript',
    framework: 'react',
    category: 'integration'
  },
  {
    id: 'js-nextjs',
    name: 'Next.js API Route',
    description: 'Server-side API route',
    language: 'javascript',
    framework: 'nextjs',
    category: 'integration'
  },
  {
    id: 'js-production',
    name: 'Production Ready',
    description: 'With error handling, retry logic, and caching',
    language: 'javascript',
    category: 'production'
  },

  // Python Templates
  {
    id: 'py-basic',
    name: 'Basic Python',
    description: 'Simple requests call',
    language: 'python',
    category: 'basic'
  },
  {
    id: 'py-sdk',
    name: 'Rayuela SDK',
    description: 'Using official Python SDK',
    language: 'python',
    category: 'basic'
  },
  {
    id: 'py-fastapi',
    name: 'FastAPI Integration',
    description: 'FastAPI endpoint with recommendations',
    language: 'python',
    framework: 'fastapi',
    category: 'integration'
  },
  {
    id: 'py-django',
    name: 'Django View',
    description: 'Django view with recommendations',
    language: 'python',
    framework: 'django',
    category: 'integration'
  },
  {
    id: 'py-production',
    name: 'Production Ready',
    description: 'With async, error handling, and logging',
    language: 'python',
    category: 'production'
  }
];

export default function CodeGenerator({ endpoint, parameters, apiKey }: GeneratorProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<CodeTemplate>(CODE_TEMPLATES[1]); // Default to SDK
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  const generateCode = (template: CodeTemplate): string => {
    const baseUrl = 'https://api.rayuela.ai';
    const fullUrl = `${baseUrl}${endpoint.path}`;
    
    switch (template.id) {
      case 'js-basic':
        return generateJavaScriptBasic(fullUrl, endpoint, parameters, apiKey);
      case 'js-sdk':
        return generateJavaScriptSDK(endpoint, parameters, apiKey);
      case 'js-react':
        return generateReactHook(endpoint, parameters, apiKey);
      case 'js-nextjs':
        return generateNextJSRoute(endpoint, parameters, apiKey);
      case 'js-production':
        return generateJavaScriptProduction(fullUrl, endpoint, parameters, apiKey);
      case 'py-basic':
        return generatePythonBasic(fullUrl, endpoint, parameters, apiKey);
      case 'py-sdk':
        return generatePythonSDK(endpoint, parameters, apiKey);
      case 'py-fastapi':
        return generateFastAPIIntegration(endpoint, parameters, apiKey);
      case 'py-django':
        return generateDjangoView(endpoint, parameters, apiKey);
      case 'py-production':
        return generatePythonProduction(fullUrl, endpoint, parameters, apiKey);
      default:
        return '// Template not found';
    }
  };

  const copyToClipboard = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedCode(id);
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const downloadCode = (code: string, template: CodeTemplate) => {
    const extension = template.language === 'javascript' ? 'js' : 'py';
    const filename = `rayuela-${template.id}.${extension}`;
    
    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const filteredTemplates = CODE_TEMPLATES.filter(template => 
    template.language === selectedTemplate.language
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Code2 className="h-5 w-5" />
          Code Generator
        </CardTitle>
        <CardDescription>
          Generate production-ready code for your application
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Language Selection */}
          <div className="flex gap-2">
            <Button
              variant={selectedTemplate.language === 'javascript' ? 'default' : 'outline'}
              onClick={() => setSelectedTemplate(CODE_TEMPLATES.find(t => t.id === 'js-sdk')!)}
            >
              JavaScript
            </Button>
            <Button
              variant={selectedTemplate.language === 'python' ? 'default' : 'outline'}
              onClick={() => setSelectedTemplate(CODE_TEMPLATES.find(t => t.id === 'py-sdk')!)}
            >
              Python
            </Button>
          </div>

          {/* Template Selection */}
          <div>
            <label className="text-sm font-medium mb-2 block">Template</label>
            <Select
              value={selectedTemplate.id}
              onValueChange={(value) => setSelectedTemplate(CODE_TEMPLATES.find(t => t.id === value)!)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {filteredTemplates.map((template) => (
                  <SelectItem key={template.id} value={template.id}>
                    <div className="flex items-center gap-2">
                      <span>{template.name}</span>
                      {template.framework && (
                        <Badge variant="outline" className="text-xs">
                          {template.framework}
                        </Badge>
                      )}
                      <Badge 
                        variant={template.category === 'production' ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {template.category}
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground mt-1">
              {selectedTemplate.description}
            </p>
          </div>

          {/* Generated Code */}
          <div className="relative">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <span className="text-sm font-medium">Generated Code</span>
              </div>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => downloadCode(generateCode(selectedTemplate), selectedTemplate)}
                >
                  <Download className="h-4 w-4 mr-1" />
                  Download
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => copyToClipboard(generateCode(selectedTemplate), selectedTemplate.id)}
                >
                  {copiedCode === selectedTemplate.id ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
            
            <pre className="bg-muted p-4 rounded-lg overflow-x-auto text-sm max-h-96">
              <code>{generateCode(selectedTemplate)}</code>
            </pre>
          </div>

          {/* Installation Instructions */}
          {(selectedTemplate.id.includes('sdk') || selectedTemplate.category === 'production') && (
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="pt-4">
                <div className="flex items-center gap-2 mb-2">
                  <Package className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800">Installation Required</span>
                </div>
                <div className="space-y-2">
                  {selectedTemplate.language === 'javascript' && (
                    <code className="text-sm bg-white px-2 py-1 rounded border">
                      npm install rayuela-sdk
                    </code>
                  )}
                  {selectedTemplate.language === 'python' && (
                    <code className="text-sm bg-white px-2 py-1 rounded border">
                      pip install rayuela-sdk
                    </code>
                  )}
                  {selectedTemplate.category === 'production' && (
                    <p className="text-sm text-blue-700 mt-2">
                      This template includes production-ready features like error handling, retry logic, and logging.
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Quick Actions */}
          <div className="flex gap-2 pt-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setSelectedTemplate(CODE_TEMPLATES.find(t => t.id === `${selectedTemplate.language}-sdk`)!)}
            >
              <Zap className="h-4 w-4 mr-1" />
              Use SDK (Recommended)
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setSelectedTemplate(CODE_TEMPLATES.find(t => t.id === `${selectedTemplate.language}-production`)!)}
            >
              Production Ready
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Code generation functions
function generateJavaScriptBasic(url: string, endpoint: any, params: any, apiKey: string): string {
  if (endpoint.method === 'POST') {
    return `// Basic JavaScript with fetch
async function getRecommendations() {
  try {
    const response = await fetch('${url}', {
      method: 'POST',
      headers: {
        'X-API-Key': '${apiKey}',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(${JSON.stringify(params, null, 2)})
    });
    
    if (!response.ok) {
      throw new Error(\`HTTP \${response.status}: \${response.statusText}\`);
    }
    
    const data = await response.json();
    console.log('Recommendations:', data);
    return data;
  } catch (error) {
    console.error('Error fetching recommendations:', error);
    throw error;
  }
}

// Usage
getRecommendations();`;
  } else {
    const queryParams = new URLSearchParams(params).toString();
    return `// Basic JavaScript with fetch
async function getRecommendations() {
  try {
    const response = await fetch('${url}${queryParams ? '?' + queryParams : ''}', {
      headers: {
        'X-API-Key': '${apiKey}'
      }
    });
    
    if (!response.ok) {
      throw new Error(\`HTTP \${response.status}: \${response.statusText}\`);
    }
    
    const data = await response.json();
    console.log('Data:', data);
    return data;
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
}

// Usage
getRecommendations();`;
  }
}

function generateJavaScriptSDK(endpoint: any, params: any, apiKey: string): string {
  const isEcommerce = endpoint.id.includes('ecommerce');
  const isAbTest = endpoint.id.includes('ab-test');
  
  if (isEcommerce) {
    return `// Using Rayuela SDK (Recommended)
import Rayuela from 'rayuela-sdk';

const client = new Rayuela({ 
  apiKey: '${apiKey}',
  debug: true // Enable for development
});

async function getHomepageRecommendations() {
  try {
    const recommendations = await client.ecommerce('${params.external_user_id || 'user-123'}', {
      page: 'homepage',
      limit: ${params.limit || 10},
      inStockOnly: true${params.category ? `,\n      category: '${params.category}'` : ''}
    });
    
    console.log(\`Got \${recommendations.items.length} recommendations\`);
    
    // Display recommendations
    recommendations.items.forEach((item, index) => {
      console.log(\`\${index + 1}. \${item.name} - Score: \${item.score.toFixed(2)}\`);
    });
    
    return recommendations;
  } catch (error) {
    console.error('Error:', error.message);
    if (error.code === 'INVALID_API_KEY') {
      console.log('💡 Get your API key at: https://dashboard.rayuela.ai');
    }
  }
}

// Usage
getHomepageRecommendations();`;
  } else if (isAbTest) {
    return `// A/B Testing with Rayuela SDK
import Rayuela from 'rayuela-sdk';

const client = new Rayuela({ apiKey: '${apiKey}' });

async function runAbTest() {
  try {
    // Get recommendations with automatic A/B testing
    const result = await client.abTest('${params.external_user_id || 'user-123'}', {
      limit: ${params.limit || 10}
    });
    
    console.log(\`User assigned to: \${result.variant} group\`);
    console.log(\`Experiment ID: \${result.experimentId}\`);
    console.log(\`Got \${result.items.length} recommendations\`);
    
    // Track user interaction
    if (result.items.length > 0) {
      await client.trackAbTest({
        experimentId: result.experimentId,
        userId: '${params.external_user_id || 'user-123'}',
        eventType: 'click',
        productId: result.items[0].id.toString()
      });
      console.log('✅ Interaction tracked');
    }
    
    // Get experiment results
    const results = await client.getAbTestResults(result.experimentId);
    console.log(\`CTR Lift: \${results.lifts.ctrLiftPercentage}\`);
    
    return result;
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Usage
runAbTest();`;
  } else {
    return `// Using Rayuela SDK
import Rayuela from 'rayuela-sdk';

const client = new Rayuela({ apiKey: '${apiKey}' });

async function getRecommendations() {
  try {
    const recommendations = await client.recommend('${params.external_user_id || 'user-123'}', {
      limit: ${params.limit || 10},
      strategy: 'balanced'
    });
    
    console.log(\`Got \${recommendations.items.length} recommendations\`);
    return recommendations;
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Usage
getRecommendations();`;
  }
}

function generateReactHook(endpoint: any, params: any, apiKey: string): string {
  return `// Custom React Hook for Rayuela Recommendations
import { useState, useEffect } from 'react';
import Rayuela from 'rayuela-sdk';

const client = new Rayuela({ apiKey: '${apiKey}' });

export function useRecommendations(userId, options = {}) {
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchRecommendations = async () => {
    if (!userId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await client.recommend(userId, {
        limit: 10,
        ...options
      });
      setRecommendations(result.items);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecommendations();
  }, [userId, JSON.stringify(options)]);

  const trackInteraction = async (productId, eventType = 'click') => {
    try {
      await client.track({
        userId,
        productId,
        type: eventType
      });
    } catch (err) {
      console.error('Tracking error:', err);
    }
  };

  return {
    recommendations,
    loading,
    error,
    refetch: fetchRecommendations,
    trackInteraction
  };
}

// Usage in component:
// const { recommendations, loading, trackInteraction } = useRecommendations('user-123');`;
}

function generateNextJSRoute(endpoint: any, params: any, apiKey: string): string {
  return `// Next.js API Route (pages/api/recommendations.js)
import Rayuela from 'rayuela-sdk';

const client = new Rayuela({ apiKey: '${apiKey}' });

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userId, limit = 10, strategy = 'balanced' } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'userId is required' });
    }

    const recommendations = await client.recommend(userId, {
      limit,
      strategy
    });

    res.status(200).json({
      success: true,
      data: recommendations
    });
  } catch (error) {
    console.error('Recommendations error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
}

// Usage from frontend:
// const response = await fetch('/api/recommendations', {
//   method: 'POST',
//   headers: { 'Content-Type': 'application/json' },
//   body: JSON.stringify({ userId: 'user-123', limit: 10 })
// });`;
}

function generateJavaScriptProduction(url: string, endpoint: any, params: any, apiKey: string): string {
  return `// Production-ready JavaScript implementation
class RayuelaClient {
  constructor(apiKey, options = {}) {
    this.apiKey = apiKey;
    this.baseUrl = options.baseUrl || 'https://api.rayuela.ai';
    this.timeout = options.timeout || 30000;
    this.retryAttempts = options.retryAttempts || 3;
    this.cache = new Map();
  }

  async makeRequest(endpoint, options = {}) {
    const url = \`\${this.baseUrl}\${endpoint}\`;
    const cacheKey = \`\${options.method || 'GET'}:\${url}:\${JSON.stringify(options.body)}\`;
    
    // Check cache for GET requests
    if (!options.method || options.method === 'GET') {
      const cached = this.cache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < 300000) { // 5 min cache
        return cached.data;
      }
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await this.retryRequest(url, {
        ...options,
        headers: {
          'X-API-Key': this.apiKey,
          'Content-Type': 'application/json',
          ...options.headers
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(\`HTTP \${response.status}: \${response.statusText}\`);
      }

      const data = await response.json();
      
      // Cache successful GET responses
      if (!options.method || options.method === 'GET') {
        this.cache.set(cacheKey, { data, timestamp: Date.now() });
      }

      return data;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  async retryRequest(url, options, attempt = 1) {
    try {
      return await fetch(url, options);
    } catch (error) {
      if (attempt < this.retryAttempts && this.isRetryableError(error)) {
        await this.delay(Math.pow(2, attempt) * 1000); // Exponential backoff
        return this.retryRequest(url, options, attempt + 1);
      }
      throw error;
    }
  }

  isRetryableError(error) {
    return error.name === 'TypeError' || // Network error
           (error.status >= 500 && error.status < 600); // Server error
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async getRecommendations(userId, options = {}) {
    return this.makeRequest('${endpoint.path}', {
      method: '${endpoint.method}',
      body: JSON.stringify({
        external_user_id: userId,
        ...options
      })
    });
  }
}

// Usage
const client = new RayuelaClient('${apiKey}', {
  timeout: 10000,
  retryAttempts: 3
});

async function main() {
  try {
    const recommendations = await client.getRecommendations('${params.external_user_id || 'user-123'}', ${JSON.stringify(params, null, 2)});
    console.log('Success:', recommendations);
  } catch (error) {
    console.error('Error:', error.message);
  }
}

main();`;
}

function generatePythonBasic(url: string, endpoint: any, params: any, apiKey: string): string {
  if (endpoint.method === 'POST') {
    return `# Basic Python with requests
import requests
import json

def get_recommendations():
    url = '${url}'
    headers = {
        'X-API-Key': '${apiKey}',
        'Content-Type': 'application/json'
    }
    data = ${JSON.stringify(params, null, 2).replace(/"/g, "'")}
    
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        
        result = response.json()
        print(f"Got {len(result.get('items', []))} recommendations")
        return result
    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
        raise

# Usage
if __name__ == "__main__":
    recommendations = get_recommendations()`;
  } else {
    return `# Basic Python with requests
import requests

def get_data():
    url = '${url}'
    headers = {'X-API-Key': '${apiKey}'}
    params = ${JSON.stringify(params, null, 2).replace(/"/g, "'")}
    
    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        
        result = response.json()
        print("Data retrieved successfully")
        return result
    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
        raise

# Usage
if __name__ == "__main__":
    data = get_data()`;
  }
}

function generatePythonSDK(endpoint: any, params: any, apiKey: string): string {
  return `# Using Rayuela Python SDK (Recommended)
from rayuela import Rayuela

client = Rayuela(api_key='${apiKey}')

def get_recommendations():
    try:
        recommendations = client.recommend(
            user_id='${params.external_user_id || 'user-123'}',
            limit=${params.limit || 10},
            strategy='balanced'
        )
        
        print(f"Got {len(recommendations.items)} recommendations")
        
        for i, item in enumerate(recommendations.items, 1):
            print(f"{i}. {item.name} - Score: {item.score:.2f}")
        
        return recommendations
    except Exception as e:
        print(f"Error: {e}")
        if hasattr(e, 'code') and e.code == 'INVALID_API_KEY':
            print("💡 Get your API key at: https://dashboard.rayuela.ai")

if __name__ == "__main__":
    get_recommendations()`;
}

function generateFastAPIIntegration(endpoint: any, params: any, apiKey: string): string {
  return `# FastAPI Integration
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from rayuela import Rayuela
from typing import Optional

app = FastAPI()
client = Rayuela(api_key='${apiKey}')

class RecommendationRequest(BaseModel):
    user_id: str
    limit: Optional[int] = 10
    strategy: Optional[str] = 'balanced'

@app.post("/recommendations")
async def get_recommendations(request: RecommendationRequest):
    try:
        recommendations = client.recommend(
            user_id=request.user_id,
            limit=request.limit,
            strategy=request.strategy
        )
        
        return {
            "success": True,
            "data": recommendations.dict(),
            "count": len(recommendations.items)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/track")
async def track_interaction(user_id: str, product_id: str, event_type: str = "click"):
    try:
        client.track(
            user_id=user_id,
            product_id=product_id,
            type=event_type
        )
        return {"success": True, "message": "Interaction tracked"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Run with: uvicorn main:app --reload`;
}

function generateDjangoView(endpoint: any, params: any, apiKey: string): string {
  return `# Django Views
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from rayuela import Rayuela
import json

client = Rayuela(api_key='${apiKey}')

@csrf_exempt
@require_http_methods(["POST"])
def get_recommendations(request):
    try:
        data = json.loads(request.body)
        user_id = data.get('user_id')
        
        if not user_id:
            return JsonResponse({'error': 'user_id is required'}, status=400)
        
        recommendations = client.recommend(
            user_id=user_id,
            limit=data.get('limit', 10),
            strategy=data.get('strategy', 'balanced')
        )
        
        return JsonResponse({
            'success': True,
            'data': recommendations.dict(),
            'count': len(recommendations.items)
        })
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def track_interaction(request):
    try:
        data = json.loads(request.body)
        
        client.track(
            user_id=data['user_id'],
            product_id=data['product_id'],
            type=data.get('event_type', 'click')
        )
        
        return JsonResponse({'success': True})
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

# Add to urls.py:
# path('recommendations/', views.get_recommendations, name='recommendations'),
# path('track/', views.track_interaction, name='track'),`;
}

function generatePythonProduction(url: string, endpoint: any, params: any, apiKey: string): string {
  return `# Production-ready Python implementation
import asyncio
import aiohttp
import logging
from typing import Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime, timedelta

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class RayuelaConfig:
    api_key: str
    base_url: str = 'https://api.rayuela.ai'
    timeout: int = 30
    retry_attempts: int = 3
    cache_ttl: int = 300  # 5 minutes

class RayuelaClient:
    def __init__(self, config: RayuelaConfig):
        self.config = config
        self.cache = {}
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config.timeout),
            headers={'X-API-Key': self.config.api_key}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def make_request(self, endpoint: str, method: str = 'GET', data: Optional[Dict] = None) -> Dict[str, Any]:
        url = f"{self.config.base_url}{endpoint}"
        cache_key = f"{method}:{url}:{str(data)}"
        
        # Check cache for GET requests
        if method == 'GET' and cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if datetime.now() - timestamp < timedelta(seconds=self.config.cache_ttl):
                logger.info(f"Cache hit for {url}")
                return cached_data
        
        for attempt in range(self.config.retry_attempts):
            try:
                async with self.session.request(method, url, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        # Cache successful GET responses
                        if method == 'GET':
                            self.cache[cache_key] = (result, datetime.now())
                        
                        logger.info(f"Request successful: {method} {url}")
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"HTTP {response.status}: {error_text}")
                        
                        if response.status < 500:  # Don't retry client errors
                            break
                            
            except asyncio.TimeoutError:
                logger.warning(f"Timeout on attempt {attempt + 1}")
            except Exception as e:
                logger.error(f"Request failed on attempt {attempt + 1}: {e}")
            
            if attempt < self.config.retry_attempts - 1:
                wait_time = 2 ** attempt  # Exponential backoff
                logger.info(f"Retrying in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
        
        raise Exception(f"Failed to complete request after {self.config.retry_attempts} attempts")
    
    async def get_recommendations(self, user_id: str, **options) -> Dict[str, Any]:
        data = {
            'external_user_id': user_id,
            **options
        }
        return await self.make_request('${endpoint.path}', '${endpoint.method}', data)

# Usage
async def main():
    config = RayuelaConfig(api_key='${apiKey}')
    
    async with RayuelaClient(config) as client:
        try:
            recommendations = await client.get_recommendations(
                '${params.external_user_id || 'user-123'}',
                **${JSON.stringify(params, null, 2).replace(/"/g, "'")}
            )
            logger.info(f"Got {len(recommendations.get('items', []))} recommendations")
            return recommendations
        except Exception as e:
            logger.error(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())`;
}
