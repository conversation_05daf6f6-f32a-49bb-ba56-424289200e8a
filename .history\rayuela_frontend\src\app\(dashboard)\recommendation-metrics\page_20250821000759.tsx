"use client";

import { useState } from 'react';
import useS<PERSON> from 'swr';
import { useAuth } from '@/lib/auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
// import { Skeleton } from '@/components/ui/skeleton'; // No usado actualmente
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { handleApiError } from '@/lib/error-handler';
import { TooltipHelper } from '@/components/ui/tooltip-helper';
import {
  BarChart3Icon,
  RefreshCwIcon,
  ShieldCheckIcon
} from 'lucide-react';
import MetricRecommendations from '@/components/dashboard/MetricRecommendations';
import { getRecommendationPerformance, getConfidenceMetrics } from '@/lib/api/recommendation-metrics';
import AdminRoute from '@/components/auth/AdminRoute';
import { DensePageLayout, DenseCard } from "@/components/ui/layout";

export default function RecommendationMetricsPage() {
  const { token, apiKey } = useAuth();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('performance');
  const [selectedModelId, setSelectedModelId] = useState<string>('all');
  const [selectedMetricType, setSelectedMetricType] = useState<string>('all');

  // Get recommendation performance metrics
  const {
    data: performanceData,
    error: performanceError,
    isLoading: isPerformanceLoading,
    mutate: mutatePerformance
  } = useSWR(
    token && apiKey ? ['recommendation-performance', selectedModelId, selectedMetricType] : null,
    async ([, modelId, metricType]) => {
      return await getRecommendationPerformance(
        modelId !== 'all' ? parseInt(modelId) : undefined,
        metricType !== 'all' ? metricType : undefined
      );
    }
  );

  // Get confidence metrics
  const {
    data: confidenceData,
    error: confidenceError,
    isLoading: isConfidenceLoading,
    mutate: mutateConfidence
  } = useSWR(
    token && apiKey ? 'confidence-metrics' : null,
    async () => {
      return await getConfidenceMetrics(apiKey!);
    }
  );

  // Función para refrescar los datos
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([mutatePerformance(), mutateConfidence()]);
      toast.success('Métricas actualizadas');
          } catch (error: unknown) {
      handleApiError(error, 'Error al actualizar las métricas');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Obtener lista de modelos disponibles para el filtro
  const availableModels: Array<{id: string, name: string}> = [];
  
  // Safe access to models data
  if (performanceData && performanceData.offlineMetrics) {
    const offlineMetrics = performanceData.offlineMetrics;
    if (offlineMetrics.models && Array.isArray(offlineMetrics.models)) {
      offlineMetrics.models.forEach((model: any) => {
        availableModels.push({
          id: model.model_id?.toString() || model.id?.toString() || Math.random().toString(),
          name: `${model.model_type || model.type || 'Model'} (${model.version || 'v1'})`
        });
      });
    }
  }

  // Lista de tipos de métricas disponibles
  const metricTypes = [
    { id: 'precision', name: 'Precisión' },
    { id: 'recall', name: 'Recall' },
    { id: 'ndcg', name: 'NDCG' },
    { id: 'map', name: 'MAP' },
    { id: 'catalog_coverage', name: 'Cobertura del Catálogo' },
    { id: 'diversity', name: 'Diversidad' },
    { id: 'novelty', name: 'Novedad' },
    { id: 'serendipity', name: 'Serendipia' },
  ];

  return (
    <AdminRoute>
      <DensePageLayout
        title="Métricas de Recomendación"
        description="Análisis detallado del rendimiento y confianza de los modelos de recomendación"
        actions={
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing || isPerformanceLoading || isConfidenceLoading}
            className="flex items-center gap-2"
          >
            <RefreshCwIcon className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
        }
      >
        {/* Información contextual */}
        <DenseCard title="Acerca de las Métricas" icon={<BarChart3Icon className="h-5 w-5" /> }>
          <CardContent className="p-6 space-y-3 text-sm">
            <p>
              Esta página muestra métricas detalladas sobre el rendimiento y la confianza de los modelos de recomendación. Pase el cursor sobre cualquier métrica para obtener más información.
            </p>
          </CardContent>
        </DenseCard>

        {/* Metric Recommendations Component */}
        {performanceData && confidenceData && (
          <MetricRecommendations
            performanceData={performanceData}
            confidenceData={confidenceData}
            isLoading={isPerformanceLoading || isConfidenceLoading}
          />
        )}

        {/* Tabs para cambiar entre métricas de rendimiento y confianza */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-2 w-full max-w-md">
            <TabsTrigger value="performance" className="flex items-center gap-2">
              <BarChart3Icon className="h-4 w-4" />
              Rendimiento
            </TabsTrigger>
            <TabsTrigger value="confidence" className="flex items-center gap-2">
              <ShieldCheckIcon className="h-4 w-4" />
              Confianza
            </TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Contenido de la pestaña de rendimiento */}
        {activeTab === 'performance' && (
          <div className="space-y-6">
            {/* Filtros para métricas de rendimiento */}
            <div className="flex flex-wrap gap-4">
              <div className="w-full md:w-auto">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Modelo
                </label>
                <Select value={selectedModelId} onValueChange={setSelectedModelId}>
                  <SelectTrigger className="w-full md:w-[200px]">
                    <SelectValue placeholder="Todos los modelos" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos los modelos</SelectItem>
                    {availableModels.map((model) => (
                      <SelectItem key={model.id} value={model.id}>{model.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full md:w-auto">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Tipo de Métrica
                </label>
                <Select value={selectedMetricType} onValueChange={setSelectedMetricType}>
                  <SelectTrigger className="w-full md:w-[200px]">
                    <SelectValue placeholder="Todas las métricas" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todas las métricas</SelectItem>
                    {metricTypes.map(metric => (
                      <SelectItem key={metric.id} value={metric.id}>{metric.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Performance metrics summary cards */}
            {performanceData && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-sm font-medium text-muted-foreground">Precisión</CardTitle>
                      <TooltipHelper
                        content="Porcentaje de recomendaciones relevantes del total de recomendaciones mostradas"
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {(() => {
                        if (performanceData && performanceData.offlineMetrics) {
                          const precision = performanceData.offlineMetrics.precision as number | undefined;
                          return precision ? `${(precision * 100).toFixed(1)}%` : '--';
                        }
                        return '--';
                      })()}
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-sm font-medium text-muted-foreground">NDCG</CardTitle>
                      <TooltipHelper
                        content="Normalized Discounted Cumulative Gain - evalúa la calidad del ranking"
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {(() => {
                        if (performanceData && performanceData.offlineMetrics) {
                          const ndcg = performanceData.offlineMetrics.ndcg as number | undefined;
                          return ndcg ? `${(ndcg * 100).toFixed(1)}%` : '--';
                        }
                        return '--';
                      })()}
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">Diversidad</CardTitle>
                      <TooltipHelper
                        content="Variedad en las recomendaciones mostradas"
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {(() => {
                        if (performanceData && performanceData.offlineMetrics) {
                          const diversity = performanceData.offlineMetrics.diversity as number | undefined;
                          return diversity ? `${(diversity * 100).toFixed(1)}%` : '--';
                        }
                        return '--';
                      })()}
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">Cobertura</CardTitle>
                      <TooltipHelper
                        content="Porcentaje del catálogo que se está recomendando"
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {(() => {
                        if (performanceData && performanceData.offlineMetrics) {
                          const catalogCoverage = performanceData.offlineMetrics.catalogCoverage as number | undefined;
                          return catalogCoverage ? `${(catalogCoverage * 100).toFixed(1)}%` : '--';
                        }
                        return '--';
                      })()}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Loading state */}
            {isPerformanceLoading && (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            )}

            {/* Error state */}
            {performanceError && (
              <div className="text-center py-8">
                <p className="text-red-500">Error al cargar métricas de rendimiento</p>
              </div>
            )}
          </div>
        )}

        {/* Contenido de la pestaña de confianza */}
        {activeTab === 'confidence' && (
          <div className="space-y-6">
            {/* Confidence metrics display */}
            {confidenceData && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Distribución de Confianza</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {typeof confidenceData === 'object' && confidenceData ? (
                        Object.entries((confidenceData as Record<string, unknown>).confidence_distribution || {}).map(([modelType, data]: [string, unknown]) => {
                          const confidenceInfo = data as Record<string, unknown>;
                          return (
                            <div key={modelType} className="flex justify-between items-center">
                              <span className="capitalize">{modelType}</span>
                              <span className="font-medium">
                                {confidenceInfo.avg ? `${((confidenceInfo.avg as number) * 100).toFixed(1)}%` : 'N/A'}
                              </span>
                            </div>
                          );
                        })
                      ) : (
                        <div className="text-center py-4">
                          <p className="text-gray-500">Datos de confianza no disponibles</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Confianza por Categoría</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {typeof confidenceData === 'object' && confidenceData ? (
                        Object.entries((confidenceData as Record<string, unknown>).category_confidence || {}).slice(0, 5).map(([category, confidence]: [string, unknown]) => (
                          <div key={category} className="flex justify-between items-center">
                            <span className="capitalize">{category}</span>
                            <span className="font-medium">
                              {typeof confidence === 'number' ? `${(confidence * 100).toFixed(1)}%` : 'N/A'}
                            </span>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-4">
                          <p className="text-gray-500">Datos por categoría no disponibles</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Loading state */}
            {isConfidenceLoading && (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            )}

            {/* Error state */}
            {confidenceError && (
              <div className="text-center py-8">
                <p className="text-red-500">Error al cargar métricas de confianza</p>
              </div>
            )}
          </div>
        )}
      </DensePageLayout>
    </AdminRoute>
  );
}
