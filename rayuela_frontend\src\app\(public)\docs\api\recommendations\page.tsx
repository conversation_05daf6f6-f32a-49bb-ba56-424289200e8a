import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowLeft, Code, Zap, Filter, Target } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'API de Recomendaciones - Referencia',
  description: 'Documentación completa de la API de recomendaciones de Rayuela. Endpoints, parámetros, filtros y ejemplos de código para obtener recomendaciones personalizadas.',
  path: '/docs/api/recommendations',
  keywords: ['API', 'recomendaciones', 'endpoints', 'filtros', 'personalización', 'machine learning'],
});

export default function RecommendationsApiPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="mb-8">
          <Button variant="ghost" asChild className="mb-4">
            <Link href="/docs">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver a Documentación
            </Link>
          </Button>
          
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            API de Recomendaciones
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl">
            Documentación completa para obtener recomendaciones personalizadas usando machine learning.
          </p>
        </div>

        {/* Overview */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Visión General</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              La API de recomendaciones de Rayuela utiliza algoritmos de machine learning para generar 
              recomendaciones personalizadas basadas en el comportamiento del usuario, preferencias y 
              características de los productos.
            </p>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="flex items-center gap-2">
                <Target className="w-5 h-5 text-primary" />
                <span className="text-sm">Personalización avanzada</span>
              </div>
              <div className="flex items-center gap-2">
                <Filter className="w-5 h-5 text-primary" />
                <span className="text-sm">Filtros flexibles</span>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="w-5 h-5 text-primary" />
                <span className="text-sm">Respuesta en tiempo real</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Endpoint */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code className="w-5 h-5" />
              Recomendaciones Personalizadas
            </CardTitle>
            <div className="flex gap-2 mt-2">
              <Badge variant="default">POST</Badge>
              <Badge variant="outline">/recommendations/personalized/query</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Obtiene recomendaciones personalizadas para un usuario específico basadas en su historial 
              de interacciones y preferencias.
            </p>

            <h4 className="font-semibold mb-3">Parámetros del Request</h4>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm mb-4">
              <pre className="text-sm font-mono overflow-x-auto">
{`{
  "user_external_id": "user_123",
  "limit": 10,
  "strategy": "collaborative_filtering",
  "context": {
    "page_type": "product_detail",
    "device_type": "mobile"
  },
  "filters": {
    "category": "electronics",
    "price_min": 100,
    "price_max": 1000,
    "in_stock": true
  },
  "exclude_products": ["prod_123", "prod_456"]
}`}
              </pre>
            </div>

            <h4 className="font-semibold mb-3">Ejemplo de Request</h4>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm mb-4">
              <pre className="text-sm font-mono overflow-x-auto">
{`curl -X POST "https://api.rayuela.ai/api/v1/recommendations/personalized/query" \\
     -H "X-API-Key: sk_prod_tu_api_key" \\
     -H "Content-Type: application/json" \\
     -d '{
       "user_external_id": "user_123",
       "limit": 5,
       "filters": {
         "category": "electronics"
       }
     }'`}
              </pre>
            </div>

            <h4 className="font-semibold mb-3">Respuesta</h4>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
              <pre className="text-sm font-mono overflow-x-auto">
{`{
  "items": [
    {
      "product_external_id": "prod_789",
      "score": 0.95,
      "reason": "similar_users",
      "explanation": "Usuarios similares compraron este producto"
    },
    {
      "product_external_id": "prod_456",
      "score": 0.87,
      "reason": "category_affinity",
      "explanation": "Basado en tu interés en electrónicos"
    }
  ],
  "total_count": 2,
  "strategy_used": "collaborative_filtering",
  "request_id": "req_abc123"
}`}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Strategies */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Estrategias de Recomendación</CardTitle>
            <CardDescription>
              Diferentes algoritmos disponibles para generar recomendaciones
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="p-4 border rounded-lg">
                <h4 className="font-semibold mb-2">collaborative_filtering</h4>
                <p className="text-sm text-muted-foreground">
                  Basado en usuarios similares. Recomendaciones de productos que compraron usuarios con gustos similares.
                </p>
              </div>
              <div className="p-4 border rounded-lg">
                <h4 className="font-semibold mb-2">content_based</h4>
                <p className="text-sm text-muted-foreground">
                  Basado en características del producto. Recomienda productos similares a los que le gustaron al usuario.
                </p>
              </div>
              <div className="p-4 border rounded-lg">
                <h4 className="font-semibold mb-2">hybrid</h4>
                <p className="text-sm text-muted-foreground">
                  Combina múltiples estrategias para obtener mejores resultados. Estrategia recomendada.
                </p>
              </div>
              <div className="p-4 border rounded-lg">
                <h4 className="font-semibold mb-2">trending</h4>
                <p className="text-sm text-muted-foreground">
                  Productos populares y con tendencia al alza. Útil para usuarios nuevos sin historial.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Filters */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Filtros Disponibles</CardTitle>
            <CardDescription>
              Parámetros para refinar las recomendaciones
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="font-semibold mb-2">Filtros de Producto</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• <code>category</code> - Categoría específica</li>
                    <li>• <code>price_min</code> / <code>price_max</code> - Rango de precios</li>
                    <li>• <code>brand</code> - Marca específica</li>
                    <li>• <code>in_stock</code> - Solo productos en stock</li>
                    <li>• <code>rating_min</code> - Calificación mínima</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Filtros de Contexto</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• <code>page_type</code> - Tipo de página (home, product_detail, cart)</li>
                    <li>• <code>device_type</code> - Dispositivo (mobile, desktop, tablet)</li>
                    <li>• <code>location</code> - Ubicación geográfica</li>
                    <li>• <code>time_of_day</code> - Momento del día</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Siguientes Pasos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <Button asChild variant="outline">
                <Link href="/docs/api/authentication">
                  Autenticación
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/docs/api/batch">
                  Ingesta de Datos
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/docs/guides/data-ingestion">
                  Guía de Datos
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/docs/examples/recommendations">
                  Ejemplos Avanzados
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
