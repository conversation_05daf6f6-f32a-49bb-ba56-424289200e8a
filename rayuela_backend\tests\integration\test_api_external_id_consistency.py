"""
Tests for API consistency across all endpoints that support external IDs.

This test suite verifies that:
1. All endpoints that accept internal IDs also have external ID equivalents
2. Both internal and external ID endpoints produce consistent results
3. Error handling is consistent across all endpoints
4. Tenant isolation works correctly for all endpoints
"""

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from decimal import Decimal
from unittest.mock import patch, AsyncMock

from src.db.models.end_user import EndUser
from src.db.models.product import Product
from src.db.models.account import Account


class TestAPIExternalIdConsistency:
    """Test API consistency for external ID support."""

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession, test_account: Account):
        """Create a test user with external_id."""
        user = EndUser(
            account_id=test_account.account_id,
            external_id="api_test_user_123",
            is_active=True
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    async def test_product(self, db_session: AsyncSession, test_account: Account):
        """Create a test product with external_id."""
        product = Product(
            account_id=test_account.account_id,
            external_id="api_test_product_456",
            name="API Test Product",
            price=Decimal("99.99"),
            category="test",
            is_active=True
        )
        db_session.add(product)
        await db_session.commit()
        await db_session.refresh(product)
        return product

    @patch("src.services.recommendation_service.RecommendationService.get_also_bought")
    async def test_also_bought_consistency(
        self,
        mock_get_also_bought: AsyncMock,
        async_client: AsyncClient,
        test_product: Product,
        test_api_key: str
    ):
        """Test that also-bought endpoints return consistent results."""
        # Mock the service to return consistent data
        mock_response = {
            "items": [{"product_id": 1, "external_id": "prod_1", "name": "Product 1"}],
            "total": 1,
            "skip": 0,
            "limit": 10
        }
        mock_get_also_bought.return_value = mock_response

        # Call internal ID endpoint
        response1 = await async_client.get(
            f"/api/v1/recommendations/also-bought/{test_product.product_id}",
            headers={"X-API-Key": test_api_key}
        )
        
        # Call external ID endpoint
        response2 = await async_client.get(
            f"/api/v1/recommendations/also-bought/external/{test_product.external_id}",
            headers={"X-API-Key": test_api_key}
        )

        # Both should succeed
        assert response1.status_code == 200
        assert response2.status_code == 200

        # Both should call the service with the same internal product_id
        assert mock_get_also_bought.call_count == 2
        call1_args = mock_get_also_bought.call_args_list[0]
        call2_args = mock_get_also_bought.call_args_list[1]
        
        # Extract product_id from both calls
        assert call1_args.kwargs["product_id"] == test_product.product_id
        assert call2_args.kwargs["product_id"] == test_product.product_id

    @patch("src.services.recommendation_service.RecommendationService.get_item_explanation")
    async def test_explanation_consistency(
        self,
        mock_get_explanation: AsyncMock,
        async_client: AsyncClient,
        test_user: EndUser,
        test_product: Product,
        test_api_key: str
    ):
        """Test that explanation endpoints return consistent results."""
        mock_response = {
            "explanation": "Test explanation",
            "confidence": 0.85,
            "reasons": ["reason1", "reason2"]
        }
        mock_get_explanation.return_value = mock_response

        # Call internal ID endpoint
        response1 = await async_client.get(
            f"/api/v1/recommendations/explain/{test_user.user_id}/{test_product.product_id}",
            headers={"X-API-Key": test_api_key}
        )
        
        # Call external ID endpoint
        response2 = await async_client.get(
            f"/api/v1/recommendations/explain/external/{test_user.external_id}/{test_product.external_id}",
            headers={"X-API-Key": test_api_key}
        )

        # Both should succeed
        assert response1.status_code == 200
        assert response2.status_code == 200

        # Both should call the service with the same internal IDs
        assert mock_get_explanation.call_count == 2
        call1_args = mock_get_explanation.call_args_list[0]
        call2_args = mock_get_explanation.call_args_list[1]
        
        assert call1_args.kwargs["user_id"] == test_user.user_id
        assert call1_args.kwargs["item_id"] == test_product.product_id
        assert call2_args.kwargs["user_id"] == test_user.user_id
        assert call2_args.kwargs["item_id"] == test_product.product_id

    async def test_error_handling_consistency(
        self,
        async_client: AsyncClient,
        test_api_key: str
    ):
        """Test that error handling is consistent across internal and external ID endpoints."""
        
        # Test non-existent internal IDs
        response1 = await async_client.get(
            "/api/v1/recommendations/also-bought/99999",
            headers={"X-API-Key": test_api_key}
        )
        
        # Test non-existent external IDs
        response2 = await async_client.get(
            "/api/v1/recommendations/also-bought/external/nonexistent_product",
            headers={"X-API-Key": test_api_key}
        )

        # Both should return 404
        assert response1.status_code == 404
        assert response2.status_code == 404

        # Error messages should be informative
        data1 = response1.json()
        data2 = response2.json()
        assert "detail" in data1
        assert "detail" in data2

    async def test_tenant_isolation_consistency(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        test_api_key: str
    ):
        """Test that tenant isolation works consistently for both internal and external ID endpoints."""
        # Create a product in a different account
        other_account = Account(
            name="Other Account",
            email="<EMAIL>",
            api_key="other_api_key"
        )
        db_session.add(other_account)
        await db_session.flush()

        other_product = Product(
            account_id=other_account.account_id,
            external_id="other_product_123",
            name="Other Product",
            price=Decimal("50.00"),
            category="other",
            is_active=True
        )
        db_session.add(other_product)
        await db_session.commit()

        # Try to access other account's product using current account's API key
        response1 = await async_client.get(
            f"/api/v1/recommendations/also-bought/{other_product.product_id}",
            headers={"X-API-Key": test_api_key}
        )
        
        response2 = await async_client.get(
            f"/api/v1/recommendations/also-bought/external/{other_product.external_id}",
            headers={"X-API-Key": test_api_key}
        )

        # Both should fail with 404 (not found in current account)
        assert response1.status_code == 404
        assert response2.status_code == 404

    async def test_all_external_id_endpoints_exist(
        self,
        async_client: AsyncClient,
        test_user: EndUser,
        test_product: Product,
        test_api_key: str
    ):
        """Test that all expected external ID endpoints exist and are accessible."""
        
        # List of external ID endpoints that should exist
        external_endpoints = [
            f"/api/v1/recommendations/also-bought/external/{test_product.external_id}",
            f"/api/v1/recommendations/explain/external/{test_user.external_id}/{test_product.external_id}",
            f"/api/v1/recommendations/invalidate-cache/external/{test_user.external_id}",
            f"/api/v1/products/external/{test_product.external_id}",
        ]

        for endpoint in external_endpoints:
            response = await async_client.get(endpoint, headers={"X-API-Key": test_api_key})
            # Should not return 404 (endpoint not found)
            # May return other errors (like 422 for missing parameters), but endpoint should exist
            assert response.status_code != 404, f"Endpoint {endpoint} not found"

    @patch("src.services.recommendation_service.RecommendationService.get_similar_products")
    async def test_similar_products_consistency(
        self,
        mock_get_similar: AsyncMock,
        async_client: AsyncClient,
        test_product: Product,
        test_api_key: str
    ):
        """Test that similar products endpoints return consistent results."""
        mock_response = {
            "items": [{"product_id": 2, "external_id": "similar_1", "name": "Similar Product"}],
            "total": 1,
            "skip": 0,
            "limit": 10
        }
        mock_get_similar.return_value = mock_response

        # Call internal ID endpoint
        response1 = await async_client.get(
            f"/api/v1/recommendations/products/{test_product.product_id}/similar",
            headers={"X-API-Key": test_api_key}
        )
        
        # Call external ID endpoint
        response2 = await async_client.get(
            f"/api/v1/recommendations/products/external/{test_product.external_id}/similar",
            headers={"X-API-Key": test_api_key}
        )

        # Both should succeed
        assert response1.status_code == 200
        assert response2.status_code == 200

        # Both should call the service with the same internal product_id
        assert mock_get_similar.call_count == 2
        call1_args = mock_get_similar.call_args_list[0]
        call2_args = mock_get_similar.call_args_list[1]
        
        assert call1_args.kwargs["product_id"] == test_product.product_id
        assert call2_args.kwargs["product_id"] == test_product.product_id

    async def test_parameter_validation_consistency(
        self,
        async_client: AsyncClient,
        test_api_key: str
    ):
        """Test that parameter validation is consistent across endpoints."""
        
        # Test invalid internal ID (negative number)
        response1 = await async_client.get(
            "/api/v1/recommendations/also-bought/-1",
            headers={"X-API-Key": test_api_key}
        )
        
        # Test invalid external ID (empty string)
        response2 = await async_client.get(
            "/api/v1/recommendations/also-bought/external/",
            headers={"X-API-Key": test_api_key}
        )

        # Both should return validation errors
        assert response1.status_code in [400, 422]  # Bad request or validation error
        assert response2.status_code in [404, 422]  # Not found or validation error
