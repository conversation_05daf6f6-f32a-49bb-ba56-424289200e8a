"""
Tests for ID mapping utilities to ensure they handle all nomenclature variations correctly.

This test suite verifies that the ID mapping utilities properly handle:
- Legacy nomenclature (end_user_id, endUserId)
- Current nomenclature (user_id, userId)
- External ID resolution
- Edge cases and error handling
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession

from src.utils.user_id_mapper import extract_user_id_from_request_data
from src.utils.product_id_mapper import (
    extract_product_id_from_request_data,
    resolve_product_external_id,
    resolve_product_internal_id,
    ProductIdMapper,
    map_product_id_to_external_id,
    map_external_product_id_to_product_id
)


class TestUserIdMapper:
    """Test user ID mapping utilities."""

    def test_extract_user_id_current_nomenclature(self):
        """Test extraction with current nomenclature (user_id)."""
        data = {"user_id": 123}
        result = extract_user_id_from_request_data(data)
        assert result == 123

    def test_extract_user_id_legacy_nomenclature(self):
        """Test extraction with legacy nomenclature (end_user_id)."""
        data = {"end_user_id": 456}
        result = extract_user_id_from_request_data(data)
        assert result == 456

    def test_extract_user_id_camel_case(self):
        """Test extraction with camelCase nomenclature."""
        data = {"userId": 789}
        result = extract_user_id_from_request_data(data)
        assert result == 789

    def test_extract_user_id_legacy_camel_case(self):
        """Test extraction with legacy camelCase nomenclature."""
        data = {"endUserId": 101}
        result = extract_user_id_from_request_data(data)
        assert result == 101

    def test_extract_user_id_string_number(self):
        """Test extraction with string number."""
        data = {"user_id": "123"}
        result = extract_user_id_from_request_data(data)
        assert result == 123

    def test_extract_user_id_priority_order(self):
        """Test that user_id takes priority over legacy nomenclature."""
        data = {
            "user_id": 123,
            "end_user_id": 456,
            "userId": 789,
            "endUserId": 101
        }
        result = extract_user_id_from_request_data(data)
        assert result == 123  # user_id should take priority

    def test_extract_user_id_invalid_cases(self):
        """Test extraction with invalid cases."""
        # Non-dict input
        assert extract_user_id_from_request_data("not_a_dict") is None
        
        # Empty dict
        assert extract_user_id_from_request_data({}) is None
        
        # Invalid user_id values
        assert extract_user_id_from_request_data({"user_id": 0}) is None
        assert extract_user_id_from_request_data({"user_id": -1}) is None
        assert extract_user_id_from_request_data({"user_id": "abc"}) is None
        assert extract_user_id_from_request_data({"user_id": None}) is None


class TestProductIdMapper:
    """Test product ID mapping utilities."""

    def test_extract_product_id_external_priority(self):
        """Test that external_product_id takes priority."""
        data = {
            "external_product_id": "ext_123",
            "product_id": "int_456",
            "productId": "camel_789"
        }
        result = extract_product_id_from_request_data(data)
        assert result == "ext_123"

    def test_extract_product_id_various_formats(self):
        """Test extraction with various naming formats."""
        test_cases = [
            ({"external_product_id": "ext_123"}, "ext_123"),
            ({"product_id": "prod_456"}, "prod_456"),
            ({"productId": "camel_789"}, "camel_789"),
            ({"externalProductId": "extCamel_101"}, "extCamel_101"),
        ]
        
        for data, expected in test_cases:
            result = extract_product_id_from_request_data(data)
            assert result == expected

    def test_extract_product_id_integer_conversion(self):
        """Test that integer product IDs are converted to strings."""
        data = {"product_id": 123}
        result = extract_product_id_from_request_data(data)
        assert result == "123"

    def test_map_product_id_to_external_id(self):
        """Test mapping from internal to external format."""
        data = {"product_id": 123, "other_field": "value"}
        result = map_product_id_to_external_id(data)
        
        assert "external_product_id" in result
        assert result["external_product_id"] == 123
        assert "product_id" not in result
        assert result["other_field"] == "value"

    def test_map_external_product_id_to_product_id(self):
        """Test mapping from external to internal format."""
        data = {"external_product_id": "ext_123", "other_field": "value"}
        result = map_external_product_id_to_product_id(data)
        
        assert "product_id" in result
        assert result["product_id"] == "ext_123"
        assert "external_product_id" not in result
        assert result["other_field"] == "value"

    def test_mapping_functions_preserve_original(self):
        """Test that mapping functions don't modify the original data."""
        original_data = {"product_id": 123, "other_field": "value"}
        original_copy = original_data.copy()
        
        result = map_product_id_to_external_id(original_data)
        
        # Original should be unchanged
        assert original_data == original_copy
        # Result should be different
        assert result != original_data

    def test_mapping_functions_handle_non_dict(self):
        """Test that mapping functions handle non-dict input gracefully."""
        assert map_product_id_to_external_id("not_a_dict") == "not_a_dict"
        assert map_external_product_id_to_product_id(None) is None


class TestProductIdMapperClass:
    """Test the ProductIdMapper class."""

    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def product_mapper(self, mock_db):
        """Create ProductIdMapper instance."""
        return ProductIdMapper(mock_db, account_id=1)

    async def test_extract_product_id_with_resolution(self, product_mapper, mock_db):
        """Test extract_product_id method with external ID resolution."""
        # Mock the resolve function
        with pytest.mock.patch(
            'src.utils.product_id_mapper.resolve_product_external_id',
            return_value=123
        ) as mock_resolve:
            data = {"external_product_id": "ext_123"}
            result = await product_mapper.extract_product_id(data)
            
            assert result == 123
            mock_resolve.assert_called_once_with(mock_db, 1, "ext_123")

    async def test_to_external_format(self, product_mapper, mock_db):
        """Test conversion to external format."""
        with pytest.mock.patch(
            'src.utils.product_id_mapper.resolve_product_internal_id',
            return_value="ext_123"
        ) as mock_resolve:
            data = {"product_id": 123, "other_field": "value"}
            result = await product_mapper.to_external_format(data)
            
            assert result["external_product_id"] == "ext_123"
            assert result["other_field"] == "value"
            mock_resolve.assert_called_once_with(mock_db, 1, 123)

    async def test_to_internal_format(self, product_mapper, mock_db):
        """Test conversion to internal format."""
        with pytest.mock.patch(
            'src.utils.product_id_mapper.resolve_product_external_id',
            return_value=123
        ) as mock_resolve:
            data = {"external_product_id": "ext_123", "productId": "ignored"}
            result = await product_mapper.to_internal_format(data)
            
            assert result["product_id"] == 123
            assert "external_product_id" not in result
            assert "productId" not in result
            mock_resolve.assert_called_once_with(mock_db, 1, "ext_123")


class TestEdgeCasesAndErrorHandling:
    """Test edge cases and error handling in ID mapping utilities."""

    def test_empty_and_none_inputs(self):
        """Test handling of empty and None inputs."""
        assert extract_user_id_from_request_data({}) is None
        assert extract_user_id_from_request_data(None) is None
        assert extract_product_id_from_request_data({}) is None
        assert extract_product_id_from_request_data(None) is None

    def test_mixed_data_types(self):
        """Test handling of mixed data types."""
        # User ID with mixed types
        data = {"user_id": "123", "end_user_id": 456}
        result = extract_user_id_from_request_data(data)
        assert result == 123  # String should be converted

        # Product ID with mixed types
        data = {"product_id": 123, "external_product_id": "ext_456"}
        result = extract_product_id_from_request_data(data)
        assert result == "ext_456"  # External should take priority

    async def test_database_error_handling(self):
        """Test handling of database errors in async functions."""
        mock_db = AsyncMock(spec=AsyncSession)
        mock_db.execute.side_effect = Exception("Database error")
        
        # Should return None on database errors, not raise
        result = await resolve_product_external_id(mock_db, 1, "ext_123")
        assert result is None
        
        result = await resolve_product_internal_id(mock_db, 1, 123)
        assert result is None
