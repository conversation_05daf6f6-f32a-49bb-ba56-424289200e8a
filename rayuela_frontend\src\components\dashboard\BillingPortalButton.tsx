"use client";

import { useState } from 'react';
import { Button, ButtonProps } from '@/components/ui/button';
import { useAuth } from '@/lib/auth';
import { createBillingPortalSession } from '@/lib/api';
import { toast } from 'sonner';
import { ExternalLinkIcon, LoaderIcon } from 'lucide-react';

interface BillingPortalButtonProps extends ButtonProps {}

export function BillingPortalButton({
  children,
  className,
  variant = "outline",
  ...props
}: BillingPortalButtonProps) {
  const { token, apiKey } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const handlePortalAccess = async () => {
    if (!token || !apiKey) {
      toast.error("Debes iniciar sesión para realizar esta acción");
      return;
    }

    setIsLoading(true);
    try {
      // Llamar a la API para crear una sesión del Portal de Facturación de Mercado Pago
      const response = await createBillingPortalSession() as { url?: string };

      // Redirigir a la página del Portal de Facturación de Mercado Pago
      if (response.url) {
        window.location.href = response.url;
      } else {
        throw new Error("No se recibió una URL de redirección");
      }
    } catch (error: unknown) {
      console.error("Error al crear sesión del Portal de Facturación:", error);
      toast.error((error as Error).message || "Error al acceder al portal de facturación");
      setIsLoading(false);
    }
    // No necesitamos un finally con setIsLoading(false) porque la página se recargará después de la redirección
  };

  return (
    <Button
      onClick={handlePortalAccess}
      disabled={isLoading}
      className={className}
      variant={variant}
      {...props}
    >
      {isLoading ? (
        <>
          <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
          Redirigiendo...
        </>
      ) : (
        <>
          <ExternalLinkIcon className="mr-2 h-4 w-4" />
          {children || "Gestionar Facturación"}
        </>
      )}
    </Button>
  );
}
