import React from 'react';
import { Badge } from "@/components/ui/badge";
import { 
  CheckCircleIcon, 
  ClockIcon, 
  XCircleIcon, 
  AlertTriangleIcon,
  PlayIcon,
  RefreshCwIcon
} from 'lucide-react';

/**
 * Formats bytes to a human-readable string
 * @param bytes - Number of bytes to format
 * @param decimals - Number of decimal places (default: 2)
 * @returns Formatted string (e.g., "1.23 MB")
 */
export function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Formats large numbers with locale-specific thousands separators
 * @param num - Number to format
 * @returns Formatted string (e.g., "1,234,567")
 */
export function formatNumber(num: number): string {
  return new Intl.NumberFormat().format(num);
}

/**
 * Formats a percentage value
 * @param value - Value between 0 and 1
 * @param decimals - Number of decimal places (default: 1)
 * @returns Formatted percentage string
 */
export function formatPercentage(value: number, decimals = 1): string {
  return `${(value * 100).toFixed(decimals)}%`;
}

/**
 * Returns an appropriate icon for a given status
 * @param status - Status string (e.g., "completed", "running", "failed")
 * @returns React icon component
 */
export function getStatusIcon(status: string): React.ReactElement {
  const iconProps = { className: "h-4 w-4" };
  
  switch (status?.toLowerCase()) {
    case 'completed':
    case 'success':
    case 'finished':
      return <CheckCircleIcon {...iconProps} className="h-4 w-4 text-green-500" />;
    case 'running':
    case 'processing':
    case 'in_progress':
      return <RefreshCwIcon {...iconProps} className="h-4 w-4 text-blue-500 animate-spin" />;
    case 'pending':
    case 'queued':
    case 'waiting':
      return <ClockIcon {...iconProps} className="h-4 w-4 text-yellow-500" />;
    case 'failed':
    case 'error':
    case 'cancelled':
      return <XCircleIcon {...iconProps} className="h-4 w-4 text-red-500" />;
    case 'starting':
    case 'initializing':
      return <PlayIcon {...iconProps} className="h-4 w-4 text-blue-400" />;
    case 'warning':
      return <AlertTriangleIcon {...iconProps} className="h-4 w-4 text-amber-500" />;
    default:
      return <ClockIcon {...iconProps} className="h-4 w-4 text-muted-foreground/70" />;
  }
}

/**
 * Returns an appropriate badge component for a given status
 * @param status - Status string (e.g., "completed", "running", "failed")
 * @returns React Badge component
 */
export function getStatusBadge(status: string): React.ReactElement {
  switch (status?.toLowerCase()) {
    case 'completed':
    case 'success':
    case 'finished':
      return (
        <Badge variant="success" className="text-xs">
          Completado
        </Badge>
      );
    case 'running':
    case 'processing':
    case 'in_progress':
      return (
        <Badge variant="info" className="text-xs">
          En progreso
        </Badge>
      );
    case 'pending':
    case 'queued':
    case 'waiting':
      return (
        <Badge variant="warning" className="text-xs">
          Pendiente
        </Badge>
      );
    case 'failed':
    case 'error':
    case 'cancelled':
      return (
        <Badge variant="destructive" className="text-xs">
          Fallido
        </Badge>
      );
    case 'starting':
    case 'initializing':
      return (
        <Badge variant="secondary" className="text-xs">
          Iniciando
        </Badge>
      );
    case 'warning':
      return (
        <Badge variant="warning" className="text-xs">
          Advertencia
        </Badge>
      );
    default:
      return (
        <Badge variant="outline" className="text-xs">
          Desconocido
        </Badge>
      );
  }
}

/**
 * Formats date strings to human-readable format
 * @param dateString - ISO date string or null
 * @param includeTime - Whether to include time (default: true)
 * @returns Formatted date string
 */
export function formatDate(dateString: string | null, includeTime = true): string {
  if (!dateString) return 'No disponible';
  
  try {
    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      ...(includeTime && {
        hour: '2-digit',
        minute: '2-digit'
      })
    };
    
    return new Intl.DateTimeFormat('es-ES', options).format(date);
  } catch (error) {
    console.error("Error al formatear fecha:", error);
    return 'Formato de fecha inválido';
  }
}

/**
 * Formats a duration in milliseconds to human-readable format
 * @param milliseconds - Duration in milliseconds
 * @returns Formatted duration string (e.g., "2h 30m 15s")
 */
export function formatDuration(milliseconds: number): string {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}d ${hours % 24}h ${minutes % 60}m`;
  }
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  }
  if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  }
  return `${seconds}s`;
}

/**
 * Truncates text to a specified length with ellipsis
 * @param text - Text to truncate
 * @param maxLength - Maximum length (default: 50)
 * @returns Truncated text with ellipsis if needed
 */
export function truncateText(text: string, maxLength = 50): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
} 