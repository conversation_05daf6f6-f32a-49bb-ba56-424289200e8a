{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/lib/generated/rayuelaAPI.ts"], "sourcesContent": ["/**\n * Generated by orval v6.31.0 🍺\n * Do not edit manually.\n * Rayuela\n * OpenAPI spec version: v1\n */\nimport axios from \"axios\";\nimport type { AxiosRequestConfig, AxiosResponse } from \"axios\";\nexport type GetUsageHistoryApiV1UsageGetParams = {\n  /**\n   * Start date for historical data\n   */\n  start_date?: string | null;\n  /**\n   * End date for historical data\n   */\n  end_date?: string | null;\n};\n\nexport type ListBatchJobsApiV1IngestionBatchGetParams = {\n  /**\n   * Maximum number of jobs to return\n   */\n  limit?: number;\n  /**\n   * Filter by job status\n   */\n  status?: string | null;\n};\n\nexport type RefreshStorageUsageApiV1StorageRefreshPost200 = {\n  [key: string]: unknown;\n};\n\nexport type GetStorageUsageApiV1StorageUsageGet200 = { [key: string]: unknown };\n\nexport type GetSubscriptionUsageApiV1SubscriptionUsageGet200 = {\n  [key: string]: unknown;\n};\n\nexport type MonitorTablesApiV1MaintenanceMaintenanceMonitorTablesPost200 = {\n  [key: string]: unknown;\n};\n\nexport type MonitorTablesApiV1MaintenanceMaintenanceMonitorTablesPostParams = {\n  run_async?: boolean;\n};\n\nexport type GetSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGet200 =\n  { [key: string]: unknown };\n\nexport type GetSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGetParams =\n  {\n    account_id?: number | null;\n    run_async?: boolean;\n  };\n\nexport type CleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPost200 =\n  { [key: string]: unknown };\n\nexport type CleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPostParams =\n  {\n    retention_days?: number;\n    account_id?: number | null;\n    dry_run?: boolean;\n    run_async?: boolean;\n  };\n\nexport type ListArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGet200 =\n  { [key: string]: unknown };\n\nexport type ListArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGetParams =\n  {\n    start_date?: string | null;\n    end_date?: string | null;\n    account_id?: number | null;\n  };\n\nexport type ArchiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPost200 =\n  { [key: string]: unknown };\n\nexport type ArchiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPostParams =\n  {\n    days_to_keep?: number;\n    account_id?: number | null;\n    batch_size?: number;\n    run_async?: boolean;\n  };\n\nexport type ArchiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPost200 =\n  { [key: string]: unknown };\n\nexport type ArchiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPostParams =\n  {\n    days_to_keep?: number;\n    account_id?: number | null;\n    batch_size?: number;\n    run_async?: boolean;\n  };\n\nexport type GetTaskStatusApiV1MaintenanceMaintenanceTaskTaskIdGet200 = {\n  [key: string]: unknown;\n};\n\nexport type CleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPost200 =\n  { [key: string]: unknown };\n\nexport type CleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPostParams =\n  {\n    days_to_keep?: number;\n    account_id?: number | null;\n    batch_size?: number;\n    run_async?: boolean;\n  };\n\nexport type CleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPost200 =\n  { [key: string]: unknown };\n\nexport type CleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPostParams =\n  {\n    days_to_keep?: number;\n    account_id?: number | null;\n    run_async?: boolean;\n  };\n\nexport type GetMetricsHistoryApiV1AnalyticsAnalyticsMetricsHistoryGetParams = {\n  /**\n   * Name of the metric to get history for\n   */\n  metric_name: string;\n  /**\n   * Maximum number of historical data points to return\n   */\n  limit?: number;\n  account_id?: number | null;\n};\n\nexport type CompareModelVersionsApiV1AnalyticsAnalyticsModelsCompareGetParams =\n  {\n    /**\n     * List of model IDs to compare\n     */\n    model_ids?: number[];\n    /**\n     * Maximum number of models to compare if model_ids not provided\n     */\n    limit?: number;\n    account_id?: number | null;\n  };\n\nexport type GetRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGetParams =\n  {\n    /**\n     * Filter by specific model ID\n     */\n    model_id?: number | null;\n    /**\n     * Filter by metric type (ndcg, map, catalog_coverage, etc.)\n     */\n    metric_type?: string | null;\n    account_id?: number | null;\n  };\n\nexport type GetEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGet200Item = {\n  [key: string]: unknown;\n};\n\nexport type GetEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGetParams = {\n  /**\n   * Filter by specific endpoint\n   */\n  endpoint?: string | null;\n  /**\n   * Filter by HTTP method\n   */\n  method?: string | null;\n  account_id?: number | null;\n};\n\nexport type GetAccountAnalyticsApiV1AnalyticsAnalyticsAccountGet200 = {\n  [key: string]: unknown;\n};\n\nexport type GetAccountAnalyticsApiV1AnalyticsAnalyticsAccountGetParams = {\n  account_id?: number | null;\n};\n\nexport type ListTrainingJobsApiV1PipelineJobsGetParams = {\n  /**\n   * Maximum number of jobs to return\n   */\n  limit?: number;\n  /**\n   * Filter by job status\n   */\n  status?: string | null;\n};\n\nexport type TrainingCallbackApiV1PipelineCallbackJobIdPost200 = {\n  [key: string]: unknown;\n};\n\nexport type TrainingCallbackApiV1PipelineCallbackJobIdPostBody = {\n  [key: string]: unknown;\n};\n\nexport type ProcessTrainingJobApiV1PipelineProcessPost200 = {\n  [key: string]: unknown;\n};\n\nexport type ProcessTrainingJobApiV1PipelineProcessPostBody = {\n  [key: string]: unknown;\n};\n\nexport type TrainArtifactForAccountApiV1PipelineTrainAccountIdPost202 = {\n  [key: string]: unknown;\n};\n\nexport type InvalidateCacheApiV1PipelineInvalidateCachePostParams = {\n  model_type?: string | null;\n  metric_type?: string | null;\n};\n\nexport type ListModelsApiV1PipelineModelsGetParams = {\n  limit?: number;\n  offset?: number;\n};\n\nexport type ReadInteractionsApiV1InteractionsGetParams = {\n  skip?: number;\n  limit?: number;\n};\n\nexport type GetRecommendationPerformanceApiV1RecommendationsPerformanceGetParams =\n  {\n    /**\n     * Filter by specific model ID\n     */\n    model_id?: number | null;\n    /**\n     * Filter by metric type (ndcg, map, catalog_coverage, etc.)\n     */\n    metric_type?: string | null;\n    account_id?: number | null;\n  };\n\nexport type GetConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet200 = {\n  [key: string]: unknown;\n};\n\nexport type GetAlsoBoughtExternalApiV1RecommendationsAlsoBoughtExternalExternalProductIdGetParams =\n  {\n    skip?: number;\n    limit?: number;\n  };\n\nexport type GetSimilarProductsExternalApiV1RecommendationsProductsExternalExternalProductIdSimilarGetParams =\n  {\n    limit?: number;\n    include_explanation?: boolean;\n    explanation_level?: ExplanationLevel;\n    skip?: number;\n  };\n\nexport type QueryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPostBody =\n  RecommendationQueryRequest | RecommendationQueryExternalRequest;\n\nexport type GetSimilarProductsApiV1RecommendationsProductsProductIdSimilarGetParams =\n  {\n    limit?: number;\n    /**\n     * Incluir explicación de la similitud\n     */\n    include_explanation?: boolean;\n    /**\n     * Nivel de detalle de la explicación\n     */\n    explanation_level?: ExplanationLevel;\n    skip?: number;\n  };\n\nexport type GetAlsoBoughtApiV1RecommendationsAlsoBoughtProductIdGetParams = {\n  skip?: number;\n  limit?: number;\n};\n\nexport type GetRelatedCategoriesApiV1RecommendationsRelatedCategoriesCategoryGetParams =\n  {\n    skip?: number;\n    limit?: number;\n  };\n\nexport type GetCategoryProductsApiV1RecommendationsCategoryCategoryGetSortBy =\n  (typeof GetCategoryProductsApiV1RecommendationsCategoryCategoryGetSortBy)[keyof typeof GetCategoryProductsApiV1RecommendationsCategoryCategoryGetSortBy];\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const GetCategoryProductsApiV1RecommendationsCategoryCategoryGetSortBy =\n  {\n    popularity: \"popularity\",\n    rating: \"rating\",\n    price_asc: \"price_asc\",\n    price_desc: \"price_desc\",\n  } as const;\n\nexport type GetCategoryProductsApiV1RecommendationsCategoryCategoryGetParams = {\n  sort_by?: GetCategoryProductsApiV1RecommendationsCategoryCategoryGetSortBy;\n  skip?: number;\n  limit?: number;\n};\n\nexport type GetTopRatedApiV1RecommendationsTopRatedGetParams = {\n  min_ratings?: number;\n  category?: string | null;\n  /**\n   * Model type to use for recommendations\n   */\n  model_type?: string;\n  /**\n   * Incluir explicación de la recomendación\n   */\n  include_explanation?: boolean;\n  skip?: number;\n  limit?: number;\n};\n\nexport type GetMostSoldApiV1RecommendationsMostSoldGetTimeframe =\n  (typeof GetMostSoldApiV1RecommendationsMostSoldGetTimeframe)[keyof typeof GetMostSoldApiV1RecommendationsMostSoldGetTimeframe];\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const GetMostSoldApiV1RecommendationsMostSoldGetTimeframe = {\n  day: \"day\",\n  week: \"week\",\n  month: \"month\",\n} as const;\n\nexport type GetMostSoldApiV1RecommendationsMostSoldGetParams = {\n  timeframe?: GetMostSoldApiV1RecommendationsMostSoldGetTimeframe;\n  category?: string | null;\n  skip?: number;\n  limit?: number;\n};\n\nexport type GetRelatedSearchesApiV1RecommendationsRelatedSearchesProductIdGetParams =\n  {\n    skip?: number;\n    limit?: number;\n  };\n\nexport type GetPopularTrendsApiV1RecommendationsPopularTrendsGetParams = {\n  /**\n   * Model type to use for recommendations\n   */\n  model_type?: string;\n  /**\n   * Incluir explicación de la recomendación\n   */\n  include_explanation?: boolean;\n  skip?: number;\n  limit?: number;\n};\n\nexport type GetTrendingSearchesApiV1RecommendationsTrendingSearchesGetParams = {\n  skip?: number;\n  limit?: number;\n};\n\nexport type GetMostSearchedApiV1RecommendationsMostSearchedGetTimeframe =\n  (typeof GetMostSearchedApiV1RecommendationsMostSearchedGetTimeframe)[keyof typeof GetMostSearchedApiV1RecommendationsMostSearchedGetTimeframe];\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const GetMostSearchedApiV1RecommendationsMostSearchedGetTimeframe = {\n  day: \"day\",\n  week: \"week\",\n  month: \"month\",\n} as const;\n\nexport type GetMostSearchedApiV1RecommendationsMostSearchedGetParams = {\n  timeframe?: GetMostSearchedApiV1RecommendationsMostSearchedGetTimeframe;\n  skip?: number;\n  limit?: number;\n};\n\nexport type ReadProductsApiV1ProductsGetParams = {\n  category?: string;\n  skip?: number;\n  limit?: number;\n};\n\nexport type ReadEndUsersApiV1EndUsersGetParams = {\n  skip?: number;\n  limit?: number;\n};\n\nexport type GetAvailablePlansApiV1PlansGet200 = {\n  [key: string]: SrcApiV1EndpointsPlansPlanInfo;\n};\n\nexport type GetAuditLogsApiV1AccountsAccountIdAuditLogsGetParams = {\n  action?: string | null;\n  artifact_name?: string | null;\n  start_date?: string | null;\n  end_date?: string | null;\n};\n\nexport type VerifyEmailApiV1AuthVerifyEmailGetParams = {\n  token: string;\n};\n\nexport type AuthHealthCheckApiV1HealthAuthGet200 = { [key: string]: unknown };\n\nexport type DbHealthCheckApiV1HealthDbGet200 = { [key: string]: unknown };\n\nexport type HealthCheckApiV1HealthGet200 = { [key: string]: unknown };\n\n/**\n * Plan information\n */\nexport interface SrcDbSchemasUsageSummaryPlanInfo {\n  /** Plan features */\n  features: PlanFeatures;\n  /** Plan ID */\n  id: string;\n  /** Plan limits */\n  limits: PlanLimits;\n}\n\nexport type SrcApiV1EndpointsPlansPlanInfoMercadopagoPriceId = string | null;\n\nexport type SrcApiV1EndpointsPlansPlanInfoLimits = { [key: string]: unknown };\n\n/**\n * Plan information model.\n */\nexport interface SrcApiV1EndpointsPlansPlanInfo {\n  contact_required?: boolean;\n  description: string;\n  features: string[];\n  id: string;\n  limits: SrcApiV1EndpointsPlansPlanInfoLimits;\n  mercadopago_price_id?: SrcApiV1EndpointsPlansPlanInfoMercadopagoPriceId;\n  name: string;\n  price: string;\n  recommended?: boolean;\n}\n\nexport type ValidationErrorLocItem = string | number;\n\nexport interface ValidationError {\n  loc: ValidationErrorLocItem[];\n  msg: string;\n  type: string;\n}\n\n/**\n * Billing portal URL\n */\nexport type UsageSummaryResponseBillingPortalUrl = string | null;\n\n/**\n * All available plans\n */\nexport type UsageSummaryResponseAllPlans = {\n  [key: string]: SrcDbSchemasUsageSummaryPlanInfo;\n};\n\n/**\n * Complete usage summary response\n */\nexport interface UsageSummaryResponse {\n  /** All available plans */\n  allPlans?: UsageSummaryResponseAllPlans;\n  /** API calls usage */\n  apiCalls: ApiCallsUsage;\n  /** Billing portal URL */\n  billingPortalUrl?: UsageSummaryResponseBillingPortalUrl;\n  /** Current plan limits */\n  planLimits: PlanLimits;\n  /** Storage usage */\n  storage: StorageUsage;\n  subscription: SubscriptionInfo;\n  /** Training usage */\n  training: TrainingUsage;\n}\n\nexport type UsageStatsUpdatedAt = string | null;\n\nexport type UsageStatsLastReset = string | null;\n\n/**\n * Estadísticas de uso de la API\n */\nexport interface UsageStats {\n  api_calls_count?: number;\n  last_reset?: UsageStatsLastReset;\n  storage_used?: number;\n  updated_at?: UsageStatsUpdatedAt;\n}\n\n/**\n * Usage history item response schema\n */\nexport interface UsageHistoryItemResponse {\n  /** Number of API calls made on this date */\n  apiCalls: number;\n  /** Date of the usage record */\n  date: string;\n  /** Storage used in bytes on this date */\n  storage: number;\n}\n\n/**\n * Next available training time\n */\nexport type TrainingUsageNextAvailable = string | null;\n\n/**\n * Date of last training\n */\nexport type TrainingUsageLastTrainingDate = string | null;\n\n/**\n * Training usage information\n */\nexport interface TrainingUsage {\n  /** Whether training is available now */\n  canTrainNow?: boolean;\n  /** Training frequency limit */\n  frequencyLimit?: string;\n  /** Date of last training */\n  lastTrainingDate?: TrainingUsageLastTrainingDate;\n  /** Next available training time */\n  nextAvailable?: TrainingUsageNextAvailable;\n}\n\nexport type TrainingStatusTrainingTime = number | null;\n\nexport type TrainingStatusRecall = number | null;\n\nexport type TrainingStatusPrecision = number | null;\n\nexport type TrainingStatusNdcg = number | null;\n\nexport type TrainingStatusMap = number | null;\n\nexport type TrainingStatusDiversity = number | null;\n\nexport type TrainingStatusDataPoints = number | null;\n\nexport type TrainingStatusCustomMetricsAnyOf = { [key: string]: unknown };\n\nexport type TrainingStatusCustomMetrics =\n  TrainingStatusCustomMetricsAnyOf | null;\n\nexport type TrainingStatusCoverage = number | null;\n\n/**\n * Estado del entrenamiento y métricas\n */\nexport interface TrainingStatus {\n  coverage?: TrainingStatusCoverage;\n  customMetrics?: TrainingStatusCustomMetrics;\n  dataPoints?: TrainingStatusDataPoints;\n  diversity?: TrainingStatusDiversity;\n  map?: TrainingStatusMap;\n  ndcg?: TrainingStatusNdcg;\n  precision?: TrainingStatusPrecision;\n  recall?: TrainingStatusRecall;\n  trainingTime?: TrainingStatusTrainingTime;\n}\n\nexport type TrainingResponseTaskId = string | null;\n\nexport type TrainingResponseJobId = number | null;\n\nexport type TrainingResponseAccountId = number | null;\n\n/**\n * Respuesta para el inicio de entrenamiento\n */\nexport interface TrainingResponse {\n  accountId?: TrainingResponseAccountId;\n  jobId?: TrainingResponseJobId;\n  message: string;\n  taskId?: TrainingResponseTaskId;\n}\n\nexport type TrainingJobStatusTaskId = string | null;\n\nexport type TrainingJobStatusStartedAt = string | null;\n\n/**\n * Estado detallado de un trabajo de entrenamiento\n */\nexport interface TrainingJobStatus {\n  completedAt?: TrainingJobStatusCompletedAt;\n  createdAt: string;\n  errorMessage?: TrainingJobStatusErrorMessage;\n  jobId: number;\n  metrics?: TrainingJobStatusMetrics;\n  model?: TrainingJobStatusModel;\n  parameters?: TrainingJobStatusParameters;\n  startedAt?: TrainingJobStatusStartedAt;\n  status: string;\n  taskId?: TrainingJobStatusTaskId;\n}\n\nexport type TrainingJobStatusParametersAnyOf = { [key: string]: unknown };\n\nexport type TrainingJobStatusParameters =\n  TrainingJobStatusParametersAnyOf | null;\n\nexport type TrainingJobStatusModel = ModelInfo | null;\n\nexport type TrainingJobStatusMetricsAnyOf = { [key: string]: unknown };\n\nexport type TrainingJobStatusMetrics = TrainingJobStatusMetricsAnyOf | null;\n\nexport type TrainingJobStatusErrorMessage = string | null;\n\nexport type TrainingJobStatusCompletedAt = string | null;\n\nexport type SystemUserUpdatePassword = string | null;\n\nexport interface SystemUserUpdate {\n  email: string;\n  password?: SystemUserUpdatePassword;\n}\n\nexport type SystemUserResponseDeletedAt = string | null;\n\n/**\n * Esquema de respuesta para usuarios del sistema que excluye campos sensibles\n */\nexport interface SystemUserResponse {\n  account_id: number;\n  created_at: string;\n  deleted_at?: SystemUserResponseDeletedAt;\n  email: string;\n  id: number;\n  is_active: boolean;\n  is_admin: boolean;\n  roles?: Role[];\n  updated_at: string;\n}\n\nexport interface SystemUserCreate {\n  email: string;\n  password: string;\n}\n\n/**\n * Expiration date\n */\nexport type SubscriptionInfoExpiresAt = string | null;\n\n/**\n * Subscription information\n */\nexport interface SubscriptionInfo {\n  /** Expiration date */\n  expiresAt?: SubscriptionInfoExpiresAt;\n  /** Whether subscription is active */\n  isActive?: boolean;\n  /** Plan type */\n  plan: string;\n}\n\nexport type SubscriptionBasicInfoExpiresAt = string | null;\n\n/**\n * Información básica de suscripción para incluir en las respuestas de cuenta\n */\nexport interface SubscriptionBasicInfo {\n  expiresAt?: SubscriptionBasicInfoExpiresAt;\n  isActive: boolean;\n  plan: string;\n}\n\n/**\n * Source of measurement (redis_cache, database_calculation)\n */\nexport type StorageUsageSource = string | null;\n\n/**\n * Last measurement timestamp\n */\nexport type StorageUsageLastMeasured = string | null;\n\n/**\n * Storage usage information\n */\nexport interface StorageUsage {\n  /** Last measurement timestamp */\n  lastMeasured?: StorageUsageLastMeasured;\n  /** Storage limit in bytes */\n  limitBytes?: number;\n  /** Percentage of limit used */\n  percentage?: number;\n  /** Source of measurement (redis_cache, database_calculation) */\n  source?: StorageUsageSource;\n  /** Storage used in bytes */\n  usedBytes?: number;\n}\n\n/**\n * Dirección de ordenamiento\n */\nexport type SortDirection = (typeof SortDirection)[keyof typeof SortDirection];\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const SortDirection = {\n  asc: \"asc\",\n  desc: \"desc\",\n} as const;\n\n/**\n * Configuración de ordenamiento\n */\nexport interface SortConfig {\n  /** Dirección del ordenamiento */\n  direction?: SortDirection;\n  /** Campo por el cual ordenar */\n  field: string;\n}\n\nexport interface Search {\n  account_id: number;\n  id: number;\n  query: string;\n  timestamp: string;\n  user_id: number;\n}\n\nexport type RoleType = (typeof RoleType)[keyof typeof RoleType];\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const RoleType = {\n  ADMIN: \"ADMIN\",\n  EDITOR: \"EDITOR\",\n  VIEWER: \"VIEWER\",\n} as const;\n\n/**\n * Descripción del rol\n */\nexport type RoleCreateDescription = string | null;\n\nexport interface RoleCreate {\n  /** Descripción del rol */\n  description?: RoleCreateDescription;\n  name: RoleType;\n}\n\n/**\n * Descripción del rol\n */\nexport type RoleDescription = string | null;\n\nexport interface Role {\n  account_id: number;\n  created_at: string;\n  /** Descripción del rol */\n  description?: RoleDescription;\n  id: number;\n  name: RoleType;\n  updated_at: string;\n}\n\n/**\n * Registration response schema\n */\nexport interface RegisterResponse {\n  /** JWT access token */\n  accessToken: string;\n  /** Account ID */\n  accountId: number;\n  /** Initial API key for the user */\n  apiKey: string;\n  /** Whether user is admin */\n  isAdmin: boolean;\n  /** Success message */\n  message: string;\n  /** Token type (bearer) */\n  tokenType: string;\n  /** User ID */\n  userId: number;\n}\n\n/**\n * Schema para el registro completo de una cuenta con usuario administrador.\n */\nexport interface RegisterRequest {\n  /**\n   * Nombre de la cuenta\n   * @minLength 1\n   */\n  accountName: string;\n  /** Email del usuario administrador */\n  email: string;\n  /**\n   * Contraseña del usuario administrador\n   * @minLength 8\n   */\n  password: string;\n}\n\n/**\n * Estrategia de recomendación a utilizar\n */\nexport type RecommendationQueryRequestStrategy = string | null;\n\n/**\n * Configuración de ordenamiento\n */\nexport type RecommendationQueryRequestSortBy = SortConfig | null;\n\n/**\n * Filtros estructurados para aplicar a las recomendaciones\n */\nexport type RecommendationQueryRequestFilters = FilterGroup | null;\n\n/**\n * Contexto explícito para filtrar o re-rankear recomendaciones\n */\nexport type RecommendationQueryRequestContext = RecommendationContext | null;\n\n/**\n * Esquema para solicitudes de recomendaciones con filtros complejos.\nPermite especificar filtros estructurados y otros parámetros en el cuerpo de la solicitud.\n */\nexport interface RecommendationQueryRequest {\n  /** Contexto explícito para filtrar o re-rankear recomendaciones */\n  context?: RecommendationQueryRequestContext;\n  /** Filtros estructurados para aplicar a las recomendaciones */\n  filters?: RecommendationQueryRequestFilters;\n  /** Incluir explicación de la recomendación */\n  include_explanation?: boolean;\n  /**\n   * Número máximo de elementos a devolver\n   * @maximum 100\n   */\n  limit?: number;\n  /** Tipo de modelo a utilizar */\n  model_type?: string;\n  /**\n   * Número de elementos a saltar\n   * @minimum 0\n   */\n  skip?: number;\n  /** Configuración de ordenamiento */\n  sort_by?: RecommendationQueryRequestSortBy;\n  /** Estrategia de recomendación a utilizar */\n  strategy?: RecommendationQueryRequestStrategy;\n  /**\n   * ID del usuario para el que se generan recomendaciones\n   */\n  user_id: number;\n}\n\n/**\n * Estrategia de recomendación a utilizar\n */\nexport type RecommendationQueryExternalRequestStrategy = string | null;\n\n/**\n * Configuración de ordenamiento\n */\nexport type RecommendationQueryExternalRequestSortBy = SortConfig | null;\n\n/**\n * Filtros estructurados para aplicar a las recomendaciones\n */\nexport type RecommendationQueryExternalRequestFilters = FilterGroup | null;\n\n/**\n * Contexto explícito para filtrar o re-rankear recomendaciones\n */\nexport type RecommendationQueryExternalRequestContext =\n  RecommendationContext | null;\n\n/**\n * Esquema para solicitudes de recomendaciones usando external_user_id.\nPermite a los clientes usar sus propios identificadores de usuario.\n */\nexport interface RecommendationQueryExternalRequest {\n  /** Contexto explícito para filtrar o re-rankear recomendaciones */\n  context?: RecommendationQueryExternalRequestContext;\n  /** ID externo del usuario proporcionado por el cliente */\n  external_user_id: string;\n  /** Filtros estructurados para aplicar a las recomendaciones */\n  filters?: RecommendationQueryExternalRequestFilters;\n  /** Incluir explicación de la recomendación */\n  include_explanation?: boolean;\n  /**\n   * Número máximo de elementos a devolver\n   * @maximum 100\n   */\n  limit?: number;\n  /** Tipo de modelo a utilizar */\n  model_type?: string;\n  /**\n   * Número de elementos a saltar\n   * @minimum 0\n   */\n  skip?: number;\n  /** Configuración de ordenamiento */\n  sort_by?: RecommendationQueryExternalRequestSortBy;\n  /** Estrategia de recomendación a utilizar */\n  strategy?: RecommendationQueryExternalRequestStrategy;\n}\n\n/**\n * Specific model ID if filtered\n */\nexport type RecommendationPerformanceResponseModelId = number | null;\n\n/**\n * Complete recommendation performance response\n */\nexport interface RecommendationPerformanceResponse {\n  /** Specific model ID if filtered */\n  modelId?: RecommendationPerformanceResponseModelId;\n  /** Offline metrics */\n  offlineMetrics: OfflineMetrics;\n  /** Online metrics */\n  onlineMetrics: OnlineMetrics;\n}\n\n/**\n * Momento del día (mañana, tarde, noche)\n */\nexport type RecommendationContextTimeOfDay = string | null;\n\n/**\n * ID del producto de origen (ej: en página de detalle)\n */\nexport type RecommendationContextSourceItemId = number | null;\n\n/**\n * Consulta de búsqueda actual\n */\nexport type RecommendationContextSearchQuery = string | null;\n\n/**\n * IDs de productos vistos recientemente\n */\nexport type RecommendationContextRecentlyViewedIds = number[] | null;\n\n/**\n * Tipo de página donde se muestran las recomendaciones\n */\nexport type RecommendationContextPageType = PageType | null;\n\n/**\n * Ubicación geográfica del usuario\n */\nexport type RecommendationContextLocation = string | null;\n\n/**\n * Tipo de dispositivo desde donde se solicitan las recomendaciones\n */\nexport type RecommendationContextDevice = DeviceType | null;\n\nexport type RecommendationContextCustomAttributesAnyOf = {\n  [key: string]: unknown;\n};\n\n/**\n * Atributos personalizados adicionales\n */\nexport type RecommendationContextCustomAttributes =\n  RecommendationContextCustomAttributesAnyOf | null;\n\n/**\n * ID de la categoría actual\n */\nexport type RecommendationContextCategoryId = number | null;\n\n/**\n * IDs de productos en el carrito\n */\nexport type RecommendationContextCartItemIds = number[] | null;\n\n/**\n * Contexto explícito para recomendaciones personalizadas\n */\nexport interface RecommendationContext {\n  /** IDs de productos en el carrito */\n  cart_item_ids?: RecommendationContextCartItemIds;\n  /** ID de la categoría actual */\n  category_id?: RecommendationContextCategoryId;\n  /** Atributos personalizados adicionales */\n  custom_attributes?: RecommendationContextCustomAttributes;\n  /** Tipo de dispositivo desde donde se solicitan las recomendaciones */\n  device?: RecommendationContextDevice;\n  /** Ubicación geográfica del usuario */\n  location?: RecommendationContextLocation;\n  /** Tipo de página donde se muestran las recomendaciones */\n  page_type?: RecommendationContextPageType;\n  /** IDs de productos vistos recientemente */\n  recently_viewed_ids?: RecommendationContextRecentlyViewedIds;\n  /** Consulta de búsqueda actual */\n  search_query?: RecommendationContextSearchQuery;\n  /** ID del producto de origen (ej: en página de detalle) */\n  source_item_id?: RecommendationContextSourceItemId;\n  /** Momento del día (mañana, tarde, noche) */\n  time_of_day?: RecommendationContextTimeOfDay;\n}\n\nexport type ProductUpdatePrice = number | string | null;\n\nexport type ProductUpdateNumRatings = number | null;\n\nexport type ProductUpdateName = string | null;\n\nexport type ProductUpdateInventoryCount = number | null;\n\n/**\n * External identifier provided by the client (e.g., SKU, product code)\n */\nexport type ProductUpdateExternalId = string | null;\n\nexport type ProductUpdateDescription = string | null;\n\nexport type ProductUpdateCategory = string | null;\n\nexport type ProductUpdateAverageRating = number | null;\n\nexport interface ProductUpdate {\n  average_rating?: ProductUpdateAverageRating;\n  category?: ProductUpdateCategory;\n  description?: ProductUpdateDescription;\n  /** External identifier provided by the client (e.g., SKU, product code) */\n  external_id?: ProductUpdateExternalId;\n  inventory_count?: ProductUpdateInventoryCount;\n  name?: ProductUpdateName;\n  num_ratings?: ProductUpdateNumRatings;\n  price?: ProductUpdatePrice;\n}\n\nexport type ProductCreatePrice = number | string;\n\nexport type ProductCreateDescription = string | null;\n\nexport interface ProductCreate {\n  /**\n   * @minimum 0\n   * @maximum 5\n   */\n  averageRating?: number;\n  /**\n   * @minLength 1\n   * @maxLength 100\n   */\n  category: string;\n  description?: ProductCreateDescription;\n  /**\n   * External identifier provided by the client (e.g., SKU, product code)\n   * @minLength 1\n   * @maxLength 255\n   */\n  externalId: string;\n  /** @minimum 0 */\n  inventoryCount?: number;\n  /**\n   * @minLength 1\n   * @maxLength 255\n   */\n  name: string;\n  /** @minimum 0 */\n  numRatings?: number;\n  price: ProductCreatePrice;\n}\n\nexport type ProductSource = string | null;\n\nexport type ProductScore = number | null;\n\nexport type ProductExplanation = string | null;\n\nexport type ProductDescription = string | null;\n\nexport type ProductDeletedAt = string | null;\n\nexport interface Product {\n  accountId: number;\n  /**\n   * @minimum 0\n   * @maximum 5\n   */\n  averageRating?: number;\n  /**\n   * @minLength 1\n   * @maxLength 100\n   */\n  category: string;\n  createdAt: string;\n  deletedAt?: ProductDeletedAt;\n  description?: ProductDescription;\n  explanation?: ProductExplanation;\n  /**\n   * External identifier provided by the client (e.g., SKU, product code)\n   * @minLength 1\n   * @maxLength 255\n   */\n  externalId: string;\n  /** @minimum 0 */\n  inventoryCount?: number;\n  isActive?: boolean;\n  /**\n   * @minLength 1\n   * @maxLength 255\n   */\n  name: string;\n  /** @minimum 0 */\n  numRatings?: number;\n  price: string;\n  productId: number;\n  score?: ProductScore;\n  source?: ProductSource;\n  updatedAt: string;\n}\n\n/**\n * Plan limits information\n */\nexport interface PlanLimits {\n  /** API calls limit */\n  apiCalls?: number;\n  /** Concurrent requests limit */\n  maxConcurrentRequests?: number;\n  /** Maximum items */\n  maxItems?: number;\n  /** Formatted max items */\n  maxItemsFormatted?: string;\n  /** Rate limit per minute */\n  maxRequestsPerMinute?: number;\n  /** Maximum users */\n  maxUsers?: number;\n  /** Formatted max users */\n  maxUsersFormatted?: string;\n  /** Cache TTL in seconds */\n  recommendationCacheTtl?: number;\n  /** Storage limit in bytes */\n  storageBytes?: number;\n  /** Storage limit in GB */\n  storageGb?: number;\n  /** Storage limit in MB */\n  storageMb?: number;\n  /** Training data limit */\n  trainingDataLimit?: number;\n  /** Formatted training data limit */\n  trainingDataLimitFormatted?: string;\n  /** Training frequency */\n  trainingFrequency?: string;\n}\n\n/**\n * Plan features information\n */\nexport interface PlanFeatures {\n  /** Available models */\n  availableModels?: string[];\n}\n\nexport type PermissionType =\n  (typeof PermissionType)[keyof typeof PermissionType];\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const PermissionType = {\n  READ: \"READ\",\n  WRITE: \"WRITE\",\n  DELETE: \"DELETE\",\n  ADMIN: \"ADMIN\",\n  PRODUCT_READ: \"PRODUCT_READ\",\n  PRODUCT_CREATE: \"PRODUCT_CREATE\",\n  PRODUCT_UPDATE: \"PRODUCT_UPDATE\",\n  PRODUCT_DELETE: \"PRODUCT_DELETE\",\n  USER_READ: \"USER_READ\",\n  USER_CREATE: \"USER_CREATE\",\n  USER_UPDATE: \"USER_UPDATE\",\n  USER_DELETE: \"USER_DELETE\",\n  SYSTEM_USER_READ: \"SYSTEM_USER_READ\",\n  SYSTEM_USER_CREATE: \"SYSTEM_USER_CREATE\",\n  SYSTEM_USER_UPDATE: \"SYSTEM_USER_UPDATE\",\n  SYSTEM_USER_DELETE: \"SYSTEM_USER_DELETE\",\n  ROLE_READ: \"ROLE_READ\",\n  ROLE_CREATE: \"ROLE_CREATE\",\n  ROLE_UPDATE: \"ROLE_UPDATE\",\n  ROLE_DELETE: \"ROLE_DELETE\",\n  PERMISSION_ASSIGN: \"PERMISSION_ASSIGN\",\n  ANALYTICS_READ: \"ANALYTICS_READ\",\n  MODEL_READ: \"MODEL_READ\",\n  MODEL_CREATE: \"MODEL_CREATE\",\n  MODEL_UPDATE: \"MODEL_UPDATE\",\n  MODEL_DELETE: \"MODEL_DELETE\",\n  TRAINING_JOB_READ: \"TRAINING_JOB_READ\",\n  TRAINING_JOB_CREATE: \"TRAINING_JOB_CREATE\",\n  TRAINING_JOB_UPDATE: \"TRAINING_JOB_UPDATE\",\n  TRAINING_JOB_CANCEL: \"TRAINING_JOB_CANCEL\",\n} as const;\n\nexport interface PermissionCreate {\n  name: PermissionType;\n}\n\nexport interface Permission {\n  account_id: number;\n  id: number;\n  name: PermissionType;\n}\n\nexport interface PaginatedResponseSearch {\n  items: Search[];\n  limit: number;\n  skip: number;\n  total: number;\n}\n\nexport interface PaginatedResponseProduct {\n  items: Product[];\n  limit: number;\n  skip: number;\n  total: number;\n}\n\nexport interface PaginatedResponseInteraction {\n  items: Interaction[];\n  limit: number;\n  skip: number;\n  total: number;\n}\n\nexport interface PaginatedResponseEndUser {\n  items: EndUser[];\n  limit: number;\n  skip: number;\n  total: number;\n}\n\nexport interface PaginatedResponseCategory {\n  items: Category[];\n  limit: number;\n  skip: number;\n  total: number;\n}\n\n/**\n * Tipos de páginas donde se muestran recomendaciones\n */\nexport type PageType = (typeof PageType)[keyof typeof PageType];\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const PageType = {\n  home: \"home\",\n  product_detail: \"product_detail\",\n  category: \"category\",\n  search_results: \"search_results\",\n  cart: \"cart\",\n  checkout: \"checkout\",\n  user_profile: \"user_profile\",\n} as const;\n\n/**\n * Online metrics data\n */\nexport interface OnlineMetrics {\n  /** Bounce rate */\n  bounceRate?: number;\n  /** Click-through rate */\n  ctr?: number;\n  /** Conversion rate */\n  cvr?: number;\n  /** Average session duration */\n  sessionDuration?: number;\n  /** User engagement score */\n  userEngagement?: number;\n}\n\n/**\n * Offline metrics data\n */\nexport interface OfflineMetrics {\n  /** Overall catalog coverage */\n  catalogCoverage?: number;\n  /** Overall diversity */\n  diversity?: number;\n  /** Overall MAP */\n  map?: number;\n  /** Individual model metrics */\n  models?: ModelMetrics[];\n  /** Overall NDCG */\n  ndcg?: number;\n  /** Overall novelty */\n  novelty?: number;\n  /** Overall precision */\n  precision?: number;\n  /** Overall recall */\n  recall?: number;\n  /** Overall serendipity */\n  serendipity?: number;\n}\n\n/**\n * Nombre descriptivo de la API Key\n */\nexport type NewApiKeyResponseName = string | null;\n\n/**\n * Schema for a newly created API Key (includes the full key)\n */\nexport interface NewApiKeyResponse {\n  /** API Key completa (solo se muestra una vez) */\n  api_key: string;\n  /** Fecha de creación */\n  created_at: string;\n  /** ID único de la API Key */\n  id: number;\n  /** Mensaje informativo */\n  message: string;\n  /** Nombre descriptivo de la API Key */\n  name?: NewApiKeyResponseName;\n  /** Prefijo de la API Key */\n  prefix: string;\n}\n\n/**\n * Individual model metrics\n */\nexport interface ModelMetrics {\n  /** Catalog coverage */\n  catalogCoverage?: number;\n  /** Diversity metric */\n  diversity?: number;\n  /** MAP score */\n  map?: number;\n  /** Model ID */\n  modelId: number;\n  /** Model type (collaborative, content, hybrid) */\n  modelType: string;\n  /** NDCG metric */\n  ndcg?: number;\n  /** Novelty metric */\n  novelty?: number;\n  /** Precision metric */\n  precision?: number;\n  /** Recall metric */\n  recall?: number;\n  /** Serendipity metric */\n  serendipity?: number;\n  /** Model version */\n  version?: string;\n}\n\nexport type ModelMetadataResponsePerformanceMetrics = {\n  [key: string]: unknown;\n};\n\nexport type ModelMetadataResponseParameters = { [key: string]: unknown };\n\nexport type ModelMetadataResponseDescription = string | null;\n\nexport interface ModelMetadataResponse {\n  artifact_name: string;\n  artifact_version: string;\n  description?: ModelMetadataResponseDescription;\n  id: number;\n  parameters: ModelMetadataResponseParameters;\n  performance_metrics: ModelMetadataResponsePerformanceMetrics;\n  training_date: string;\n}\n\nexport type ModelInfoDescription = string | null;\n\n/**\n * Información básica de un modelo\n */\nexport interface ModelInfo {\n  artifactName: string;\n  artifactVersion: string;\n  description?: ModelInfoDescription;\n  id: number;\n  trainingDate: string;\n}\n\n/**\n * Improvement suggestions\n */\nexport type ModelComparisonResponseImprovements = { [key: string]: string };\n\n/**\n * Comparison matrix between models\n */\nexport type ModelComparisonResponseComparisonMatrix = {\n  [key: string]: { [key: string]: number };\n};\n\n/**\n * Model comparison response\n */\nexport interface ModelComparisonResponse {\n  /** Best performing model */\n  bestModel?: ModelComparisonResponseBestModel;\n  /** Comparison matrix between models */\n  comparisonMatrix?: ModelComparisonResponseComparisonMatrix;\n  /** Improvement suggestions */\n  improvements?: ModelComparisonResponseImprovements;\n  /** Models being compared */\n  models?: ModelComparisonMetrics[];\n}\n\n/**\n * All metrics\n */\nexport type ModelComparisonMetricsMetrics = { [key: string]: number };\n\n/**\n * Metrics for model comparison\n */\nexport interface ModelComparisonMetrics {\n  /** Creation date */\n  createdAt: string;\n  /** All metrics */\n  metrics?: ModelComparisonMetricsMetrics;\n  /** Model ID */\n  modelId: number;\n  /** Model name */\n  modelName: string;\n  /** Overall performance score */\n  performanceScore?: number;\n}\n\n/**\n * Best performing model\n */\nexport type ModelComparisonResponseBestModel = ModelComparisonMetrics | null;\n\n/**\n * Associated model ID\n */\nexport type MetricsHistoryPointModelId = number | null;\n\n/**\n * Single point in metrics history\n */\nexport interface MetricsHistoryPoint {\n  /** Associated model ID */\n  modelId?: MetricsHistoryPointModelId;\n  /** Timestamp */\n  timestamp: string;\n  /** Metric value */\n  value: number;\n}\n\n/**\n * Metrics history response\n */\nexport interface MetricsHistoryResponse {\n  /** Average value over the period */\n  averageValue?: number;\n  /** Historical data points */\n  dataPoints?: MetricsHistoryPoint[];\n  /** Name of the metric */\n  metricName: string;\n  /** Trend direction (improving, declining, stable) */\n  trend?: string;\n}\n\n/**\n * Login response schema\n */\nexport interface LoginResponse {\n  /** JWT access token */\n  accessToken: string;\n  /** Account ID */\n  accountId: number;\n  /** Whether user is admin */\n  isAdmin: boolean;\n  /** Token type (bearer) */\n  tokenType: string;\n}\n\n/**\n * Schema para el login de usuarios con JSON payload.\n */\nexport interface LoginRequest {\n  /** Email del usuario */\n  email: string;\n  /**\n   * Contraseña del usuario\n   * @minLength 1\n   */\n  password: string;\n}\n\n/**\n * Operadores lógicos para combinar filtros\n */\nexport type LogicalOperator =\n  (typeof LogicalOperator)[keyof typeof LogicalOperator];\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const LogicalOperator = {\n  and: \"and\",\n  or: \"or\",\n} as const;\n\nexport interface InventoryUpdate {\n  /**\n   * Cantidad de inventario disponible\n   * @minimum 0\n   */\n  inventory_count: number;\n}\n\nexport type InteractionType =\n  (typeof InteractionType)[keyof typeof InteractionType];\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const InteractionType = {\n  VIEW: \"VIEW\",\n  LIKE: \"LIKE\",\n  PURCHASE: \"PURCHASE\",\n  CART: \"CART\",\n  RATING: \"RATING\",\n  WISHLIST: \"WISHLIST\",\n  CLICK: \"CLICK\",\n  SEARCH: \"SEARCH\",\n  FAVORITE: \"FAVORITE\",\n} as const;\n\nexport type InteractionExternalCreateValue = number | string;\n\n/**\n * Schema for creating interactions using external IDs.\n */\nexport interface InteractionExternalCreate {\n  /** External product identifier provided by the client */\n  external_product_id: string;\n  /** External user identifier provided by the client */\n  external_user_id: string;\n  interaction_type: InteractionType;\n  recommendation_metadata?: InteractionExternalCreateRecommendationMetadata;\n  value: InteractionExternalCreateValue;\n}\n\nexport type InteractionExternalCreateRecommendationMetadataAnyOf = {\n  [key: string]: unknown;\n};\n\nexport type InteractionExternalCreateRecommendationMetadata =\n  InteractionExternalCreateRecommendationMetadataAnyOf | null;\n\nexport type InteractionCreateValue = number | string;\n\nexport interface InteractionCreate {\n  interactionType: InteractionType;\n  productId: number;\n  recommendationMetadata?: InteractionCreateRecommendationMetadata;\n  userId: number;\n  value: InteractionCreateValue;\n}\n\nexport type InteractionCreateRecommendationMetadataAnyOf = {\n  [key: string]: unknown;\n};\n\nexport type InteractionCreateRecommendationMetadata =\n  InteractionCreateRecommendationMetadataAnyOf | null;\n\nexport type InteractionRecommendationMetadataAnyOf = { [key: string]: unknown };\n\nexport type InteractionRecommendationMetadata =\n  InteractionRecommendationMetadataAnyOf | null;\n\nexport interface Interaction {\n  accountId: number;\n  id: number;\n  interactionType: InteractionType;\n  productId: number;\n  recommendationMetadata?: InteractionRecommendationMetadata;\n  timestamp: string;\n  userId: number;\n  value: string;\n}\n\nexport interface HTTPValidationError {\n  detail?: ValidationError[];\n}\n\n/**\n * Operadores para filtros estructurados\n */\nexport type FilterOperator =\n  (typeof FilterOperator)[keyof typeof FilterOperator];\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const FilterOperator = {\n  eq: \"eq\",\n  ne: \"ne\",\n  gt: \"gt\",\n  gte: \"gte\",\n  lt: \"lt\",\n  lte: \"lte\",\n  in: \"in\",\n  nin: \"nin\",\n  contains: \"contains\",\n  startswith: \"startswith\",\n  endswith: \"endswith\",\n} as const;\n\n/**\n * Filtro para recomendaciones\n */\nexport interface Filter {\n  /** Campo a filtrar */\n  field: string;\n  /** Operador de filtrado */\n  operator: FilterOperator;\n  /** Valor a comparar */\n  value: unknown;\n}\n\nexport type FilterGroupFiltersItem = Filter | FilterGroup;\n\n/**\n * Grupo de filtros con lógica AND/OR\n */\nexport interface FilterGroup {\n  /** Lista de filtros o grupos de filtros */\n  filters: FilterGroupFiltersItem[];\n  /** Lógica de combinación (and/or) */\n  logic?: LogicalOperator;\n}\n\n/**\n * Razones para recomendar un producto\n */\nexport type ExplanationReason =\n  (typeof ExplanationReason)[keyof typeof ExplanationReason];\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const ExplanationReason = {\n  similar_users: \"similar_users\",\n  similar_items: \"similar_items\",\n  category_affinity: \"category_affinity\",\n  popular_item: \"popular_item\",\n  trending_item: \"trending_item\",\n  attribute_match: \"attribute_match\",\n  complementary_item: \"complementary_item\",\n  recent_interaction: \"recent_interaction\",\n  personalized_ranking: \"personalized_ranking\",\n  new_item: \"new_item\",\n  diversity: \"diversity\",\n  seasonal: \"seasonal\",\n  promotional: \"promotional\",\n} as const;\n\n/**\n * Niveles de detalle para explicaciones de recomendaciones\n */\nexport type ExplanationLevel =\n  (typeof ExplanationLevel)[keyof typeof ExplanationLevel];\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const ExplanationLevel = {\n  simple: \"simple\",\n  detailed: \"detailed\",\n} as const;\n\n/**\n * ID del elemento (si aplica)\n */\nexport type ExplanationEvidenceId = number | null;\n\n/**\n * Evidencia que respalda una explicación\n */\nexport interface ExplanationEvidence {\n  /** ID del elemento (si aplica) */\n  id?: ExplanationEvidenceId;\n  /** Nombre o descripción del elemento */\n  name: string;\n  /**\n   * Relevancia de esta evidencia (0-1)\n   * @minimum 0\n   * @maximum 1\n   */\n  relevance: number;\n  /** Tipo de evidencia (producto, categoría, atributo, etc.) */\n  type: string;\n}\n\n/**\n * Minimum price preference for recommendations\n */\nexport type EndUserCreatePriceRangeMin = number | string | null;\n\n/**\n * Maximum price preference for recommendations\n */\nexport type EndUserCreatePriceRangeMax = number | string | null;\n\n/**\n * List of preferred product categories for initial recommendations\n */\nexport type EndUserCreatePreferredCategories = string[] | null;\n\n/**\n * List of preferred brands for initial recommendations\n */\nexport type EndUserCreatePreferredBrands = string[] | null;\n\nexport type EndUserCreateOnboardingPreferencesAnyOf = {\n  [key: string]: unknown;\n};\n\n/**\n * Additional preferences collected during onboarding\n */\nexport type EndUserCreateOnboardingPreferences =\n  EndUserCreateOnboardingPreferencesAnyOf | null;\n\n/**\n * List of categories to avoid in recommendations\n */\nexport type EndUserCreateDislikedCategories = string[] | null;\n\n/**\n * List of brands to avoid in recommendations\n */\nexport type EndUserCreateDislikedBrands = string[] | null;\n\nexport interface EndUserCreate {\n  /** Demographic information (age_group, gender, location, etc.) */\n  demographicInfo?: EndUserCreateDemographicInfo;\n  /** List of brands to avoid in recommendations */\n  dislikedBrands?: EndUserCreateDislikedBrands;\n  /** List of categories to avoid in recommendations */\n  dislikedCategories?: EndUserCreateDislikedCategories;\n  externalId: string;\n  /** Additional preferences collected during onboarding */\n  onboardingPreferences?: EndUserCreateOnboardingPreferences;\n  /** List of preferred brands for initial recommendations */\n  preferredBrands?: EndUserCreatePreferredBrands;\n  /** List of preferred product categories for initial recommendations */\n  preferredCategories?: EndUserCreatePreferredCategories;\n  /** Maximum price preference for recommendations */\n  priceRangeMax?: EndUserCreatePriceRangeMax;\n  /** Minimum price preference for recommendations */\n  priceRangeMin?: EndUserCreatePriceRangeMin;\n}\n\nexport type EndUserCreateDemographicInfoAnyOf = { [key: string]: unknown };\n\n/**\n * Demographic information (age_group, gender, location, etc.)\n */\nexport type EndUserCreateDemographicInfo =\n  EndUserCreateDemographicInfoAnyOf | null;\n\nexport type EndUserPriceRangeMin = string | null;\n\nexport type EndUserPriceRangeMax = string | null;\n\nexport type EndUserPreferredCategories = string[] | null;\n\nexport type EndUserPreferredBrands = string[] | null;\n\nexport interface EndUser {\n  accountId: number;\n  createdAt: string;\n  deletedAt?: EndUserDeletedAt;\n  demographicInfo?: EndUserDemographicInfo;\n  dislikedBrands?: EndUserDislikedBrands;\n  dislikedCategories?: EndUserDislikedCategories;\n  externalId: string;\n  isActive: boolean;\n  onboardingPreferences?: EndUserOnboardingPreferences;\n  preferredBrands?: EndUserPreferredBrands;\n  preferredCategories?: EndUserPreferredCategories;\n  priceRangeMax?: EndUserPriceRangeMax;\n  priceRangeMin?: EndUserPriceRangeMin;\n  updatedAt: string;\n  userId: number;\n}\n\nexport type EndUserOnboardingPreferencesAnyOf = { [key: string]: unknown };\n\nexport type EndUserOnboardingPreferences =\n  EndUserOnboardingPreferencesAnyOf | null;\n\nexport type EndUserDislikedCategories = string[] | null;\n\nexport type EndUserDislikedBrands = string[] | null;\n\nexport type EndUserDemographicInfoAnyOf = { [key: string]: unknown };\n\nexport type EndUserDemographicInfo = EndUserDemographicInfoAnyOf | null;\n\nexport type EndUserDeletedAt = string | null;\n\n/**\n * Tipos de dispositivos desde donde se solicitan recomendaciones\n */\nexport type DeviceType = (typeof DeviceType)[keyof typeof DeviceType];\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const DeviceType = {\n  desktop: \"desktop\",\n  mobile: \"mobile\",\n  tablet: \"tablet\",\n  tv: \"tv\",\n  other: \"other\",\n} as const;\n\n/**\n * Explicación detallada de una recomendación\n */\nexport interface DetailedExplanation {\n  /**\n   * Confianza en la recomendación (0-1)\n   * @minimum 0\n   * @maximum 1\n   */\n  confidence: number;\n  /** Evidencia que respalda la explicación */\n  evidence?: ExplanationEvidence[];\n  /** Razón principal de la recomendación */\n  primary_reason: ExplanationReason;\n  /** Razones secundarias */\n  secondary_reasons?: ExplanationReason[];\n  /** Fuente de la recomendación (collaborative, content, hybrid) */\n  source: string;\n  /** Explicación en texto plano */\n  text_explanation: string;\n}\n\n/**\n * Schema para la respuesta de creación de sesión del portal de facturación.\n */\nexport interface CreatePortalSessionResponse {\n  /** URL de la sesión del portal de facturación */\n  url: string;\n}\n\n/**\n * URL de retorno después de usar el portal\n */\nexport type CreatePortalSessionRequestReturnUrl = string | null;\n\n/**\n * Pasarela de pago a utilizar (mercadopago)\n */\nexport type CreatePortalSessionRequestPaymentGateway = string | null;\n\n/**\n * Schema para crear una sesión del portal de facturación.\n */\nexport interface CreatePortalSessionRequest {\n  /** Pasarela de pago a utilizar (mercadopago) */\n  payment_gateway?: CreatePortalSessionRequestPaymentGateway;\n  /** URL de retorno después de usar el portal */\n  return_url?: CreatePortalSessionRequestReturnUrl;\n}\n\n/**\n * ID de la sesión de checkout (solo para Stripe)\n */\nexport type CreateCheckoutSessionResponseSessionId = string | null;\n\n/**\n * Schema para la respuesta de creación de sesión de checkout.\n */\nexport interface CreateCheckoutSessionResponse {\n  /** ID de la sesión de checkout (solo para Stripe) */\n  session_id?: CreateCheckoutSessionResponseSessionId;\n  /** URL de la sesión de checkout */\n  url: string;\n}\n\n/**\n * URL de redirección en caso de éxito\n */\nexport type CreateCheckoutSessionRequestSuccessUrl = string | null;\n\n/**\n * Pasarela de pago a utilizar (mercadopago)\n */\nexport type CreateCheckoutSessionRequestPaymentGateway = string | null;\n\n/**\n * URL de redirección en caso de cancelación\n */\nexport type CreateCheckoutSessionRequestCancelUrl = string | null;\n\n/**\n * Schema para crear una sesión de checkout.\n */\nexport interface CreateCheckoutSessionRequest {\n  /** URL de redirección en caso de cancelación */\n  cancel_url?: CreateCheckoutSessionRequestCancelUrl;\n  /** Pasarela de pago a utilizar (mercadopago) */\n  payment_gateway?: CreateCheckoutSessionRequestPaymentGateway;\n  /** Price ID (Mercado Pago) */\n  price_id: string;\n  /** URL de redirección en caso de éxito */\n  success_url?: CreateCheckoutSessionRequestSuccessUrl;\n}\n\nexport type CategoryRelevance = number | null;\n\n/**\n * Esquema para representar una categoría de productos.\n */\nexport interface Category {\n  id: number;\n  name: string;\n  relevance?: CategoryRelevance;\n}\n\nexport type BatchIngestionResponseTaskId = string | null;\n\n/**\n * Esquema para la respuesta de la carga masiva\n */\nexport interface BatchIngestionResponse {\n  jobId: number;\n  message: string;\n  status: string;\n  taskId?: BatchIngestionResponseTaskId;\n  totalInteractions: number;\n  totalProducts: number;\n  totalUsers: number;\n}\n\n/**\n * Lista de usuarios a cargar\n */\nexport type BatchIngestionRequestUsers = EndUserCreate[] | null;\n\n/**\n * Lista de productos a cargar\n */\nexport type BatchIngestionRequestProducts = ProductCreate[] | null;\n\nexport type BatchIngestionRequestInteractionsAnyOfItem =\n  | InteractionCreate\n  | InteractionExternalCreate;\n\n/**\n * Lista de interacciones a cargar (pueden usar IDs internos o externos)\n */\nexport type BatchIngestionRequestInteractions =\n  | BatchIngestionRequestInteractionsAnyOfItem[]\n  | null;\n\n/**\n * Esquema para la carga masiva de datos\n */\nexport interface BatchIngestionRequest {\n  /** Lista de interacciones a cargar (pueden usar IDs internos o externos) */\n  interactions?: BatchIngestionRequestInteractions;\n  /** Lista de productos a cargar */\n  products?: BatchIngestionRequestProducts;\n  /** Lista de usuarios a cargar */\n  users?: BatchIngestionRequestUsers;\n}\n\nexport type BatchIngestionJobStatusTaskId = string | null;\n\nexport type BatchIngestionJobStatusSuccessRate = number | null;\n\nexport type BatchIngestionJobStatusStartedAt = string | null;\n\nexport type BatchIngestionJobStatusProgressPercentage = number | null;\n\nexport type BatchIngestionJobStatusProcessedCountAnyOf = {\n  [key: string]: unknown;\n};\n\nexport type BatchIngestionJobStatusProcessedCount =\n  BatchIngestionJobStatusProcessedCountAnyOf | null;\n\nexport type BatchIngestionJobStatusParametersAnyOf = { [key: string]: unknown };\n\nexport type BatchIngestionJobStatusParameters =\n  BatchIngestionJobStatusParametersAnyOf | null;\n\nexport type BatchIngestionJobStatusEstimatedRemainingTime = number | null;\n\nexport type BatchIngestionJobStatusErrorMessage = string | null;\n\n/**\n * Esquema para consultar el estado de un trabajo de ingesta masiva.\n */\nexport interface BatchIngestionJobStatus {\n  completedAt?: BatchIngestionJobStatusCompletedAt;\n  createdAt: string;\n  errorCount?: BatchIngestionJobStatusErrorCount;\n  errorDetails?: BatchIngestionJobStatusErrorDetails;\n  errorMessage?: BatchIngestionJobStatusErrorMessage;\n  estimatedRemainingTime?: BatchIngestionJobStatusEstimatedRemainingTime;\n  jobId: number;\n  parameters?: BatchIngestionJobStatusParameters;\n  processedCount?: BatchIngestionJobStatusProcessedCount;\n  progressPercentage?: BatchIngestionJobStatusProgressPercentage;\n  startedAt?: BatchIngestionJobStatusStartedAt;\n  status: string;\n  successRate?: BatchIngestionJobStatusSuccessRate;\n  taskId?: BatchIngestionJobStatusTaskId;\n}\n\nexport type BatchIngestionJobStatusErrorDetailsAnyOf = {\n  [key: string]: unknown;\n};\n\nexport type BatchIngestionJobStatusErrorDetails =\n  BatchIngestionJobStatusErrorDetailsAnyOf | null;\n\nexport type BatchIngestionJobStatusErrorCount = number | null;\n\nexport type BatchIngestionJobStatusCompletedAt = string | null;\n\nexport type AuditLogSystemUserId = number | null;\n\nexport type AuditLogEntityId = string | number;\n\nexport type AuditLogDetails = string | null;\n\nexport type AuditLogChangesAnyOf = { [key: string]: unknown };\n\nexport type AuditLogChanges = AuditLogChangesAnyOf | null;\n\nexport interface AuditLog {\n  account_id: number;\n  /** @maxLength 50 */\n  action: string;\n  changes?: AuditLogChanges;\n  created_at: string;\n  details?: AuditLogDetails;\n  entity_id: AuditLogEntityId;\n  /** @maxLength 50 */\n  entity_type: string;\n  id: number;\n  performed_by: string;\n  system_user_id?: AuditLogSystemUserId;\n}\n\n/**\n * Nuevo nombre descriptivo para esta API Key\n */\nexport type ApiKeyUpdateName = string | null;\n\n/**\n * Schema for updating an API Key\n */\nexport interface ApiKeyUpdate {\n  /** Nuevo nombre descriptivo para esta API Key */\n  name?: ApiKeyUpdateName;\n}\n\n/**\n * Prefijo de la API Key\n */\nexport type ApiKeyResponsePrefix = string | null;\n\n/**\n * Nombre descriptivo para esta API Key\n */\nexport type ApiKeyResponseName = string | null;\n\n/**\n * Último uso de la API Key\n */\nexport type ApiKeyResponseLastUsed = string | null;\n\n/**\n * Últimos caracteres de la API Key\n */\nexport type ApiKeyResponseLastChars = string | null;\n\n/**\n * Fecha de creación\n */\nexport type ApiKeyResponseCreatedAt = string | null;\n\n/**\n * Schema for API Key information (no sensitive data)\n */\nexport interface ApiKeyResponse {\n  /** Fecha de creación */\n  created_at?: ApiKeyResponseCreatedAt;\n  /** ID único de la API Key */\n  id: number;\n  /** Estado de la API Key */\n  is_active?: boolean;\n  /** Últimos caracteres de la API Key */\n  last_chars?: ApiKeyResponseLastChars;\n  /** Último uso de la API Key */\n  last_used?: ApiKeyResponseLastUsed;\n  /** Nombre descriptivo para esta API Key */\n  name?: ApiKeyResponseName;\n  /** Prefijo de la API Key */\n  prefix?: ApiKeyResponsePrefix;\n}\n\n/**\n * Schema for listing API Keys\n */\nexport interface ApiKeyListResponse {\n  /** Lista de API Keys */\n  api_keys: ApiKeyResponse[];\n  /** Número total de API Keys */\n  total: number;\n}\n\n/**\n * Nombre descriptivo para esta API Key\n */\nexport type ApiKeyCreateName = string | null;\n\n/**\n * Schema for creating a new API Key\n */\nexport interface ApiKeyCreate {\n  /** Nombre descriptivo para esta API Key */\n  name?: ApiKeyCreateName;\n}\n\n/**\n * Date when usage resets\n */\nexport type ApiCallsUsageResetDate = string | null;\n\n/**\n * API calls usage information\n */\nexport interface ApiCallsUsage {\n  /** API calls limit */\n  limit?: number;\n  /** Percentage of limit used */\n  percentage?: number;\n  /** Date when usage resets */\n  resetDate?: ApiCallsUsageResetDate;\n  /** Number of API calls used */\n  used?: number;\n}\n\nexport type AccountUpdateOnboardingChecklistStatusAnyOf = {\n  [key: string]: unknown;\n};\n\nexport type AccountUpdateOnboardingChecklistStatus =\n  AccountUpdateOnboardingChecklistStatusAnyOf | null;\n\nexport type AccountUpdateName = string | null;\n\nexport type AccountUpdateMercadopagoCustomerId = string | null;\n\nexport type AccountUpdateIsActive = boolean | null;\n\nexport interface AccountUpdate {\n  isActive?: AccountUpdateIsActive;\n  mercadopagoCustomerId?: AccountUpdateMercadopagoCustomerId;\n  name?: AccountUpdateName;\n  onboardingChecklistStatus?: AccountUpdateOnboardingChecklistStatus;\n}\n\nexport type AccountResponseSubscription = SubscriptionBasicInfo | null;\n\nexport type AccountResponseOnboardingChecklistStatusAnyOf = {\n  [key: string]: unknown;\n};\n\nexport type AccountResponseOnboardingChecklistStatus =\n  AccountResponseOnboardingChecklistStatusAnyOf | null;\n\nexport type AccountResponseMercadopagoCustomerId = string | null;\n\nexport type AccountResponseDeletedAt = string | null;\n\nexport interface AccountResponse {\n  accountId: number;\n  createdAt: string;\n  deletedAt?: AccountResponseDeletedAt;\n  isActive: boolean;\n  mercadopagoCustomerId?: AccountResponseMercadopagoCustomerId;\n  name: string;\n  onboardingChecklistStatus?: AccountResponseOnboardingChecklistStatus;\n  subscription?: AccountResponseSubscription;\n  updatedAt: string;\n}\n\nexport type AccountCreateMercadopagoCustomerId = string | null;\n\nexport interface AccountCreate {\n  isActive?: boolean;\n  mercadopagoCustomerId?: AccountCreateMercadopagoCustomerId;\n  /** @minLength 1 */\n  name: string;\n}\n\nexport const getRayuela = () => {\n  /**\n   * @summary Health Check\n   */\n  const healthCheckHealthGet = <TData = AxiosResponse<unknown>>(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/health`, options);\n  };\n\n  /**\n * Endpoint para verificar el estado de la API.\nNo requiere autenticación.\n * @summary Health Check\n */\n  const healthCheckApiV1HealthGet = <\n    TData = AxiosResponse<HealthCheckApiV1HealthGet200>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/health`, options);\n  };\n\n  /**\n * Endpoint para verificar la conexión a la base de datos.\nNo requiere autenticación.\n * @summary Db Health Check\n */\n  const dbHealthCheckApiV1HealthDbGet = <\n    TData = AxiosResponse<DbHealthCheckApiV1HealthDbGet200>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/health/db`, options);\n  };\n\n  /**\n * Endpoint para verificar la autenticación.\nRequiere autenticación con API Key.\n * @summary Auth Health Check\n */\n  const authHealthCheckApiV1HealthAuthGet = <\n    TData = AxiosResponse<AuthHealthCheckApiV1HealthAuthGet200>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/health/auth`, options);\n  };\n\n  /**\n   * Envía un email de verificación al usuario actual.\n   * @summary Send Verification Email\n   */\n  const sendVerificationEmailApiV1AuthSendVerificationEmailPost = <\n    TData = AxiosResponse<unknown>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(\n      `/api/v1/auth/send-verification-email`,\n      undefined,\n      options,\n    );\n  };\n\n  /**\n   * Verifica el email de un usuario usando un token.\n   * @summary Verify Email\n   */\n  const verifyEmailApiV1AuthVerifyEmailGet = <TData = AxiosResponse<unknown>>(\n    params: VerifyEmailApiV1AuthVerifyEmailGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/auth/verify-email`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n   * Creates a new account with global email uniqueness validation and returns a JWT token and the first API Key\n   * @summary Register a new account\n   */\n  const registerApiV1AuthRegisterPost = <\n    TData = AxiosResponse<RegisterResponse>,\n  >(\n    registerRequest: RegisterRequest,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(`/api/v1/auth/register`, registerRequest, options);\n  };\n\n  /**\n   * Authenticates a user and returns a JWT token for dashboard access. Accepts JSON payload with email and password.\n   * @summary Login to obtain JWT token\n   */\n  const loginApiV1AuthTokenPost = <TData = AxiosResponse<LoginResponse>>(\n    loginRequest: LoginRequest,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(`/api/v1/auth/token`, loginRequest, options);\n  };\n\n  /**\n   * Revoca el token JWT actual.\n   * @summary Logout\n   */\n  const logoutApiV1AuthLogoutPost = <TData = AxiosResponse<unknown>>(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(`/api/v1/auth/logout`, undefined, options);\n  };\n\n  /**\n   * List all accounts.\n   * @summary List Accounts\n   */\n  const listAccountsApiV1AccountsGet = <\n    TData = AxiosResponse<AccountResponse[]>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/accounts/`, options);\n  };\n\n  /**\n * Crea una nueva cuenta.\n\nUtiliza la columna Identity para generar automáticamente el ID de la cuenta.\n * @summary Create Account\n */\n  const createAccountApiV1AccountsAccountsPost = <\n    TData = AxiosResponse<AccountResponse>,\n  >(\n    accountCreate: AccountCreate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(`/api/v1/accounts/accounts`, accountCreate, options);\n  };\n\n  /**\n   * Get account by ID.\n   * @summary Get Account\n   */\n  const getAccountApiV1AccountsAccountIdGet = <\n    TData = AxiosResponse<AccountResponse>,\n  >(\n    accountId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/accounts/${accountId}`, options);\n  };\n\n  /**\n   * @summary Deactivate Account\n   */\n  const deactivateAccountApiV1AccountsAccountIdDeactivatePatch = <\n    TData = AxiosResponse<void>,\n  >(\n    accountId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.patch(\n      `/api/v1/accounts/${accountId}/deactivate`,\n      undefined,\n      options,\n    );\n  };\n\n  /**\n   * Activate an account.\n   * @summary Activate Account\n   */\n  const activateAccountApiV1AccountsAccountIdActivatePatch = <\n    TData = AxiosResponse<AccountResponse>,\n  >(\n    accountId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.patch(\n      `/api/v1/accounts/${accountId}/activate`,\n      undefined,\n      options,\n    );\n  };\n\n  /**\n   * Get information about the current account.\n   * @summary Get Account Info\n   */\n  const getAccountInfoApiV1AccountsCurrentGet = <\n    TData = AxiosResponse<AccountResponse>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/accounts/current`, options);\n  };\n\n  /**\n * Update the current account information.\n\nThis endpoint allows administrators to update their own account details,\nsuch as the account name.\n * @summary Update Current Account\n */\n  const updateCurrentAccountApiV1AccountsCurrentPut = <\n    TData = AxiosResponse<AccountResponse>,\n  >(\n    accountUpdate: AccountUpdate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.put(`/api/v1/accounts/current`, accountUpdate, options);\n  };\n\n  /**\n * Partially update the current account information.\n\nThis endpoint allows administrators to update specific fields of their own account,\nsuch as the account name, without having to provide all fields.\n * @summary Patch Current Account\n */\n  const patchCurrentAccountApiV1AccountsCurrentPatch = <\n    TData = AxiosResponse<AccountResponse>,\n  >(\n    accountUpdate: AccountUpdate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.patch(`/api/v1/accounts/current`, accountUpdate, options);\n  };\n\n  /**\n   * Get audit logs with optional filters for a specific account.\n   * @summary Get Audit Logs\n   */\n  const getAuditLogsApiV1AccountsAccountIdAuditLogsGet = <\n    TData = AxiosResponse<AuditLog[]>,\n  >(\n    accountId: number,\n    params?: GetAuditLogsApiV1AccountsAccountIdAuditLogsGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/accounts/${accountId}/audit-logs`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n   * Get API usage statistics for the current account.\n   * @summary Get Api Usage\n   */\n  const getApiUsageApiV1AccountsUsageGet = <TData = AxiosResponse<UsageStats>>(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/accounts/usage`, options);\n  };\n\n  /**\n * Get all available subscription plans with their details.\n\nReturns:\n    Dict with plan information for all available plans.\n * @summary Get Available Plans\n */\n  const getAvailablePlansApiV1PlansGet = <\n    TData = AxiosResponse<GetAvailablePlansApiV1PlansGet200>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/plans/`, options);\n  };\n\n  /**\n * Get information about the current authenticated user.\n\nThis endpoint only requires a valid JWT token, no API Key is needed.\nIt's useful for the initial login flow when the user hasn't confirmed\nthe API Key yet.\n * @summary Get Current User Info\n */\n  const getCurrentUserInfoApiV1SystemUsersMeGet = <\n    TData = AxiosResponse<SystemUserResponse>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/system-users/me`, options);\n  };\n\n  /**\n   * Update current user's information.\n   * @summary Update User Me\n   */\n  const updateUserMeApiV1SystemUsersMePut = <\n    TData = AxiosResponse<SystemUserResponse>,\n  >(\n    systemUserUpdate: SystemUserUpdate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.put(`/api/v1/system-users/me`, systemUserUpdate, options);\n  };\n\n  /**\n   * Delete current user.\n   * @summary Delete User Me\n   */\n  const deleteUserMeApiV1SystemUsersMeDelete = <TData = AxiosResponse<void>>(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.delete(`/api/v1/system-users/me`, options);\n  };\n\n  /**\n   * Create a new system user.\n   * @summary Create System User\n   */\n  const createSystemUserApiV1SystemUsersPost = <\n    TData = AxiosResponse<SystemUserResponse>,\n  >(\n    systemUserCreate: SystemUserCreate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(`/api/v1/system-users/`, systemUserCreate, options);\n  };\n\n  /**\n   * Get a system user by ID.\n   * @summary Get System User\n   */\n  const getSystemUserApiV1SystemUsersUserIdGet = <\n    TData = AxiosResponse<SystemUserResponse>,\n  >(\n    userId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/system-users/${userId}`, options);\n  };\n\n  /**\n   * Create a new role.\n   * @summary Create Role\n   */\n  const createRoleApiV1SystemUsersRolesPost = <TData = AxiosResponse<Role>>(\n    roleCreate: RoleCreate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(`/api/v1/system-users/roles/`, roleCreate, options);\n  };\n\n  /**\n   * Assign a role to a system user.\n   * @summary Assign Role\n   */\n  const assignRoleApiV1SystemUsersUserIdRolesRoleIdPost = <\n    TData = AxiosResponse<unknown>,\n  >(\n    userId: number,\n    roleId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(\n      `/api/v1/system-users/${userId}/roles/${roleId}`,\n      undefined,\n      options,\n    );\n  };\n\n  /**\n   * Remove a role from a system user.\n   * @summary Remove Role\n   */\n  const removeRoleApiV1SystemUsersUserIdRolesRoleIdDelete = <\n    TData = AxiosResponse<void>,\n  >(\n    userId: number,\n    roleId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.delete(\n      `/api/v1/system-users/${userId}/roles/${roleId}`,\n      options,\n    );\n  };\n\n  /**\n   * Get all roles assigned to a system user.\n   * @summary Get User Roles\n   */\n  const getUserRolesApiV1SystemUsersUserIdRolesGet = <\n    TData = AxiosResponse<Role[]>,\n  >(\n    userId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/system-users/${userId}/roles`, options);\n  };\n\n  /**\n   * Get all permissions assigned to a system user through their roles.\n   * @summary Get User Permissions\n   */\n  const getUserPermissionsApiV1SystemUsersUserIdPermissionsGet = <\n    TData = AxiosResponse<string[]>,\n  >(\n    userId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/system-users/${userId}/permissions`, options);\n  };\n\n  /**\n   * @summary Create End User\n   */\n  const createEndUserApiV1EndUsersPost = <TData = AxiosResponse<EndUser>>(\n    endUserCreate: EndUserCreate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(`/api/v1/end-users/`, endUserCreate, options);\n  };\n\n  /**\n   * @summary Read End Users\n   */\n  const readEndUsersApiV1EndUsersGet = <\n    TData = AxiosResponse<PaginatedResponseEndUser>,\n  >(\n    params?: ReadEndUsersApiV1EndUsersGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/end-users/`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n   * Get an end user by ID.\n   * @summary Read End User\n   */\n  const readEndUserApiV1EndUsersUserIdGet = <TData = AxiosResponse<EndUser>>(\n    userId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/end-users/${userId}`, options);\n  };\n\n  /**\n   * @summary Create Product\n   */\n  const createProductApiV1ProductsPost = <TData = AxiosResponse<Product>>(\n    productCreate: ProductCreate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(`/api/v1/products/`, productCreate, options);\n  };\n\n  /**\n   * Obtener productos paginados para la cuenta actual\n   * @summary Read Products\n   */\n  const readProductsApiV1ProductsGet = <\n    TData = AxiosResponse<PaginatedResponseProduct>,\n  >(\n    params?: ReadProductsApiV1ProductsGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/products/`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n   * Get a product by ID.\n   * @summary Get Product\n   */\n  const getProductApiV1ProductsProductIdGet = <TData = AxiosResponse<Product>>(\n    productId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/products/${productId}`, options);\n  };\n\n  /**\n   * @summary Update Product\n   */\n  const updateProductApiV1ProductsProductIdPut = <\n    TData = AxiosResponse<Product>,\n  >(\n    productId: number,\n    productUpdate: ProductUpdate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.put(`/api/v1/products/${productId}`, productUpdate, options);\n  };\n\n  /**\n   * Update product inventory.\n   * @summary Update Inventory\n   */\n  const updateInventoryApiV1ProductsProductIdInventoryPatch = <\n    TData = AxiosResponse<Product>,\n  >(\n    productId: number,\n    inventoryUpdate: InventoryUpdate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.patch(\n      `/api/v1/products/${productId}/inventory`,\n      inventoryUpdate,\n      options,\n    );\n  };\n\n  /**\n   * Get a product by its external_id.\n   * @summary Get Product By External Id\n   */\n  const getProductByExternalIdApiV1ProductsExternalExternalIdGet = <\n    TData = AxiosResponse<Product>,\n  >(\n    externalId: string,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/products/external/${externalId}`, options);\n  };\n\n  /**\n   * Update a product by its external_id.\n   * @summary Update Product By External Id\n   */\n  const updateProductByExternalIdApiV1ProductsExternalExternalIdPut = <\n    TData = AxiosResponse<Product>,\n  >(\n    externalId: string,\n    productUpdate: ProductUpdate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.put(\n      `/api/v1/products/external/${externalId}`,\n      productUpdate,\n      options,\n    );\n  };\n\n  /**\n   * Delete (soft delete) a product by its external_id.\n   * @summary Delete Product By External Id\n   */\n  const deleteProductByExternalIdApiV1ProductsExternalExternalIdDelete = <\n    TData = AxiosResponse<void>,\n  >(\n    externalId: string,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.delete(`/api/v1/products/external/${externalId}`, options);\n  };\n\n  /**\n   * Update product inventory by external_id.\n   * @summary Update Inventory By External Id\n   */\n  const updateInventoryByExternalIdApiV1ProductsExternalExternalIdInventoryPatch =\n    <TData = AxiosResponse<Product>>(\n      externalId: string,\n      inventoryUpdate: InventoryUpdate,\n      options?: AxiosRequestConfig,\n    ): Promise<TData> => {\n      return axios.patch(\n        `/api/v1/products/external/${externalId}/inventory`,\n        inventoryUpdate,\n        options,\n      );\n    };\n\n  /**\n   * @summary Get Most Searched\n   */\n  const getMostSearchedApiV1RecommendationsMostSearchedGet = <\n    TData = AxiosResponse<PaginatedResponseProduct>,\n  >(\n    params?: GetMostSearchedApiV1RecommendationsMostSearchedGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/recommendations/most-searched/`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n   * @summary Get Trending Searches\n   */\n  const getTrendingSearchesApiV1RecommendationsTrendingSearchesGet = <\n    TData = AxiosResponse<PaginatedResponseSearch>,\n  >(\n    params?: GetTrendingSearchesApiV1RecommendationsTrendingSearchesGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/recommendations/trending-searches/`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n   * @summary Get Popular Trends\n   */\n  const getPopularTrendsApiV1RecommendationsPopularTrendsGet = <\n    TData = AxiosResponse<PaginatedResponseProduct>,\n  >(\n    params?: GetPopularTrendsApiV1RecommendationsPopularTrendsGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/recommendations/popular-trends/`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n   * @summary Get Related Searches\n   */\n  const getRelatedSearchesApiV1RecommendationsRelatedSearchesProductIdGet = <\n    TData = AxiosResponse<PaginatedResponseSearch>,\n  >(\n    productId: number,\n    params?: GetRelatedSearchesApiV1RecommendationsRelatedSearchesProductIdGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/recommendations/related-searches/${productId}`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n   * @summary Get Most Sold\n   */\n  const getMostSoldApiV1RecommendationsMostSoldGet = <\n    TData = AxiosResponse<PaginatedResponseProduct>,\n  >(\n    params?: GetMostSoldApiV1RecommendationsMostSoldGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/recommendations/most-sold/`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n   * @summary Get Top Rated\n   */\n  const getTopRatedApiV1RecommendationsTopRatedGet = <\n    TData = AxiosResponse<PaginatedResponseProduct>,\n  >(\n    params?: GetTopRatedApiV1RecommendationsTopRatedGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/recommendations/top-rated/`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n   * @summary Get Category Products\n   */\n  const getCategoryProductsApiV1RecommendationsCategoryCategoryGet = <\n    TData = AxiosResponse<PaginatedResponseProduct>,\n  >(\n    category: string,\n    params?: GetCategoryProductsApiV1RecommendationsCategoryCategoryGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/recommendations/category/${category}`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n   * @summary Get Related Categories\n   */\n  const getRelatedCategoriesApiV1RecommendationsRelatedCategoriesCategoryGet = <\n    TData = AxiosResponse<PaginatedResponseCategory>,\n  >(\n    category: string,\n    params?: GetRelatedCategoriesApiV1RecommendationsRelatedCategoriesCategoryGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/recommendations/related-categories/${category}`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n   * @summary Get Also Bought\n   */\n  const getAlsoBoughtApiV1RecommendationsAlsoBoughtProductIdGet = <\n    TData = AxiosResponse<PaginatedResponseProduct>,\n  >(\n    productId: number,\n    params?: GetAlsoBoughtApiV1RecommendationsAlsoBoughtProductIdGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/recommendations/also-bought/${productId}`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n * Obtiene productos similares al producto especificado basados en contenido.\n\nUtiliza vectores de características de productos para calcular similitud coseno\ny encontrar productos con características similares.\n\nParámetros:\n- product_id: ID del producto para el que se buscan similares\n- limit: Número máximo de productos similares a devolver\n- include_explanation: Si es True, incluye explicaciones de por qué los productos son similares\n- explanation_level: Nivel de detalle de la explicación (simple o detailed)\n\nRetorna:\n- Lista paginada de productos similares ordenados por similitud\n- Cada producto incluye un score de similitud\n- Si se solicita, incluye explicaciones de la similitud\n * @summary Get Similar Products\n */\n  const getSimilarProductsApiV1RecommendationsProductsProductIdSimilarGet = <\n    TData = AxiosResponse<PaginatedResponseProduct>,\n  >(\n    productId: number,\n    params?: GetSimilarProductsApiV1RecommendationsProductsProductIdSimilarGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/recommendations/products/${productId}/similar`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n * Invalida la caché de recomendaciones para un usuario específico.\n\nArgs:\n    user_id: ID del usuario\n    account: Cuenta actual\n    db: Sesión de base de datos\n\nReturns:\n    Mensaje de confirmación\n * @summary Invalidate User Cache\n */\n  const invalidateUserCacheApiV1RecommendationsInvalidateCacheUserIdPost = <\n    TData = AxiosResponse<unknown>,\n  >(\n    userId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(\n      `/api/v1/recommendations/invalidate-cache/${userId}`,\n      undefined,\n      options,\n    );\n  };\n\n  /**\n   * Invalida toda la caché de recomendaciones para una cuenta.\n   * @summary Invalidate Account Cache\n   */\n  const invalidateAccountCacheApiV1RecommendationsInvalidateCachePost = <\n    TData = AxiosResponse<unknown>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(\n      `/api/v1/recommendations/invalidate-cache`,\n      undefined,\n      options,\n    );\n  };\n\n  /**\n   * Main endpoint for obtaining personalized recommendations with complex filters and structured context.\n   * @summary Get personalized recommendations\n   */\n  const queryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPost =\n    <TData = AxiosResponse<PaginatedResponseProduct>>(\n      queryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPostBody: QueryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPostBody,\n      options?: AxiosRequestConfig,\n    ): Promise<TData> => {\n      return axios.post(\n        `/api/v1/recommendations/personalized/query`,\n        queryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPostBody,\n        options,\n      );\n    };\n\n  /**\n * Obtiene una explicación detallada de por qué un producto específico se recomienda a un usuario.\n\nEsta explicación incluye:\n- Razón principal de la recomendación\n- Razones secundarias\n- Nivel de confianza\n- Evidencia que respalda la recomendación (productos similares, categorías afines, etc.)\n- Explicación en texto plano\n\nArgs:\n    user_id: ID del usuario\n    item_id: ID del producto\n    account: Información de la cuenta autenticada\n    db: Sesión de base de datos\n    limit_service: Servicio de límites\n\nReturns:\n    Explicación detallada de la recomendación\n * @summary Get Recommendation Explanation\n */\n  const getRecommendationExplanationApiV1RecommendationsExplainUserIdItemIdGet =\n    <TData = AxiosResponse<DetailedExplanation>>(\n      userId: number,\n      itemId: number,\n      options?: AxiosRequestConfig,\n    ): Promise<TData> => {\n      return axios.get(\n        `/api/v1/recommendations/explain/${userId}/${itemId}`,\n        options,\n      );\n    };\n\n  /**\n   * Get explanation using external IDs by resolving to internal IDs.\n   * @summary Get explanation using external IDs\n   */\n  const getRecommendationExplanationExternalApiV1RecommendationsExplainExternalExternalUserIdExternalItemIdGet =\n    <TData = AxiosResponse<DetailedExplanation>>(\n      externalUserId: string,\n      externalItemId: string,\n      options?: AxiosRequestConfig,\n    ): Promise<TData> => {\n      return axios.get(\n        `/api/v1/recommendations/explain/external/${externalUserId}/${externalItemId}`,\n        options,\n      );\n    };\n\n  /**\n   * @summary Get similar products using external ID\n   */\n  const getSimilarProductsExternalApiV1RecommendationsProductsExternalExternalProductIdSimilarGet =\n    <TData = AxiosResponse<PaginatedResponseProduct>>(\n      externalProductId: string,\n      params?: GetSimilarProductsExternalApiV1RecommendationsProductsExternalExternalProductIdSimilarGetParams,\n      options?: AxiosRequestConfig,\n    ): Promise<TData> => {\n      return axios.get(\n        `/api/v1/recommendations/products/external/${externalProductId}/similar`,\n        {\n          ...options,\n          params: { ...params, ...options?.params },\n        },\n      );\n    };\n\n  /**\n   * @summary Get also-bought products using external ID\n   */\n  const getAlsoBoughtExternalApiV1RecommendationsAlsoBoughtExternalExternalProductIdGet =\n    <TData = AxiosResponse<PaginatedResponseProduct>>(\n      externalProductId: string,\n      params?: GetAlsoBoughtExternalApiV1RecommendationsAlsoBoughtExternalExternalProductIdGetParams,\n      options?: AxiosRequestConfig,\n    ): Promise<TData> => {\n      return axios.get(\n        `/api/v1/recommendations/also-bought/external/${externalProductId}`,\n        {\n          ...options,\n          params: { ...params, ...options?.params },\n        },\n      );\n    };\n\n  /**\n * Obtiene métricas de confianza para las recomendaciones.\n\nEstas métricas incluyen:\n- Distribución de scores de confianza por tipo de modelo (colaborativo, contenido, híbrido)\n- Confianza promedio por categoría de producto\n- Factores que influyen en la confianza\n- Tendencias de confianza a lo largo del tiempo\n\nReturns:\n    Diccionario con métricas de confianza\n * @summary Get Confidence Metrics\n */\n  const getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet = <\n    TData = AxiosResponse<GetConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet200>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/recommendations/confidence-metrics`, options);\n  };\n\n  /**\n   * @summary Rollback Model\n   */\n  const rollbackModelApiV1RecommendationsRollbackArtifactVersionPost = <\n    TData = AxiosResponse<unknown>,\n  >(\n    artifactVersion: string,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(\n      `/api/v1/recommendations/rollback/${artifactVersion}`,\n      undefined,\n      options,\n    );\n  };\n\n  /**\n * Get performance metrics for recommendation models.\n\nReturns metrics such as:\n- Precision/Recall/NDCG/MAP: Accuracy of recommendations\n- Coverage: Percentage of catalog being recommended\n- Diversity: Variety in recommendations\n- Novelty: Measure of how unpopular/unknown the recommended items are (based on inverse popularity)\n- Serendipity: Measure of how surprising but relevant the recommendations are\n- CTR (Click-Through Rate): Percentage of recommended items that receive clicks\n- CVR (Conversion Rate): Percentage of recommended items that result in conversions\n- Training time: Time taken to train models\n- Inference time: Time taken to generate recommendations\n- System metrics: CPU/Memory usage, latency percentiles\n\nIf account_id is provided and the user has admin privileges, returns data for that account.\nOtherwise, returns data for the current account.\n * @summary Get Recommendation Performance\n */\n  const getRecommendationPerformanceApiV1RecommendationsPerformanceGet = <\n    TData = AxiosResponse<RecommendationPerformanceResponse>,\n  >(\n    params?: GetRecommendationPerformanceApiV1RecommendationsPerformanceGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/recommendations/performance`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n   * Create a new interaction.\n   * @summary Create Interaction\n   */\n  const createInteractionApiV1InteractionsPost = <\n    TData = AxiosResponse<Interaction>,\n  >(\n    interactionCreate: InteractionCreate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(`/api/v1/interactions/`, interactionCreate, options);\n  };\n\n  /**\n   * Get all interactions for the current account.\n   * @summary Read Interactions\n   */\n  const readInteractionsApiV1InteractionsGet = <\n    TData = AxiosResponse<PaginatedResponseInteraction>,\n  >(\n    params?: ReadInteractionsApiV1InteractionsGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/interactions/`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n   * Crear una nueva interacción usando external_user_id y external_product_id.\n   * @summary Create Interaction External\n   */\n  const createInteractionExternalApiV1InteractionsExternalPost = <\n    TData = AxiosResponse<Interaction>,\n  >(\n    interactionExternalCreate: InteractionExternalCreate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(\n      `/api/v1/interactions/external`,\n      interactionExternalCreate,\n      options,\n    );\n  };\n\n  /**\n * Inicia el entrenamiento de modelos usando Celery.\n\nVerifica los límites de API y la frecuencia de entrenamiento permitida según el plan de suscripción\nantes de crear el trabajo de entrenamiento y encolar la tarea.\n\nReturns:\n    TrainingResponse: Respuesta con el ID del trabajo y la tarea iniciada.\n    Status: 202 Accepted - El trabajo ha sido aceptado y está siendo procesado.\n * @summary Train Models\n */\n  const trainModelsApiV1PipelineTrainPost = <\n    TData = AxiosResponse<TrainingResponse>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(`/api/v1/pipeline/train`, undefined, options);\n  };\n\n  /**\n * Obtiene el estado del último entrenamiento completado.\n\nEste endpoint devuelve las métricas del último modelo entrenado exitosamente,\nno el estado de un trabajo en curso. Para consultar el estado de un trabajo\nespecífico, use el endpoint /pipeline/jobs/{job_id}/status.\n * @summary Get Training Status\n */\n  const getTrainingStatusApiV1PipelineStatusGet = <\n    TData = AxiosResponse<TrainingStatus>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/pipeline/status`, options);\n  };\n\n  /**\n * Consulta el estado de un trabajo de entrenamiento específico.\n\nArgs:\n    job_id: ID del trabajo de entrenamiento\n    account: Cuenta actual\n    db: Sesión de base de datos\n\nReturns:\n    Estado detallado del trabajo de entrenamiento\n * @summary Get Training Job Status\n */\n  const getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet = <\n    TData = AxiosResponse<TrainingJobStatus>,\n  >(\n    jobId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/pipeline/jobs/${jobId}/status`, options);\n  };\n\n  /**\n   * Lista todos los modelos entrenados para la cuenta.\n   * @summary List Models\n   */\n  const listModelsApiV1PipelineModelsGet = <\n    TData = AxiosResponse<ModelMetadataResponse[]>,\n  >(\n    params?: ListModelsApiV1PipelineModelsGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/pipeline/models`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n   * Obtiene las métricas de un modelo específico.\n   * @summary Get Model Metrics\n   */\n  const getModelMetricsApiV1PipelineModelsModelIdMetricsGet = <\n    TData = AxiosResponse<TrainingStatus>,\n  >(\n    modelId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/pipeline/models/${modelId}/metrics`, options);\n  };\n\n  /**\n   * Invalida la caché de modelos y métricas.\n   * @summary Invalidate Cache\n   */\n  const invalidateCacheApiV1PipelineInvalidateCachePost = <\n    TData = AxiosResponse<unknown>,\n  >(\n    params?: InvalidateCacheApiV1PipelineInvalidateCachePostParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(`/api/v1/pipeline/invalidate-cache`, undefined, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n * Inicia un entrenamiento específico para la cuenta `account_id`.\nSolo para administradores del sistema.\n\nReturns:\n    Dict[str, Any]: Respuesta con el ID del trabajo y la tarea iniciada.\n    Status: 202 Accepted - El trabajo ha sido aceptado y está siendo procesado.\n * @summary Train Artifact For Account\n */\n  const trainArtifactForAccountApiV1PipelineTrainAccountIdPost = <\n    TData = AxiosResponse<TrainArtifactForAccountApiV1PipelineTrainAccountIdPost202>,\n  >(\n    accountId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(\n      `/api/v1/pipeline/train/${accountId}`,\n      undefined,\n      options,\n    );\n  };\n\n  /**\n * Endpoint para procesar trabajos de entrenamiento manualmente.\nEste endpoint es para uso administrativo o debugging.\n * @summary Process Training Job\n */\n  const processTrainingJobApiV1PipelineProcessPost = <\n    TData = AxiosResponse<ProcessTrainingJobApiV1PipelineProcessPost200>,\n  >(\n    processTrainingJobApiV1PipelineProcessPostBody: ProcessTrainingJobApiV1PipelineProcessPostBody,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(\n      `/api/v1/pipeline/process`,\n      processTrainingJobApiV1PipelineProcessPostBody,\n      options,\n    );\n  };\n\n  /**\n * Endpoint de callback para notificaciones de finalización de entrenamiento.\nEste endpoint puede ser llamado por Celery o por sistemas externos.\n * @summary Training Callback\n */\n  const trainingCallbackApiV1PipelineCallbackJobIdPost = <\n    TData = AxiosResponse<TrainingCallbackApiV1PipelineCallbackJobIdPost200>,\n  >(\n    jobId: number,\n    trainingCallbackApiV1PipelineCallbackJobIdPostBody: TrainingCallbackApiV1PipelineCallbackJobIdPostBody,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(\n      `/api/v1/pipeline/callback/${jobId}`,\n      trainingCallbackApiV1PipelineCallbackJobIdPostBody,\n      options,\n    );\n  };\n\n  /**\n * Lista los trabajos de entrenamiento recientes para la cuenta actual.\n\nEste endpoint permite obtener una lista de trabajos de entrenamiento,\nordenados por fecha de creación (más recientes primero).\n\nArgs:\n    limit: Número máximo de trabajos a devolver (máximo 100)\n    status: Filtrar por estado del trabajo (opcional)\n    account: Cuenta actual\n    db: Sesión de base de datos\n\nReturns:\n    Lista de trabajos de entrenamiento\n * @summary List Training Jobs\n */\n  const listTrainingJobsApiV1PipelineJobsGet = <\n    TData = AxiosResponse<TrainingJobStatus[]>,\n  >(\n    params?: ListTrainingJobsApiV1PipelineJobsGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/pipeline/jobs`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n   * @summary Test Cache\n   */\n  const testCacheApiV1CacheTestCacheGet = <TData = AxiosResponse<unknown>>(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/cache/test-cache`, options);\n  };\n\n  /**\n   * @summary Check Redis\n   */\n  const checkRedisApiV1CacheRedisHealthGet = <TData = AxiosResponse<unknown>>(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/cache/redis-health`, options);\n  };\n\n  /**\n * Get analytics data for the current account.\n\nIf account_id is provided and the user has admin privileges, returns data for that account.\nOtherwise, returns data for the current account.\n * @summary Get Account Analytics\n */\n  const getAccountAnalyticsApiV1AnalyticsAnalyticsAccountGet = <\n    TData = AxiosResponse<GetAccountAnalyticsApiV1AnalyticsAnalyticsAccountGet200>,\n  >(\n    params?: GetAccountAnalyticsApiV1AnalyticsAnalyticsAccountGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/analytics/analytics/account`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n * Get analytics data for API endpoints.\n\nIf account_id is provided and the user has admin privileges, returns data for that account.\nOtherwise, returns data for the current account.\n\nCan be filtered by endpoint path and HTTP method.\n * @summary Get Endpoint Analytics\n */\n  const getEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGet = <\n    TData = AxiosResponse<\n      GetEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGet200Item[]\n    >,\n  >(\n    params?: GetEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/analytics/analytics/endpoints`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n * Get performance metrics for recommendation models.\n\nReturns metrics such as:\n- Precision/Recall/NDCG/MAP: Accuracy of recommendations\n- Coverage: Percentage of catalog being recommended\n- Diversity: Variety in recommendations\n- Novelty: Measure of how unpopular/unknown the recommended items are (based on inverse popularity)\n- Serendipity: Measure of how surprising but relevant the recommendations are\n- CTR (Click-Through Rate): Percentage of recommended items that receive clicks\n- CVR (Conversion Rate): Percentage of recommended items that result in conversions\n- Training time: Time taken to train models\n- Inference time: Time taken to generate recommendations\n- System metrics: CPU/Memory usage, latency percentiles\n\nIf account_id is provided and the user has admin privileges, returns data for that account.\nOtherwise, returns data for the current account.\n * @summary Get Recommendation Performance\n */\n  const getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet =\n    <TData = AxiosResponse<RecommendationPerformanceResponse>>(\n      params?: GetRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGetParams,\n      options?: AxiosRequestConfig,\n    ): Promise<TData> => {\n      return axios.get(\n        `/api/v1/analytics/analytics/recommendation_performance`,\n        {\n          ...options,\n          params: { ...params, ...options?.params },\n        },\n      );\n    };\n\n  /**\n * Compara métricas entre diferentes versiones de modelos.\n\nEste endpoint permite comparar las métricas de rendimiento entre diferentes modelos\npara analizar mejoras o regresiones en el tiempo.\n\nIncluye métricas estándar de ML así como métricas proxy de negocio como:\n- Tasa de conversión en el conjunto de prueba\n- ROI estimado\n- Engagement estimado\n- Valor del cliente estimado\n\nSi se proporcionan model_ids, compara específicamente esos modelos.\nSi no, compara los últimos N modelos según el parámetro limit.\n\nReturns:\n    Diccionario con comparación detallada de métricas entre versiones de modelos\n * @summary Compare Model Versions\n */\n  const compareModelVersionsApiV1AnalyticsAnalyticsModelsCompareGet = <\n    TData = AxiosResponse<ModelComparisonResponse>,\n  >(\n    params?: CompareModelVersionsApiV1AnalyticsAnalyticsModelsCompareGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/analytics/analytics/models/compare`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n * Obtiene el historial de valores para una métrica específica.\n\nEste endpoint permite visualizar la evolución de una métrica a lo largo del tiempo\npara diferentes versiones de modelos.\n\nÚtil para análisis de tendencias y para evaluar el impacto de cambios en el modelo.\n\nArgs:\n    metric_name: Nombre de la métrica a consultar\n    limit: Número máximo de puntos de datos a devolver\n    account_id: ID opcional de la cuenta\n    \nReturns:\n    Lista con valores históricos de la métrica\n * @summary Get Metrics History\n */\n  const getMetricsHistoryApiV1AnalyticsAnalyticsMetricsHistoryGet = <\n    TData = AxiosResponse<MetricsHistoryResponse>,\n  >(\n    params: GetMetricsHistoryApiV1AnalyticsAnalyticsMetricsHistoryGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/analytics/analytics/metrics/history`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n * Crea una sesión de checkout para suscribirse o cambiar de plan.\n\nRequiere autenticación con API Key y JWT.\n * @summary Create Checkout Session\n */\n  const createCheckoutSessionApiV1BillingCreateCheckoutSessionPost = <\n    TData = AxiosResponse<CreateCheckoutSessionResponse>,\n  >(\n    createCheckoutSessionRequest: CreateCheckoutSessionRequest,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(\n      `/api/v1/billing/create-checkout-session`,\n      createCheckoutSessionRequest,\n      options,\n    );\n  };\n\n  /**\n * Crea una sesión del portal de facturación para gestionar la suscripción.\n\nRequiere autenticación con API Key y JWT.\n * @summary Create Portal Session\n */\n  const createPortalSessionApiV1BillingCreatePortalSessionPost = <\n    TData = AxiosResponse<CreatePortalSessionResponse>,\n  >(\n    createPortalSessionRequest: CreatePortalSessionRequest,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(\n      `/api/v1/billing/create-portal-session`,\n      createPortalSessionRequest,\n      options,\n    );\n  };\n\n  /**\n * Webhook para recibir eventos de Mercado Pago.\n\nNo requiere autenticación, pero verifica la firma de Mercado Pago.\n * @summary Mercadopago Webhook\n */\n  const mercadopagoWebhookApiV1BillingWebhookMercadopagoPost = <\n    TData = AxiosResponse<unknown>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(\n      `/api/v1/billing/webhook/mercadopago`,\n      undefined,\n      options,\n    );\n  };\n\n  /**\n * List all roles available for the current account.\n\nRequires admin privileges.\n * @summary List Roles\n */\n  const listRolesApiV1RolesGet = <TData = AxiosResponse<Role[]>>(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/roles/`, options);\n  };\n\n  /**\n * Create a new role.\n\nRequires admin privileges.\n * @summary Create Role\n */\n  const createRoleApiV1RolesPost = <TData = AxiosResponse<Role>>(\n    roleCreate: RoleCreate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(`/api/v1/roles/`, roleCreate, options);\n  };\n\n  /**\n * Get a specific role by ID.\n\nRequires admin privileges.\n * @summary Get Role\n */\n  const getRoleApiV1RolesRoleIdGet = <TData = AxiosResponse<Role>>(\n    roleId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/roles/${roleId}`, options);\n  };\n\n  /**\n * Update an existing role.\n\nRequires admin privileges.\n * @summary Update Role\n */\n  const updateRoleApiV1RolesRoleIdPut = <TData = AxiosResponse<Role>>(\n    roleId: number,\n    roleCreate: RoleCreate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.put(`/api/v1/roles/${roleId}`, roleCreate, options);\n  };\n\n  /**\n * Delete a role.\n\nRequires admin privileges.\n * @summary Delete Role\n */\n  const deleteRoleApiV1RolesRoleIdDelete = <TData = AxiosResponse<void>>(\n    roleId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.delete(`/api/v1/roles/${roleId}`, options);\n  };\n\n  /**\n * Get all permissions assigned to a role.\n\nRequires admin privileges.\n * @summary Get Role Permissions\n */\n  const getRolePermissionsApiV1RolesRoleIdPermissionsGet = <\n    TData = AxiosResponse<Permission[]>,\n  >(\n    roleId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/roles/${roleId}/permissions`, options);\n  };\n\n  /**\n * Assign a permission to a role.\n\nRequires admin privileges.\n * @summary Assign Permission To Role\n */\n  const assignPermissionToRoleApiV1RolesRoleIdPermissionsPermissionIdPost = <\n    TData = AxiosResponse<unknown>,\n  >(\n    roleId: number,\n    permissionId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(\n      `/api/v1/roles/${roleId}/permissions/${permissionId}`,\n      undefined,\n      options,\n    );\n  };\n\n  /**\n * Remove a permission from a role.\n\nRequires admin privileges.\n * @summary Remove Permission From Role\n */\n  const removePermissionFromRoleApiV1RolesRoleIdPermissionsPermissionIdDelete =\n    <TData = AxiosResponse<void>>(\n      roleId: number,\n      permissionId: number,\n      options?: AxiosRequestConfig,\n    ): Promise<TData> => {\n      return axios.delete(\n        `/api/v1/roles/${roleId}/permissions/${permissionId}`,\n        options,\n      );\n    };\n\n  /**\n * List all permissions available for the current account.\n\nRequires admin privileges.\n * @summary List Permissions\n */\n  const listPermissionsApiV1PermissionsGet = <\n    TData = AxiosResponse<Permission[]>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/permissions/`, options);\n  };\n\n  /**\n * Create a new permission.\n\nRequires admin privileges.\n * @summary Create Permission\n */\n  const createPermissionApiV1PermissionsPost = <\n    TData = AxiosResponse<Permission>,\n  >(\n    permissionCreate: PermissionCreate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(`/api/v1/permissions/`, permissionCreate, options);\n  };\n\n  /**\n * Get a specific permission by ID.\n\nRequires admin privileges.\n * @summary Get Permission\n */\n  const getPermissionApiV1PermissionsPermissionIdGet = <\n    TData = AxiosResponse<Permission>,\n  >(\n    permissionId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/permissions/${permissionId}`, options);\n  };\n\n  /**\n * Get all roles that have a specific permission.\n\nRequires admin privileges.\n * @summary Get Roles With Permission\n */\n  const getRolesWithPermissionApiV1PermissionsPermissionIdRolesGet = <\n    TData = AxiosResponse<Role[]>,\n  >(\n    permissionId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/permissions/${permissionId}/roles`, options);\n  };\n\n  /**\n * Limpia logs de auditoría más antiguos que el período especificado.\n\nArgs:\n    days_to_keep: Número de días a mantener los logs (por defecto 90)\n    account_id: ID de la cuenta específica (None para todas las cuentas)\n    run_async: Si es True, ejecuta la limpieza en segundo plano\n\nReturns:\n    Diccionario con información sobre la operación o el ID de la tarea\n * @summary Cleanup Audit Logs\n */\n  const cleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPost = <\n    TData = AxiosResponse<CleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPost200>,\n  >(\n    params?: CleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPostParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(\n      `/api/v1/maintenance/maintenance/cleanup-audit-logs`,\n      undefined,\n      {\n        ...options,\n        params: { ...params, ...options?.params },\n      },\n    );\n  };\n\n  /**\n * Limpia interacciones más antiguas que el período especificado.\n\nArgs:\n    days_to_keep: Número de días a mantener las interacciones (por defecto 180)\n    account_id: ID de la cuenta específica (None para todas las cuentas)\n    batch_size: Tamaño del lote para eliminación (por defecto 10000)\n    run_async: Si es True, ejecuta la limpieza en segundo plano\n\nReturns:\n    Diccionario con información sobre la operación o el ID de la tarea\n * @summary Cleanup Interactions\n */\n  const cleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPost =\n    <\n      TData = AxiosResponse<CleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPost200>,\n    >(\n      params?: CleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPostParams,\n      options?: AxiosRequestConfig,\n    ): Promise<TData> => {\n      return axios.post(\n        `/api/v1/maintenance/maintenance/cleanup-interactions`,\n        undefined,\n        {\n          ...options,\n          params: { ...params, ...options?.params },\n        },\n      );\n    };\n\n  /**\n * Obtiene el estado de una tarea de mantenimiento.\n\nArgs:\n    task_id: ID de la tarea\n\nReturns:\n    Diccionario con información sobre la tarea\n * @summary Get Task Status\n */\n  const getTaskStatusApiV1MaintenanceMaintenanceTaskTaskIdGet = <\n    TData = AxiosResponse<GetTaskStatusApiV1MaintenanceMaintenanceTaskTaskIdGet200>,\n  >(\n    taskId: string,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/maintenance/maintenance/task/${taskId}`, options);\n  };\n\n  /**\n * Archiva y luego limpia logs de auditoría más antiguos que el período especificado.\n\nEsta función primero exporta los datos a GCS y luego los elimina de Cloud SQL,\nproporcionando una estrategia de retención de datos costo-efectiva.\n\nArgs:\n    days_to_keep: Número de días a mantener los logs en Cloud SQL (por defecto 90)\n    account_id: ID de la cuenta específica (None para todas las cuentas)\n    batch_size: Tamaño del lote para procesamiento (por defecto 10000)\n    run_async: Si es True, ejecuta el archivado en segundo plano\n\nReturns:\n    Diccionario con información sobre la operación o el ID de la tarea\n * @summary Archive And Cleanup Audit Logs\n */\n  const archiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPost =\n    <\n      TData = AxiosResponse<ArchiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPost200>,\n    >(\n      params?: ArchiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPostParams,\n      options?: AxiosRequestConfig,\n    ): Promise<TData> => {\n      return axios.post(\n        `/api/v1/maintenance/maintenance/archive-and-cleanup-audit-logs`,\n        undefined,\n        {\n          ...options,\n          params: { ...params, ...options?.params },\n        },\n      );\n    };\n\n  /**\n * Archiva y luego limpia interacciones más antiguas que el período especificado.\n\nEsta función primero exporta los datos a GCS y luego los elimina de Cloud SQL,\nproporcionando una estrategia de retención de datos costo-efectiva.\n\nArgs:\n    days_to_keep: Número de días a mantener las interacciones en Cloud SQL (por defecto 180)\n    account_id: ID de la cuenta específica (None para todas las cuentas)\n    batch_size: Tamaño del lote para procesamiento (por defecto 10000)\n    run_async: Si es True, ejecuta el archivado en segundo plano\n\nReturns:\n    Diccionario con información sobre la operación o el ID de la tarea\n * @summary Archive And Cleanup Interactions\n */\n  const archiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPost =\n    <\n      TData = AxiosResponse<ArchiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPost200>,\n    >(\n      params?: ArchiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPostParams,\n      options?: AxiosRequestConfig,\n    ): Promise<TData> => {\n      return axios.post(\n        `/api/v1/maintenance/maintenance/archive-and-cleanup-interactions`,\n        undefined,\n        {\n          ...options,\n          params: { ...params, ...options?.params },\n        },\n      );\n    };\n\n  /**\n * Lista archivos archivados para una tabla específica.\n\nArgs:\n    table_name: Nombre de la tabla (audit_logs o interactions)\n    start_date: Fecha de inicio para filtrar (formato ISO)\n    end_date: Fecha de fin para filtrar (formato ISO)\n    account_id: ID de cuenta para filtrar\n\nReturns:\n    Lista de archivos archivados con metadatos\n * @summary List Archived Files\n */\n  const listArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGet =\n    <\n      TData = AxiosResponse<ListArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGet200>,\n    >(\n      tableName: string,\n      params?: ListArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGetParams,\n      options?: AxiosRequestConfig,\n    ): Promise<TData> => {\n      return axios.get(\n        `/api/v1/maintenance/maintenance/archived-files/${tableName}`,\n        {\n          ...options,\n          params: { ...params, ...options?.params },\n        },\n      );\n    };\n\n  /**\n * Limpia registros con soft delete que han excedido el período de retención final.\n\nEsta función identifica y elimina permanentemente (o archiva y luego elimina)\nregistros que tienen is_active = FALSE y deleted_at anterior al umbral definido.\n\nArgs:\n    retention_days: Número de días de retención después del soft delete (por defecto 365)\n    account_id: ID de la cuenta específica (None para todas las cuentas)\n    dry_run: Si es True, solo reporta qué se eliminaría sin hacer cambios\n    run_async: Si es True, ejecuta la limpieza en segundo plano\n\nReturns:\n    Diccionario con información sobre la operación o el ID de la tarea\n * @summary Cleanup Soft Deleted Records\n */\n  const cleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPost =\n    <\n      TData = AxiosResponse<CleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPost200>,\n    >(\n      params?: CleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPostParams,\n      options?: AxiosRequestConfig,\n    ): Promise<TData> => {\n      return axios.post(\n        `/api/v1/maintenance/maintenance/cleanup-soft-deleted-records`,\n        undefined,\n        {\n          ...options,\n          params: { ...params, ...options?.params },\n        },\n      );\n    };\n\n  /**\n * Obtiene estadísticas sobre registros con soft delete.\n\nArgs:\n    account_id: ID de cuenta para filtrar (None para todas las cuentas)\n    run_async: Si es True, ejecuta como tarea en segundo plano\n\nReturns:\n    Estadísticas por tabla o ID de tarea si run_async=True\n * @summary Get Soft Delete Statistics\n */\n  const getSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGet =\n    <\n      TData = AxiosResponse<GetSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGet200>,\n    >(\n      params?: GetSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGetParams,\n      options?: AxiosRequestConfig,\n    ): Promise<TData> => {\n      return axios.get(\n        `/api/v1/maintenance/maintenance/soft-delete-statistics`,\n        {\n          ...options,\n          params: { ...params, ...options?.params },\n        },\n      );\n    };\n\n  /**\n * Monitorea las tablas de alto volumen y devuelve estadísticas.\n\nArgs:\n    run_async: Si es True, ejecuta el monitoreo en segundo plano\n\nReturns:\n    Diccionario con estadísticas de las tablas o el ID de la tarea\n * @summary Monitor Tables\n */\n  const monitorTablesApiV1MaintenanceMaintenanceMonitorTablesPost = <\n    TData = AxiosResponse<MonitorTablesApiV1MaintenanceMaintenanceMonitorTablesPost200>,\n  >(\n    params?: MonitorTablesApiV1MaintenanceMaintenanceMonitorTablesPostParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(\n      `/api/v1/maintenance/maintenance/monitor-tables`,\n      undefined,\n      {\n        ...options,\n        params: { ...params, ...options?.params },\n      },\n    );\n  };\n\n  /**\n * Get subscription usage information for the current account.\n\nReturns:\n    Dict with subscription usage information including:\n    - API calls used and limit\n    - Storage used and limit\n    - Available models\n    - Reset date for API calls counter\n * @summary Get Subscription Usage\n */\n  const getSubscriptionUsageApiV1SubscriptionUsageGet = <\n    TData = AxiosResponse<GetSubscriptionUsageApiV1SubscriptionUsageGet200>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/subscription/usage`, options);\n  };\n\n  /**\n * Get storage usage information for the current account.\n\nReturns:\n    Dict with storage usage information including:\n    - Storage used and limit\n    - Breakdown by data type\n    - Last measurement time\n * @summary Get Storage Usage\n */\n  const getStorageUsageApiV1StorageUsageGet = <\n    TData = AxiosResponse<GetStorageUsageApiV1StorageUsageGet200>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/storage/usage`, options);\n  };\n\n  /**\n * Force a refresh of storage usage calculation for the current account.\n\nReturns:\n    Dict with updated storage usage information\n * @summary Refresh Storage Usage\n */\n  const refreshStorageUsageApiV1StorageRefreshPost = <\n    TData = AxiosResponse<RefreshStorageUsageApiV1StorageRefreshPost200>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(`/api/v1/storage/refresh`, undefined, options);\n  };\n\n  /**\n * Carga masiva de datos de usuarios, productos e interacciones.\n\nEste endpoint permite cargar datos en lote para inicializar o actualizar el sistema de recomendación.\nLos datos se procesan de forma asíncrona utilizando Celery para mayor robustez.\nLos datos sensibles se almacenan de forma segura en GCS o en el sistema de archivos local.\n\nPara una guía detallada sobre cómo formatear y enviar datos, consulte la\n[Guía de Ingesta de Datos Masiva](/docs/guides/data_ingestion_guide).\n * @summary Batch Data Ingestion\n */\n  const batchDataIngestionApiV1IngestionBatchPost = <\n    TData = AxiosResponse<BatchIngestionResponse>,\n  >(\n    batchIngestionRequest: BatchIngestionRequest,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(\n      `/api/v1/ingestion/batch`,\n      batchIngestionRequest,\n      options,\n    );\n  };\n\n  /**\n * Lista los trabajos de ingesta masiva recientes para la cuenta actual.\n\nEste endpoint permite obtener una lista de trabajos de ingesta masiva,\nordenados por fecha de creación (más recientes primero).\n\nArgs:\n    limit: Número máximo de trabajos a devolver (máximo 100)\n    status: Filtrar por estado del trabajo (opcional)\n    account: Cuenta actual\n    db: Sesión de base de datos\n\nReturns:\n    Lista de trabajos de ingesta masiva\n * @summary List Batch Jobs\n */\n  const listBatchJobsApiV1IngestionBatchGet = <\n    TData = AxiosResponse<BatchIngestionJobStatus[]>,\n  >(\n    params?: ListBatchJobsApiV1IngestionBatchGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/ingestion/batch`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n * Consulta el estado de un trabajo de ingesta masiva.\n\nEste endpoint permite verificar el progreso de un trabajo de ingesta masiva,\nincluyendo la cantidad de registros procesados, errores encontrados y tiempo estimado\nde finalización.\n\nPara más detalles sobre cómo monitorear el proceso de ingesta y manejar errores,\nconsulte la sección [Monitoreo del Proceso](/docs/guides/data_ingestion_guide#monitoreo-del-proceso)\nen la Guía de Ingesta de Datos.\n\nArgs:\n    job_id: ID del trabajo de ingesta masiva\n    account: Cuenta actual\n    db: Sesión de base de datos\n\nReturns:\n    Estado del trabajo de ingesta masiva\n * @summary Get Batch Job Status\n */\n  const getBatchJobStatusApiV1IngestionBatchJobIdGet = <\n    TData = AxiosResponse<BatchIngestionJobStatus>,\n  >(\n    jobId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/ingestion/batch/${jobId}`, options);\n  };\n\n  /**\n   * Return a consolidated summary of usage information for the current account.\n   * @summary Get Usage Summary\n   */\n  const getUsageSummaryApiV1UsageSummaryGet = <\n    TData = AxiosResponse<UsageSummaryResponse>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/usage/summary`, options);\n  };\n\n  /**\n * Get historical usage data for the current account.\n\nArgs:\n    start_date: Optional start date for filtering data\n    end_date: Optional end date for filtering data\n    \nReturns:\n    List of daily usage data points\n * @summary Get Usage History\n */\n  const getUsageHistoryApiV1UsageGet = <\n    TData = AxiosResponse<UsageHistoryItemResponse[]>,\n  >(\n    params?: GetUsageHistoryApiV1UsageGetParams,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/usage/`, {\n      ...options,\n      params: { ...params, ...options?.params },\n    });\n  };\n\n  /**\n * Lista todas las API Keys activas para la cuenta actual.\n\nRetorna una lista de todas las API Keys activas asociadas a la cuenta,\nincluyendo información como nombre, prefijo, últimos caracteres, fecha de creación\ny último uso.\n\n**Nota**: No se incluyen las API Keys completas por seguridad.\n * @summary List Api Keys\n */\n  const listApiKeysApiV1ApiKeysGet = <\n    TData = AxiosResponse<ApiKeyListResponse>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/api-keys/`, options);\n  };\n\n  /**\n * Crea una nueva API Key para la cuenta actual.\n\nEste endpoint permite crear múltiples API Keys para una cuenta, lo que mejora\nla seguridad y flexibilidad:\n- Diferentes claves para diferentes entornos (desarrollo, staging, producción)\n- Claves específicas para diferentes miembros del equipo\n- Posibilidad de revocar claves específicas sin afectar otras integraciones\n\n**IMPORTANTE**:\n- Requiere autenticación JWT (debes estar logueado)\n- La API Key completa solo se devolverá una vez\n- Puedes crear múltiples API Keys activas\n- Cada API Key puede tener un nombre descriptivo\n\n**Autenticación requerida**: JWT token en el header Authorization: Bearer <token>\n\nReturns:\n- **id**: ID único de la API Key\n- **api_key**: Tu nueva API Key completa (solo se muestra una vez)\n- **name**: Nombre descriptivo de la API Key\n- **prefix**: Prefijo de la API Key para identificación\n- **created_at**: Fecha y hora de creación\n- **message**: Mensaje informativo sobre el uso seguro\n * @summary Create Api Key\n */\n  const createApiKeyApiV1ApiKeysPost = <\n    TData = AxiosResponse<NewApiKeyResponse>,\n  >(\n    apiKeyCreate: ApiKeyCreate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(`/api/v1/api-keys/`, apiKeyCreate, options);\n  };\n\n  /**\n * Revoca todas las API Keys de la cuenta actual.\nEsta acción es irreversible. Después de revocar, tendrás que generar nuevas API Keys.\n * @summary Revoke Api Key\n */\n  const revokeApiKeyApiV1ApiKeysDelete = <TData = AxiosResponse<void>>(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.delete(`/api/v1/api-keys/`, options);\n  };\n\n  /**\n * Obtiene información sobre la API Key actual.\nNo devuelve la API Key completa, solo metadatos como:\n- Prefijo de la API Key\n- Últimos caracteres\n- Fecha de creación\n- Último uso (si está disponible)\n\nEsta información permite identificar la API Key sin comprometer seguridad.\n * @summary Get Current Api Key\n */\n  const getCurrentApiKeyApiV1ApiKeysCurrentGet = <\n    TData = AxiosResponse<ApiKeyResponse>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/api-keys/current`, options);\n  };\n\n  /**\n * Actualiza los metadatos de una API Key específica.\n\nPermite actualizar información como el nombre descriptivo de la API Key.\nNo se puede cambiar la clave en sí, solo sus metadatos.\n\n**IMPORTANTE**:\n- Solo puedes actualizar API Keys que pertenecen a tu cuenta\n- No se puede cambiar la API Key en sí, solo metadatos\n- La API Key debe estar activa para poder actualizarla\n * @summary Update Api Key\n */\n  const updateApiKeyApiV1ApiKeysApiKeyIdPut = <\n    TData = AxiosResponse<ApiKeyResponse>,\n  >(\n    apiKeyId: number,\n    apiKeyUpdate: ApiKeyUpdate,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.put(`/api/v1/api-keys/${apiKeyId}`, apiKeyUpdate, options);\n  };\n\n  /**\n * Revoca una API Key específica.\n\nEsta acción es irreversible. La API Key será desactivada y no podrá\nutilizarse para autenticar solicitudes.\n\n**IMPORTANTE**:\n- Solo puedes revocar API Keys que pertenecen a tu cuenta\n- Esta acción es irreversible\n- La API Key dejará de funcionar inmediatamente\n- Otras API Keys de la cuenta no se verán afectadas\n * @summary Revoke Specific Api Key\n */\n  const revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete = <\n    TData = AxiosResponse<void>,\n  >(\n    apiKeyId: number,\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.delete(`/api/v1/api-keys/${apiKeyId}`, options);\n  };\n\n  /**\n * Reset all sandbox data for FREE plan users.\n\nThis endpoint allows FREE plan users to clean their experimental data\nto start fresh with new experiments.\n\nOnly available for FREE plan users.\n * @summary Reset Sandbox Data\n */\n  const resetSandboxDataApiV1SandboxResetPost = <\n    TData = AxiosResponse<unknown>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.post(`/api/v1/sandbox/reset`, undefined, options);\n  };\n\n  /**\n * Get current sandbox status and data counts.\n\nReturns information about the current state of sandbox data.\n * @summary Get Sandbox Status\n */\n  const getSandboxStatusApiV1SandboxStatusGet = <\n    TData = AxiosResponse<unknown>,\n  >(\n    options?: AxiosRequestConfig,\n  ): Promise<TData> => {\n    return axios.get(`/api/v1/sandbox/status`, options);\n  };\n\n  return {\n    healthCheckHealthGet,\n    healthCheckApiV1HealthGet,\n    dbHealthCheckApiV1HealthDbGet,\n    authHealthCheckApiV1HealthAuthGet,\n    sendVerificationEmailApiV1AuthSendVerificationEmailPost,\n    verifyEmailApiV1AuthVerifyEmailGet,\n    registerApiV1AuthRegisterPost,\n    loginApiV1AuthTokenPost,\n    logoutApiV1AuthLogoutPost,\n    listAccountsApiV1AccountsGet,\n    createAccountApiV1AccountsAccountsPost,\n    getAccountApiV1AccountsAccountIdGet,\n    deactivateAccountApiV1AccountsAccountIdDeactivatePatch,\n    activateAccountApiV1AccountsAccountIdActivatePatch,\n    getAccountInfoApiV1AccountsCurrentGet,\n    updateCurrentAccountApiV1AccountsCurrentPut,\n    patchCurrentAccountApiV1AccountsCurrentPatch,\n    getAuditLogsApiV1AccountsAccountIdAuditLogsGet,\n    getApiUsageApiV1AccountsUsageGet,\n    getAvailablePlansApiV1PlansGet,\n    getCurrentUserInfoApiV1SystemUsersMeGet,\n    updateUserMeApiV1SystemUsersMePut,\n    deleteUserMeApiV1SystemUsersMeDelete,\n    createSystemUserApiV1SystemUsersPost,\n    getSystemUserApiV1SystemUsersUserIdGet,\n    createRoleApiV1SystemUsersRolesPost,\n    assignRoleApiV1SystemUsersUserIdRolesRoleIdPost,\n    removeRoleApiV1SystemUsersUserIdRolesRoleIdDelete,\n    getUserRolesApiV1SystemUsersUserIdRolesGet,\n    getUserPermissionsApiV1SystemUsersUserIdPermissionsGet,\n    createEndUserApiV1EndUsersPost,\n    readEndUsersApiV1EndUsersGet,\n    readEndUserApiV1EndUsersUserIdGet,\n    createProductApiV1ProductsPost,\n    readProductsApiV1ProductsGet,\n    getProductApiV1ProductsProductIdGet,\n    updateProductApiV1ProductsProductIdPut,\n    updateInventoryApiV1ProductsProductIdInventoryPatch,\n    getProductByExternalIdApiV1ProductsExternalExternalIdGet,\n    updateProductByExternalIdApiV1ProductsExternalExternalIdPut,\n    deleteProductByExternalIdApiV1ProductsExternalExternalIdDelete,\n    updateInventoryByExternalIdApiV1ProductsExternalExternalIdInventoryPatch,\n    getMostSearchedApiV1RecommendationsMostSearchedGet,\n    getTrendingSearchesApiV1RecommendationsTrendingSearchesGet,\n    getPopularTrendsApiV1RecommendationsPopularTrendsGet,\n    getRelatedSearchesApiV1RecommendationsRelatedSearchesProductIdGet,\n    getMostSoldApiV1RecommendationsMostSoldGet,\n    getTopRatedApiV1RecommendationsTopRatedGet,\n    getCategoryProductsApiV1RecommendationsCategoryCategoryGet,\n    getRelatedCategoriesApiV1RecommendationsRelatedCategoriesCategoryGet,\n    getAlsoBoughtApiV1RecommendationsAlsoBoughtProductIdGet,\n    getSimilarProductsApiV1RecommendationsProductsProductIdSimilarGet,\n    invalidateUserCacheApiV1RecommendationsInvalidateCacheUserIdPost,\n    invalidateAccountCacheApiV1RecommendationsInvalidateCachePost,\n    queryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPost,\n    getRecommendationExplanationApiV1RecommendationsExplainUserIdItemIdGet,\n    getRecommendationExplanationExternalApiV1RecommendationsExplainExternalExternalUserIdExternalItemIdGet,\n    getSimilarProductsExternalApiV1RecommendationsProductsExternalExternalProductIdSimilarGet,\n    getAlsoBoughtExternalApiV1RecommendationsAlsoBoughtExternalExternalProductIdGet,\n    getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet,\n    rollbackModelApiV1RecommendationsRollbackArtifactVersionPost,\n    getRecommendationPerformanceApiV1RecommendationsPerformanceGet,\n    createInteractionApiV1InteractionsPost,\n    readInteractionsApiV1InteractionsGet,\n    createInteractionExternalApiV1InteractionsExternalPost,\n    trainModelsApiV1PipelineTrainPost,\n    getTrainingStatusApiV1PipelineStatusGet,\n    getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet,\n    listModelsApiV1PipelineModelsGet,\n    getModelMetricsApiV1PipelineModelsModelIdMetricsGet,\n    invalidateCacheApiV1PipelineInvalidateCachePost,\n    trainArtifactForAccountApiV1PipelineTrainAccountIdPost,\n    processTrainingJobApiV1PipelineProcessPost,\n    trainingCallbackApiV1PipelineCallbackJobIdPost,\n    listTrainingJobsApiV1PipelineJobsGet,\n    testCacheApiV1CacheTestCacheGet,\n    checkRedisApiV1CacheRedisHealthGet,\n    getAccountAnalyticsApiV1AnalyticsAnalyticsAccountGet,\n    getEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGet,\n    getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet,\n    compareModelVersionsApiV1AnalyticsAnalyticsModelsCompareGet,\n    getMetricsHistoryApiV1AnalyticsAnalyticsMetricsHistoryGet,\n    createCheckoutSessionApiV1BillingCreateCheckoutSessionPost,\n    createPortalSessionApiV1BillingCreatePortalSessionPost,\n    mercadopagoWebhookApiV1BillingWebhookMercadopagoPost,\n    listRolesApiV1RolesGet,\n    createRoleApiV1RolesPost,\n    getRoleApiV1RolesRoleIdGet,\n    updateRoleApiV1RolesRoleIdPut,\n    deleteRoleApiV1RolesRoleIdDelete,\n    getRolePermissionsApiV1RolesRoleIdPermissionsGet,\n    assignPermissionToRoleApiV1RolesRoleIdPermissionsPermissionIdPost,\n    removePermissionFromRoleApiV1RolesRoleIdPermissionsPermissionIdDelete,\n    listPermissionsApiV1PermissionsGet,\n    createPermissionApiV1PermissionsPost,\n    getPermissionApiV1PermissionsPermissionIdGet,\n    getRolesWithPermissionApiV1PermissionsPermissionIdRolesGet,\n    cleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPost,\n    cleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPost,\n    getTaskStatusApiV1MaintenanceMaintenanceTaskTaskIdGet,\n    archiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPost,\n    archiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPost,\n    listArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGet,\n    cleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPost,\n    getSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGet,\n    monitorTablesApiV1MaintenanceMaintenanceMonitorTablesPost,\n    getSubscriptionUsageApiV1SubscriptionUsageGet,\n    getStorageUsageApiV1StorageUsageGet,\n    refreshStorageUsageApiV1StorageRefreshPost,\n    batchDataIngestionApiV1IngestionBatchPost,\n    listBatchJobsApiV1IngestionBatchGet,\n    getBatchJobStatusApiV1IngestionBatchJobIdGet,\n    getUsageSummaryApiV1UsageSummaryGet,\n    getUsageHistoryApiV1UsageGet,\n    listApiKeysApiV1ApiKeysGet,\n    createApiKeyApiV1ApiKeysPost,\n    revokeApiKeyApiV1ApiKeysDelete,\n    getCurrentApiKeyApiV1ApiKeysCurrentGet,\n    updateApiKeyApiV1ApiKeysApiKeyIdPut,\n    revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete,\n    resetSandboxDataApiV1SandboxResetPost,\n    getSandboxStatusApiV1SandboxStatusGet,\n  };\n};\nexport type HealthCheckHealthGetResult = AxiosResponse<unknown>;\nexport type HealthCheckApiV1HealthGetResult =\n  AxiosResponse<HealthCheckApiV1HealthGet200>;\nexport type DbHealthCheckApiV1HealthDbGetResult =\n  AxiosResponse<DbHealthCheckApiV1HealthDbGet200>;\nexport type AuthHealthCheckApiV1HealthAuthGetResult =\n  AxiosResponse<AuthHealthCheckApiV1HealthAuthGet200>;\nexport type SendVerificationEmailApiV1AuthSendVerificationEmailPostResult =\n  AxiosResponse<unknown>;\nexport type VerifyEmailApiV1AuthVerifyEmailGetResult = AxiosResponse<unknown>;\nexport type RegisterApiV1AuthRegisterPostResult =\n  AxiosResponse<RegisterResponse>;\nexport type LoginApiV1AuthTokenPostResult = AxiosResponse<LoginResponse>;\nexport type LogoutApiV1AuthLogoutPostResult = AxiosResponse<unknown>;\nexport type ListAccountsApiV1AccountsGetResult = AxiosResponse<\n  AccountResponse[]\n>;\nexport type CreateAccountApiV1AccountsAccountsPostResult =\n  AxiosResponse<AccountResponse>;\nexport type GetAccountApiV1AccountsAccountIdGetResult =\n  AxiosResponse<AccountResponse>;\nexport type DeactivateAccountApiV1AccountsAccountIdDeactivatePatchResult =\n  AxiosResponse<void>;\nexport type ActivateAccountApiV1AccountsAccountIdActivatePatchResult =\n  AxiosResponse<AccountResponse>;\nexport type GetAccountInfoApiV1AccountsCurrentGetResult =\n  AxiosResponse<AccountResponse>;\nexport type UpdateCurrentAccountApiV1AccountsCurrentPutResult =\n  AxiosResponse<AccountResponse>;\nexport type PatchCurrentAccountApiV1AccountsCurrentPatchResult =\n  AxiosResponse<AccountResponse>;\nexport type GetAuditLogsApiV1AccountsAccountIdAuditLogsGetResult =\n  AxiosResponse<AuditLog[]>;\nexport type GetApiUsageApiV1AccountsUsageGetResult = AxiosResponse<UsageStats>;\nexport type GetAvailablePlansApiV1PlansGetResult =\n  AxiosResponse<GetAvailablePlansApiV1PlansGet200>;\nexport type GetCurrentUserInfoApiV1SystemUsersMeGetResult =\n  AxiosResponse<SystemUserResponse>;\nexport type UpdateUserMeApiV1SystemUsersMePutResult =\n  AxiosResponse<SystemUserResponse>;\nexport type DeleteUserMeApiV1SystemUsersMeDeleteResult = AxiosResponse<void>;\nexport type CreateSystemUserApiV1SystemUsersPostResult =\n  AxiosResponse<SystemUserResponse>;\nexport type GetSystemUserApiV1SystemUsersUserIdGetResult =\n  AxiosResponse<SystemUserResponse>;\nexport type CreateRoleApiV1SystemUsersRolesPostResult = AxiosResponse<Role>;\nexport type AssignRoleApiV1SystemUsersUserIdRolesRoleIdPostResult =\n  AxiosResponse<unknown>;\nexport type RemoveRoleApiV1SystemUsersUserIdRolesRoleIdDeleteResult =\n  AxiosResponse<void>;\nexport type GetUserRolesApiV1SystemUsersUserIdRolesGetResult = AxiosResponse<\n  Role[]\n>;\nexport type GetUserPermissionsApiV1SystemUsersUserIdPermissionsGetResult =\n  AxiosResponse<string[]>;\nexport type CreateEndUserApiV1EndUsersPostResult = AxiosResponse<EndUser>;\nexport type ReadEndUsersApiV1EndUsersGetResult =\n  AxiosResponse<PaginatedResponseEndUser>;\nexport type ReadEndUserApiV1EndUsersUserIdGetResult = AxiosResponse<EndUser>;\nexport type CreateProductApiV1ProductsPostResult = AxiosResponse<Product>;\nexport type ReadProductsApiV1ProductsGetResult =\n  AxiosResponse<PaginatedResponseProduct>;\nexport type GetProductApiV1ProductsProductIdGetResult = AxiosResponse<Product>;\nexport type UpdateProductApiV1ProductsProductIdPutResult =\n  AxiosResponse<Product>;\nexport type UpdateInventoryApiV1ProductsProductIdInventoryPatchResult =\n  AxiosResponse<Product>;\nexport type GetProductByExternalIdApiV1ProductsExternalExternalIdGetResult =\n  AxiosResponse<Product>;\nexport type UpdateProductByExternalIdApiV1ProductsExternalExternalIdPutResult =\n  AxiosResponse<Product>;\nexport type DeleteProductByExternalIdApiV1ProductsExternalExternalIdDeleteResult =\n  AxiosResponse<void>;\nexport type UpdateInventoryByExternalIdApiV1ProductsExternalExternalIdInventoryPatchResult =\n  AxiosResponse<Product>;\nexport type GetMostSearchedApiV1RecommendationsMostSearchedGetResult =\n  AxiosResponse<PaginatedResponseProduct>;\nexport type GetTrendingSearchesApiV1RecommendationsTrendingSearchesGetResult =\n  AxiosResponse<PaginatedResponseSearch>;\nexport type GetPopularTrendsApiV1RecommendationsPopularTrendsGetResult =\n  AxiosResponse<PaginatedResponseProduct>;\nexport type GetRelatedSearchesApiV1RecommendationsRelatedSearchesProductIdGetResult =\n  AxiosResponse<PaginatedResponseSearch>;\nexport type GetMostSoldApiV1RecommendationsMostSoldGetResult =\n  AxiosResponse<PaginatedResponseProduct>;\nexport type GetTopRatedApiV1RecommendationsTopRatedGetResult =\n  AxiosResponse<PaginatedResponseProduct>;\nexport type GetCategoryProductsApiV1RecommendationsCategoryCategoryGetResult =\n  AxiosResponse<PaginatedResponseProduct>;\nexport type GetRelatedCategoriesApiV1RecommendationsRelatedCategoriesCategoryGetResult =\n  AxiosResponse<PaginatedResponseCategory>;\nexport type GetAlsoBoughtApiV1RecommendationsAlsoBoughtProductIdGetResult =\n  AxiosResponse<PaginatedResponseProduct>;\nexport type GetSimilarProductsApiV1RecommendationsProductsProductIdSimilarGetResult =\n  AxiosResponse<PaginatedResponseProduct>;\nexport type InvalidateUserCacheApiV1RecommendationsInvalidateCacheUserIdPostResult =\n  AxiosResponse<unknown>;\nexport type InvalidateAccountCacheApiV1RecommendationsInvalidateCachePostResult =\n  AxiosResponse<unknown>;\nexport type QueryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPostResult =\n  AxiosResponse<PaginatedResponseProduct>;\nexport type GetRecommendationExplanationApiV1RecommendationsExplainUserIdItemIdGetResult =\n  AxiosResponse<DetailedExplanation>;\nexport type GetRecommendationExplanationExternalApiV1RecommendationsExplainExternalExternalUserIdExternalItemIdGetResult =\n  AxiosResponse<DetailedExplanation>;\nexport type GetSimilarProductsExternalApiV1RecommendationsProductsExternalExternalProductIdSimilarGetResult =\n  AxiosResponse<PaginatedResponseProduct>;\nexport type GetAlsoBoughtExternalApiV1RecommendationsAlsoBoughtExternalExternalProductIdGetResult =\n  AxiosResponse<PaginatedResponseProduct>;\nexport type GetConfidenceMetricsApiV1RecommendationsConfidenceMetricsGetResult =\n  AxiosResponse<GetConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet200>;\nexport type RollbackModelApiV1RecommendationsRollbackArtifactVersionPostResult =\n  AxiosResponse<unknown>;\nexport type GetRecommendationPerformanceApiV1RecommendationsPerformanceGetResult =\n  AxiosResponse<RecommendationPerformanceResponse>;\nexport type CreateInteractionApiV1InteractionsPostResult =\n  AxiosResponse<Interaction>;\nexport type ReadInteractionsApiV1InteractionsGetResult =\n  AxiosResponse<PaginatedResponseInteraction>;\nexport type CreateInteractionExternalApiV1InteractionsExternalPostResult =\n  AxiosResponse<Interaction>;\nexport type TrainModelsApiV1PipelineTrainPostResult =\n  AxiosResponse<TrainingResponse>;\nexport type GetTrainingStatusApiV1PipelineStatusGetResult =\n  AxiosResponse<TrainingStatus>;\nexport type GetTrainingJobStatusApiV1PipelineJobsJobIdStatusGetResult =\n  AxiosResponse<TrainingJobStatus>;\nexport type ListModelsApiV1PipelineModelsGetResult = AxiosResponse<\n  ModelMetadataResponse[]\n>;\nexport type GetModelMetricsApiV1PipelineModelsModelIdMetricsGetResult =\n  AxiosResponse<TrainingStatus>;\nexport type InvalidateCacheApiV1PipelineInvalidateCachePostResult =\n  AxiosResponse<unknown>;\nexport type TrainArtifactForAccountApiV1PipelineTrainAccountIdPostResult =\n  AxiosResponse<TrainArtifactForAccountApiV1PipelineTrainAccountIdPost202>;\nexport type ProcessTrainingJobApiV1PipelineProcessPostResult =\n  AxiosResponse<ProcessTrainingJobApiV1PipelineProcessPost200>;\nexport type TrainingCallbackApiV1PipelineCallbackJobIdPostResult =\n  AxiosResponse<TrainingCallbackApiV1PipelineCallbackJobIdPost200>;\nexport type ListTrainingJobsApiV1PipelineJobsGetResult = AxiosResponse<\n  TrainingJobStatus[]\n>;\nexport type TestCacheApiV1CacheTestCacheGetResult = AxiosResponse<unknown>;\nexport type CheckRedisApiV1CacheRedisHealthGetResult = AxiosResponse<unknown>;\nexport type GetAccountAnalyticsApiV1AnalyticsAnalyticsAccountGetResult =\n  AxiosResponse<GetAccountAnalyticsApiV1AnalyticsAnalyticsAccountGet200>;\nexport type GetEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGetResult =\n  AxiosResponse<\n    GetEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGet200Item[]\n  >;\nexport type GetRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGetResult =\n  AxiosResponse<RecommendationPerformanceResponse>;\nexport type CompareModelVersionsApiV1AnalyticsAnalyticsModelsCompareGetResult =\n  AxiosResponse<ModelComparisonResponse>;\nexport type GetMetricsHistoryApiV1AnalyticsAnalyticsMetricsHistoryGetResult =\n  AxiosResponse<MetricsHistoryResponse>;\nexport type CreateCheckoutSessionApiV1BillingCreateCheckoutSessionPostResult =\n  AxiosResponse<CreateCheckoutSessionResponse>;\nexport type CreatePortalSessionApiV1BillingCreatePortalSessionPostResult =\n  AxiosResponse<CreatePortalSessionResponse>;\nexport type MercadopagoWebhookApiV1BillingWebhookMercadopagoPostResult =\n  AxiosResponse<unknown>;\nexport type ListRolesApiV1RolesGetResult = AxiosResponse<Role[]>;\nexport type CreateRoleApiV1RolesPostResult = AxiosResponse<Role>;\nexport type GetRoleApiV1RolesRoleIdGetResult = AxiosResponse<Role>;\nexport type UpdateRoleApiV1RolesRoleIdPutResult = AxiosResponse<Role>;\nexport type DeleteRoleApiV1RolesRoleIdDeleteResult = AxiosResponse<void>;\nexport type GetRolePermissionsApiV1RolesRoleIdPermissionsGetResult =\n  AxiosResponse<Permission[]>;\nexport type AssignPermissionToRoleApiV1RolesRoleIdPermissionsPermissionIdPostResult =\n  AxiosResponse<unknown>;\nexport type RemovePermissionFromRoleApiV1RolesRoleIdPermissionsPermissionIdDeleteResult =\n  AxiosResponse<void>;\nexport type ListPermissionsApiV1PermissionsGetResult = AxiosResponse<\n  Permission[]\n>;\nexport type CreatePermissionApiV1PermissionsPostResult =\n  AxiosResponse<Permission>;\nexport type GetPermissionApiV1PermissionsPermissionIdGetResult =\n  AxiosResponse<Permission>;\nexport type GetRolesWithPermissionApiV1PermissionsPermissionIdRolesGetResult =\n  AxiosResponse<Role[]>;\nexport type CleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPostResult =\n  AxiosResponse<CleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPost200>;\nexport type CleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPostResult =\n  AxiosResponse<CleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPost200>;\nexport type GetTaskStatusApiV1MaintenanceMaintenanceTaskTaskIdGetResult =\n  AxiosResponse<GetTaskStatusApiV1MaintenanceMaintenanceTaskTaskIdGet200>;\nexport type ArchiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPostResult =\n  AxiosResponse<ArchiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPost200>;\nexport type ArchiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPostResult =\n  AxiosResponse<ArchiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPost200>;\nexport type ListArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGetResult =\n  AxiosResponse<ListArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGet200>;\nexport type CleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPostResult =\n  AxiosResponse<CleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPost200>;\nexport type GetSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGetResult =\n  AxiosResponse<GetSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGet200>;\nexport type MonitorTablesApiV1MaintenanceMaintenanceMonitorTablesPostResult =\n  AxiosResponse<MonitorTablesApiV1MaintenanceMaintenanceMonitorTablesPost200>;\nexport type GetSubscriptionUsageApiV1SubscriptionUsageGetResult =\n  AxiosResponse<GetSubscriptionUsageApiV1SubscriptionUsageGet200>;\nexport type GetStorageUsageApiV1StorageUsageGetResult =\n  AxiosResponse<GetStorageUsageApiV1StorageUsageGet200>;\nexport type RefreshStorageUsageApiV1StorageRefreshPostResult =\n  AxiosResponse<RefreshStorageUsageApiV1StorageRefreshPost200>;\nexport type BatchDataIngestionApiV1IngestionBatchPostResult =\n  AxiosResponse<BatchIngestionResponse>;\nexport type ListBatchJobsApiV1IngestionBatchGetResult = AxiosResponse<\n  BatchIngestionJobStatus[]\n>;\nexport type GetBatchJobStatusApiV1IngestionBatchJobIdGetResult =\n  AxiosResponse<BatchIngestionJobStatus>;\nexport type GetUsageSummaryApiV1UsageSummaryGetResult =\n  AxiosResponse<UsageSummaryResponse>;\nexport type GetUsageHistoryApiV1UsageGetResult = AxiosResponse<\n  UsageHistoryItemResponse[]\n>;\nexport type ListApiKeysApiV1ApiKeysGetResult =\n  AxiosResponse<ApiKeyListResponse>;\nexport type CreateApiKeyApiV1ApiKeysPostResult =\n  AxiosResponse<NewApiKeyResponse>;\nexport type RevokeApiKeyApiV1ApiKeysDeleteResult = AxiosResponse<void>;\nexport type GetCurrentApiKeyApiV1ApiKeysCurrentGetResult =\n  AxiosResponse<ApiKeyResponse>;\nexport type UpdateApiKeyApiV1ApiKeysApiKeyIdPutResult =\n  AxiosResponse<ApiKeyResponse>;\nexport type RevokeSpecificApiKeyApiV1ApiKeysApiKeyIdDeleteResult =\n  AxiosResponse<void>;\nexport type ResetSandboxDataApiV1SandboxResetPostResult =\n  AxiosResponse<unknown>;\nexport type GetSandboxStatusApiV1SandboxStatusGetResult =\n  AxiosResponse<unknown>;\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;;;;;;AACD;;AAkSO,MAAM,mEACX;IACE,YAAY;IACZ,QAAQ;IACR,WAAW;IACX,YAAY;AACd;AA2BK,MAAM,sDAAsD;IACjE,KAAK;IACL,MAAM;IACN,OAAO;AACT;AAqCO,MAAM,8DAA8D;IACzE,KAAK;IACL,MAAM;IACN,OAAO;AACT;AAgVO,MAAM,gBAAgB;IAC3B,KAAK;IACL,MAAM;AACR;AAuBO,MAAM,WAAW;IACtB,OAAO;IACP,QAAQ;IACR,QAAQ;AACV;AA6aO,MAAM,iBAAiB;IAC5B,MAAM;IACN,OAAO;IACP,QAAQ;IACR,OAAO;IACP,cAAc;IACd,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,WAAW;IACX,aAAa;IACb,aAAa;IACb,aAAa;IACb,kBAAkB;IAClB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,WAAW;IACX,aAAa;IACb,aAAa;IACb,aAAa;IACb,mBAAmB;IACnB,gBAAgB;IAChB,YAAY;IACZ,cAAc;IACd,cAAc;IACd,cAAc;IACd,mBAAmB;IACnB,qBAAqB;IACrB,qBAAqB;IACrB,qBAAqB;AACvB;AAqDO,MAAM,WAAW;IACtB,MAAM;IACN,gBAAgB;IAChB,UAAU;IACV,gBAAgB;IAChB,MAAM;IACN,UAAU;IACV,cAAc;AAChB;AAiPO,MAAM,kBAAkB;IAC7B,KAAK;IACL,IAAI;AACN;AAcO,MAAM,kBAAkB;IAC7B,MAAM;IACN,MAAM;IACN,UAAU;IACV,MAAM;IACN,QAAQ;IACR,UAAU;IACV,OAAO;IACP,QAAQ;IACR,UAAU;AACZ;AAoEO,MAAM,iBAAiB;IAC5B,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,KAAK;IACL,UAAU;IACV,YAAY;IACZ,UAAU;AACZ;AAiCO,MAAM,oBAAoB;IAC/B,eAAe;IACf,eAAe;IACf,mBAAmB;IACnB,cAAc;IACd,eAAe;IACf,iBAAiB;IACjB,oBAAoB;IACpB,oBAAoB;IACpB,sBAAsB;IACtB,UAAU;IACV,WAAW;IACX,UAAU;IACV,aAAa;AACf;AASO,MAAM,mBAAmB;IAC9B,QAAQ;IACR,UAAU;AACZ;AA4IO,MAAM,aAAa;IACxB,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,IAAI;IACJ,OAAO;AACT;AAqYO,MAAM,aAAa;IACxB;;GAEC,GACD,MAAM,uBAAuB,CAC3B;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE;IAC9B;IAEA;;;;CAID,GACC,MAAM,4BAA4B,CAGhC;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,EAAE;IACrC;IAEA;;;;CAID,GACC,MAAM,gCAAgC,CAGpC;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iBAAiB,CAAC,EAAE;IACxC;IAEA;;;;CAID,GACC,MAAM,oCAAoC,CAGxC;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,mBAAmB,CAAC,EAAE;IAC1C;IAEA;;;GAGC,GACD,MAAM,0DAA0D,CAG9D;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,oCAAoC,CAAC,EACtC,WACA;IAEJ;IAEA;;;GAGC,GACD,MAAM,qCAAqC,CACzC,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,yBAAyB,CAAC,EAAE;YAC5C,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;;GAGC,GACD,MAAM,gCAAgC,CAGpC,iBACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE,iBAAiB;IAC9D;IAEA;;;GAGC,GACD,MAAM,0BAA0B,CAC9B,cACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,EAAE,cAAc;IACxD;IAEA;;;GAGC,GACD,MAAM,4BAA4B,CAChC;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,mBAAmB,CAAC,EAAE,WAAW;IACtD;IAEA;;;GAGC,GACD,MAAM,+BAA+B,CAGnC;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iBAAiB,CAAC,EAAE;IACxC;IAEA;;;;;CAKD,GACC,MAAM,yCAAyC,CAG7C,eACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,yBAAyB,CAAC,EAAE,eAAe;IAChE;IAEA;;;GAGC,GACD,MAAM,sCAAsC,CAG1C,WACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,WAAW,EAAE;IACpD;IAEA;;GAEC,GACD,MAAM,yDAAyD,CAG7D,WACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,KAAK,CAChB,CAAC,iBAAiB,EAAE,UAAU,WAAW,CAAC,EAC1C,WACA;IAEJ;IAEA;;;GAGC,GACD,MAAM,qDAAqD,CAGzD,WACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,KAAK,CAChB,CAAC,iBAAiB,EAAE,UAAU,SAAS,CAAC,EACxC,WACA;IAEJ;IAEA;;;GAGC,GACD,MAAM,wCAAwC,CAG5C;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,wBAAwB,CAAC,EAAE;IAC/C;IAEA;;;;;;CAMD,GACC,MAAM,8CAA8C,CAGlD,eACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,wBAAwB,CAAC,EAAE,eAAe;IAC9D;IAEA;;;;;;CAMD,GACC,MAAM,+CAA+C,CAGnD,eACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,wBAAwB,CAAC,EAAE,eAAe;IAChE;IAEA;;;GAGC,GACD,MAAM,iDAAiD,CAGrD,WACA,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,UAAU,WAAW,CAAC,EAAE;YAC3D,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;;GAGC,GACD,MAAM,mCAAmC,CACvC;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,sBAAsB,CAAC,EAAE;IAC7C;IAEA;;;;;;CAMD,GACC,MAAM,iCAAiC,CAGrC;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,EAAE;IACrC;IAEA;;;;;;;CAOD,GACC,MAAM,0CAA0C,CAG9C;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,uBAAuB,CAAC,EAAE;IAC9C;IAEA;;;GAGC,GACD,MAAM,oCAAoC,CAGxC,kBACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,uBAAuB,CAAC,EAAE,kBAAkB;IAChE;IAEA;;;GAGC,GACD,MAAM,uCAAuC,CAC3C;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,uBAAuB,CAAC,EAAE;IACjD;IAEA;;;GAGC,GACD,MAAM,uCAAuC,CAG3C,kBACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE,kBAAkB;IAC/D;IAEA;;;GAGC,GACD,MAAM,yCAAyC,CAG7C,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ,EAAE;IACrD;IAEA;;;GAGC,GACD,MAAM,sCAAsC,CAC1C,YACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,2BAA2B,CAAC,EAAE,YAAY;IAC/D;IAEA;;;GAGC,GACD,MAAM,kDAAkD,CAGtD,QACA,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,qBAAqB,EAAE,OAAO,OAAO,EAAE,QAAQ,EAChD,WACA;IAEJ;IAEA;;;GAGC,GACD,MAAM,oDAAoD,CAGxD,QACA,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,MAAM,CACjB,CAAC,qBAAqB,EAAE,OAAO,OAAO,EAAE,QAAQ,EAChD;IAEJ;IAEA;;;GAGC,GACD,MAAM,6CAA6C,CAGjD,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,OAAO,MAAM,CAAC,EAAE;IAC3D;IAEA;;;GAGC,GACD,MAAM,yDAAyD,CAG7D,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,OAAO,YAAY,CAAC,EAAE;IACjE;IAEA;;GAEC,GACD,MAAM,iCAAiC,CACrC,eACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,EAAE,eAAe;IACzD;IAEA;;GAEC,GACD,MAAM,+BAA+B,CAGnC,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,kBAAkB,CAAC,EAAE;YACrC,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;;GAGC,GACD,MAAM,oCAAoC,CACxC,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ,EAAE;IAClD;IAEA;;GAEC,GACD,MAAM,iCAAiC,CACrC,eACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,EAAE,eAAe;IACxD;IAEA;;;GAGC,GACD,MAAM,+BAA+B,CAGnC,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iBAAiB,CAAC,EAAE;YACpC,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;;GAGC,GACD,MAAM,sCAAsC,CAC1C,WACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,WAAW,EAAE;IACpD;IAEA;;GAEC,GACD,MAAM,yCAAyC,CAG7C,WACA,eACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,WAAW,EAAE,eAAe;IACnE;IAEA;;;GAGC,GACD,MAAM,sDAAsD,CAG1D,WACA,iBACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,KAAK,CAChB,CAAC,iBAAiB,EAAE,UAAU,UAAU,CAAC,EACzC,iBACA;IAEJ;IAEA;;;GAGC,GACD,MAAM,2DAA2D,CAG/D,YACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,0BAA0B,EAAE,YAAY,EAAE;IAC9D;IAEA;;;GAGC,GACD,MAAM,8DAA8D,CAGlE,YACA,eACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CACd,CAAC,0BAA0B,EAAE,YAAY,EACzC,eACA;IAEJ;IAEA;;;GAGC,GACD,MAAM,iEAAiE,CAGrE,YACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,0BAA0B,EAAE,YAAY,EAAE;IACjE;IAEA;;;GAGC,GACD,MAAM,2EACJ,CACE,YACA,iBACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,KAAK,CAChB,CAAC,0BAA0B,EAAE,WAAW,UAAU,CAAC,EACnD,iBACA;IAEJ;IAEF;;GAEC,GACD,MAAM,qDAAqD,CAGzD,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,sCAAsC,CAAC,EAAE;YACzD,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;GAEC,GACD,MAAM,6DAA6D,CAGjE,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,0CAA0C,CAAC,EAAE;YAC7D,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;GAEC,GACD,MAAM,uDAAuD,CAG3D,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,uCAAuC,CAAC,EAAE;YAC1D,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;GAEC,GACD,MAAM,oEAAoE,CAGxE,WACA,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,yCAAyC,EAAE,WAAW,EAAE;YACxE,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;GAEC,GACD,MAAM,6CAA6C,CAGjD,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,kCAAkC,CAAC,EAAE;YACrD,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;GAEC,GACD,MAAM,6CAA6C,CAGjD,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,kCAAkC,CAAC,EAAE;YACrD,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;GAEC,GACD,MAAM,6DAA6D,CAGjE,UACA,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,UAAU,EAAE;YAC/D,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;GAEC,GACD,MAAM,uEAAuE,CAG3E,UACA,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,2CAA2C,EAAE,UAAU,EAAE;YACzE,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;GAEC,GACD,MAAM,0DAA0D,CAG9D,WACA,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,WAAW,EAAE;YACnE,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;;;;;;;;;;;;;;;;CAiBD,GACC,MAAM,oEAAoE,CAGxE,WACA,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,UAAU,QAAQ,CAAC,EAAE;YACxE,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;;;;;;;;;;CAWD,GACC,MAAM,mEAAmE,CAGvE,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,yCAAyC,EAAE,QAAQ,EACpD,WACA;IAEJ;IAEA;;;GAGC,GACD,MAAM,gEAAgE,CAGpE;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,wCAAwC,CAAC,EAC1C,WACA;IAEJ;IAEA;;;GAGC,GACD,MAAM,4EACJ,CACE,+EACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,0CAA0C,CAAC,EAC5C,+EACA;IAEJ;IAEF;;;;;;;;;;;;;;;;;;;;CAoBD,GACC,MAAM,yEACJ,CACE,QACA,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CACd,CAAC,gCAAgC,EAAE,OAAO,CAAC,EAAE,QAAQ,EACrD;IAEJ;IAEF;;;GAGC,GACD,MAAM,yGACJ,CACE,gBACA,gBACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CACd,CAAC,yCAAyC,EAAE,eAAe,CAAC,EAAE,gBAAgB,EAC9E;IAEJ;IAEF;;GAEC,GACD,MAAM,4FACJ,CACE,mBACA,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CACd,CAAC,0CAA0C,EAAE,kBAAkB,QAAQ,CAAC,EACxE;YACE,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IAEJ;IAEF;;GAEC,GACD,MAAM,kFACJ,CACE,mBACA,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CACd,CAAC,6CAA6C,EAAE,mBAAmB,EACnE;YACE,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IAEJ;IAEF;;;;;;;;;;;;CAYD,GACC,MAAM,+DAA+D,CAGnE;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,0CAA0C,CAAC,EAAE;IACjE;IAEA;;GAEC,GACD,MAAM,+DAA+D,CAGnE,iBACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,iCAAiC,EAAE,iBAAiB,EACrD,WACA;IAEJ;IAEA;;;;;;;;;;;;;;;;;;CAkBD,GACC,MAAM,iEAAiE,CAGrE,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,mCAAmC,CAAC,EAAE;YACtD,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;;GAGC,GACD,MAAM,yCAAyC,CAG7C,mBACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE,mBAAmB;IAChE;IAEA;;;GAGC,GACD,MAAM,uCAAuC,CAG3C,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,qBAAqB,CAAC,EAAE;YACxC,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;;GAGC,GACD,MAAM,yDAAyD,CAG7D,2BACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,6BAA6B,CAAC,EAC/B,2BACA;IAEJ;IAEA;;;;;;;;;;CAUD,GACC,MAAM,oCAAoC,CAGxC;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,sBAAsB,CAAC,EAAE,WAAW;IACzD;IAEA;;;;;;;CAOD,GACC,MAAM,0CAA0C,CAG9C;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,uBAAuB,CAAC,EAAE;IAC9C;IAEA;;;;;;;;;;;CAWD,GACC,MAAM,sDAAsD,CAG1D,OACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,sBAAsB,EAAE,MAAM,OAAO,CAAC,EAAE;IAC5D;IAEA;;;GAGC,GACD,MAAM,mCAAmC,CAGvC,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,uBAAuB,CAAC,EAAE;YAC1C,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;;GAGC,GACD,MAAM,sDAAsD,CAG1D,SACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,CAAC,EAAE;IACjE;IAEA;;;GAGC,GACD,MAAM,kDAAkD,CAGtD,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,iCAAiC,CAAC,EAAE,WAAW;YAChE,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;;;;;;;CAQD,GACC,MAAM,yDAAyD,CAG7D,WACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,uBAAuB,EAAE,WAAW,EACrC,WACA;IAEJ;IAEA;;;;CAID,GACC,MAAM,6CAA6C,CAGjD,gDACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,wBAAwB,CAAC,EAC1B,gDACA;IAEJ;IAEA;;;;CAID,GACC,MAAM,iDAAiD,CAGrD,OACA,oDACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,0BAA0B,EAAE,OAAO,EACpC,oDACA;IAEJ;IAEA;;;;;;;;;;;;;;;CAeD,GACC,MAAM,uCAAuC,CAG3C,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,qBAAqB,CAAC,EAAE;YACxC,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;GAEC,GACD,MAAM,kCAAkC,CACtC;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,wBAAwB,CAAC,EAAE;IAC/C;IAEA;;GAEC,GACD,MAAM,qCAAqC,CACzC;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,0BAA0B,CAAC,EAAE;IACjD;IAEA;;;;;;CAMD,GACC,MAAM,uDAAuD,CAG3D,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,mCAAmC,CAAC,EAAE;YACtD,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;;;;;;;CAQD,GACC,MAAM,0DAA0D,CAK9D,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,qCAAqC,CAAC,EAAE;YACxD,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;;;;;;;;;;;;;;;;;CAkBD,GACC,MAAM,kFACJ,CACE,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CACd,CAAC,sDAAsD,CAAC,EACxD;YACE,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IAEJ;IAEF;;;;;;;;;;;;;;;;;;CAkBD,GACC,MAAM,8DAA8D,CAGlE,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,0CAA0C,CAAC,EAAE;YAC7D,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;;;;;;;;;;;;;;;CAgBD,GACC,MAAM,4DAA4D,CAGhE,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,2CAA2C,CAAC,EAAE;YAC9D,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;;;;CAKD,GACC,MAAM,6DAA6D,CAGjE,8BACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,uCAAuC,CAAC,EACzC,8BACA;IAEJ;IAEA;;;;;CAKD,GACC,MAAM,yDAAyD,CAG7D,4BACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,qCAAqC,CAAC,EACvC,4BACA;IAEJ;IAEA;;;;;CAKD,GACC,MAAM,uDAAuD,CAG3D;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,mCAAmC,CAAC,EACrC,WACA;IAEJ;IAEA;;;;;CAKD,GACC,MAAM,yBAAyB,CAC7B;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,EAAE;IACrC;IAEA;;;;;CAKD,GACC,MAAM,2BAA2B,CAC/B,YACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE,YAAY;IAClD;IAEA;;;;;CAKD,GACC,MAAM,6BAA6B,CACjC,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ,EAAE;IAC9C;IAEA;;;;;CAKD,GACC,MAAM,gCAAgC,CACpC,QACA,YACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ,EAAE,YAAY;IAC1D;IAEA;;;;;CAKD,GACC,MAAM,mCAAmC,CACvC,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,QAAQ,EAAE;IACjD;IAEA;;;;;CAKD,GACC,MAAM,mDAAmD,CAGvD,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,YAAY,CAAC,EAAE;IAC1D;IAEA;;;;;CAKD,GACC,MAAM,oEAAoE,CAGxE,QACA,cACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,cAAc,EAAE,OAAO,aAAa,EAAE,cAAc,EACrD,WACA;IAEJ;IAEA;;;;;CAKD,GACC,MAAM,wEACJ,CACE,QACA,cACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,MAAM,CACjB,CAAC,cAAc,EAAE,OAAO,aAAa,EAAE,cAAc,EACrD;IAEJ;IAEF;;;;;CAKD,GACC,MAAM,qCAAqC,CAGzC;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,EAAE;IAC3C;IAEA;;;;;CAKD,GACC,MAAM,uCAAuC,CAG3C,kBACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE,kBAAkB;IAC9D;IAEA;;;;;CAKD,GACC,MAAM,+CAA+C,CAGnD,cACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,cAAc,EAAE;IAC1D;IAEA;;;;;CAKD,GACC,MAAM,6DAA6D,CAGjE,cACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,aAAa,MAAM,CAAC,EAAE;IAChE;IAEA;;;;;;;;;;;CAWD,GACC,MAAM,kEAAkE,CAGtE,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,kDAAkD,CAAC,EACpD,WACA;YACE,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IAEJ;IAEA;;;;;;;;;;;;CAYD,GACC,MAAM,wEACJ,CAGE,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,oDAAoD,CAAC,EACtD,WACA;YACE,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IAEJ;IAEF;;;;;;;;;CASD,GACC,MAAM,wDAAwD,CAG5D,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,qCAAqC,EAAE,QAAQ,EAAE;IACrE;IAEA;;;;;;;;;;;;;;;CAeD,GACC,MAAM,sFACJ,CAGE,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,8DAA8D,CAAC,EAChE,WACA;YACE,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IAEJ;IAEF;;;;;;;;;;;;;;;CAeD,GACC,MAAM,4FACJ,CAGE,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,gEAAgE,CAAC,EAClE,WACA;YACE,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IAEJ;IAEF;;;;;;;;;;;;CAYD,GACC,MAAM,wEACJ,CAGE,WACA,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CACd,CAAC,+CAA+C,EAAE,WAAW,EAC7D;YACE,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IAEJ;IAEF;;;;;;;;;;;;;;;CAeD,GACC,MAAM,oFACJ,CAGE,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,4DAA4D,CAAC,EAC9D,WACA;YACE,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IAEJ;IAEF;;;;;;;;;;CAUD,GACC,MAAM,4EACJ,CAGE,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CACd,CAAC,sDAAsD,CAAC,EACxD;YACE,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IAEJ;IAEF;;;;;;;;;CASD,GACC,MAAM,4DAA4D,CAGhE,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,8CAA8C,CAAC,EAChD,WACA;YACE,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IAEJ;IAEA;;;;;;;;;;CAUD,GACC,MAAM,gDAAgD,CAGpD;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,0BAA0B,CAAC,EAAE;IACjD;IAEA;;;;;;;;;CASD,GACC,MAAM,sCAAsC,CAG1C;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,qBAAqB,CAAC,EAAE;IAC5C;IAEA;;;;;;CAMD,GACC,MAAM,6CAA6C,CAGjD;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,EAAE,WAAW;IAC1D;IAEA;;;;;;;;;;CAUD,GACC,MAAM,4CAA4C,CAGhD,uBACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CACf,CAAC,uBAAuB,CAAC,EACzB,uBACA;IAEJ;IAEA;;;;;;;;;;;;;;;CAeD,GACC,MAAM,sCAAsC,CAG1C,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,uBAAuB,CAAC,EAAE;YAC1C,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;;;;;;;;;;;;;;;;;;CAmBD,GACC,MAAM,+CAA+C,CAGnD,OACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,OAAO,EAAE;IACvD;IAEA;;;GAGC,GACD,MAAM,sCAAsC,CAG1C;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,qBAAqB,CAAC,EAAE;IAC5C;IAEA;;;;;;;;;;CAUD,GACC,MAAM,+BAA+B,CAGnC,QACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,EAAE;YACjC,GAAG,OAAO;YACV,QAAQ;gBAAE,GAAG,MAAM;gBAAE,GAAG,SAAS,MAAM;YAAC;QAC1C;IACF;IAEA;;;;;;;;;CASD,GACC,MAAM,6BAA6B,CAGjC;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iBAAiB,CAAC,EAAE;IACxC;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;CAyBD,GACC,MAAM,+BAA+B,CAGnC,cACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,EAAE,cAAc;IACvD;IAEA;;;;CAID,GACC,MAAM,iCAAiC,CACrC;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,iBAAiB,CAAC,EAAE;IAC3C;IAEA;;;;;;;;;;CAUD,GACC,MAAM,yCAAyC,CAG7C;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,wBAAwB,CAAC,EAAE;IAC/C;IAEA;;;;;;;;;;;CAWD,GACC,MAAM,sCAAsC,CAG1C,UACA,cACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,UAAU,EAAE,cAAc;IACjE;IAEA;;;;;;;;;;;;CAYD,GACC,MAAM,iDAAiD,CAGrD,UACA;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,UAAU,EAAE;IACtD;IAEA;;;;;;;;CAQD,GACC,MAAM,wCAAwC,CAG5C;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE,WAAW;IACxD;IAEA;;;;;CAKD,GACC,MAAM,wCAAwC,CAG5C;QAEA,OAAO,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,sBAAsB,CAAC,EAAE;IAC7C;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1612, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/lib/generated/api-client.ts"], "sourcesContent": ["import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from \"axios\";\n// import { toast } from 'sonner'; // Comentado porque no se usa actualmente\n\n// Interfaces de error\nexport interface ApiErrorDetails {\n  field?: string;\n  message: string;\n  code?: string;\n}\n\nexport interface ApiErrorResponse {\n  message: string;\n  error_code: string;\n  details?: ApiErrorDetails[];\n  status_code: number;\n}\n\nexport class ApiError extends Error {\n  constructor(\n    message: string,\n    public status: number,\n    public errorCode: string,\n    public details?: ApiErrorDetails[] | null,\n  ) {\n    super(message);\n    this.name = \"ApiError\";\n  }\n\n  static isApiError(error: unknown): error is ApiError {\n    return error instanceof ApiError;\n  }\n\n  static fromResponse(response: ApiErrorResponse): ApiError {\n    return new ApiError(\n      response.message,\n      response.status_code,\n      response.error_code,\n      response.details,\n    );\n  }\n}\n\n// Obtén la URL base de las variables de entorno\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"\";\n\n// Set global axios baseURL so that generated getRayuela() calls use it as well\naxios.defaults.baseURL = API_BASE_URL;\n\n// Cliente Axios personalizado\nexport const customInstance = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    \"Content-Type\": \"application/json\",\n  },\n});\n\n// CustomRequestConfig no longer necesario; mantener por compatibilidad mínima\nexport interface CustomRequestConfig extends AxiosRequestConfig {}\n\n// Interceptor de solicitud para añadir cabeceras de autenticación\ncustomInstance.interceptors.request.use((config) => {\n  // Detect execution in browser\n  if (typeof window !== \"undefined\") {\n    const token = localStorage.getItem(\"rayuela-token\");\n    const apiKey = localStorage.getItem(\"rayuela-apiKey\");\n\n    config.headers = config.headers ?? {};\n\n    if (token) {\n      config.headers[\"Authorization\"] = `Bearer ${token}`;\n    }\n\n    if (\n      apiKey &&\n      !config.url?.includes(\"/auth/token\") &&\n      !config.url?.includes(\"/auth/register\")\n    ) {\n      config.headers[\"X-API-Key\"] = apiKey;\n    }\n  }\n\n  return config;\n});\n\n// Interceptor de respuesta para manejar errores\ncustomInstance.interceptors.response.use(\n  (response: AxiosResponse) => {\n    return response;\n  },\n  (error: AxiosError<ApiErrorResponse>) => {\n    if (error.response) {\n      const errorResponse = error.response.data;\n      throw ApiError.fromResponse(errorResponse);\n    } else if (error.request) {\n      // La solicitud se realizó pero no se recibió respuesta\n      throw new ApiError(\n        \"No se recibió respuesta del servidor\",\n        0,\n        \"NETWORK_ERROR\",\n        null,\n      );\n    } else {\n      // Error al configurar la solicitud\n      throw new ApiError(error.message, 0, \"REQUEST_ERROR\", null);\n    }\n  },\n);\n\nexport default customInstance;\n"], "names": [], "mappings": ";;;;;AA2CqB;AA3CrB;;AAiBO,MAAM,iBAAiB;;;;IAC5B,YACE,OAAe,EACf,AAAO,MAAc,EACrB,AAAO,SAAiB,EACxB,AAAO,OAAkC,CACzC;QACA,KAAK,CAAC,eAJC,SAAA,aACA,YAAA,gBACA,UAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;IAEA,OAAO,WAAW,KAAc,EAAqB;QACnD,OAAO,iBAAiB;IAC1B;IAEA,OAAO,aAAa,QAA0B,EAAY;QACxD,OAAO,IAAI,SACT,SAAS,OAAO,EAChB,SAAS,WAAW,EACpB,SAAS,UAAU,EACnB,SAAS,OAAO;IAEpB;AACF;AAEA,gDAAgD;AAChD,MAAM,eAAe,6DAAmC;AAExD,+EAA+E;AAC/E,wIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,OAAO,GAAG;AAGlB,MAAM,iBAAiB,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACzC,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAKA,kEAAkE;AAClE,eAAe,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC,8BAA8B;IAC9B,wCAAmC;QACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,MAAM,SAAS,aAAa,OAAO,CAAC;QAEpC,OAAO,OAAO,GAAG,OAAO,OAAO,IAAI,CAAC;QAEpC,IAAI,OAAO;YACT,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;QACrD;QAEA,IACE,UACA,CAAC,OAAO,GAAG,EAAE,SAAS,kBACtB,CAAC,OAAO,GAAG,EAAE,SAAS,mBACtB;YACA,OAAO,OAAO,CAAC,YAAY,GAAG;QAChC;IACF;IAEA,OAAO;AACT;AAEA,gDAAgD;AAChD,eAAe,YAAY,CAAC,QAAQ,CAAC,GAAG,CACtC,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE;QAClB,MAAM,gBAAgB,MAAM,QAAQ,CAAC,IAAI;QACzC,MAAM,SAAS,YAAY,CAAC;IAC9B,OAAO,IAAI,MAAM,OAAO,EAAE;QACxB,uDAAuD;QACvD,MAAM,IAAI,SACR,wCACA,GACA,iBACA;IAEJ,OAAO;QACL,mCAAmC;QACnC,MAAM,IAAI,SAAS,MAAM,OAAO,EAAE,GAAG,iBAAiB;IACxD;AACF;uCAGa", "debugId": null}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/lib/api.ts"], "sourcesContent": ["/**\r\n * Consolidated API client using generated code from OpenAPI spec\r\n * This file provides a clean interface over the generated API client\r\n */\r\n\r\n// Import generated API functions and types\r\nimport {\r\n    getRayuela,\r\n    // Types - these come directly from OpenAPI generation\r\n    RegisterRequest,\r\n    LoginResponse,\r\n    RegisterResponse,\r\n    SystemUserResponse,\r\n    AccountResponse,\r\n    UsageStats,\r\n    SrcApiV1EndpointsPlansPlanInfo,\r\n    ApiKeyResponse,\r\n    NewApiKeyResponse,\r\n    ApiKeyListResponse,\r\n    ApiKeyCreate,\r\n    ApiKeyUpdate,\r\n    UsageSummaryResponse,\r\n} from './generated/rayuelaAPI';\r\n\r\n// Import the consolidated ApiError from generated client\r\nimport { ApiError } from './generated/api-client';\r\n\r\n// Re-export ApiError for backward compatibility\r\nexport { ApiError };\r\n\r\n// Helper function to handle API errors consistently\r\nfunction handleApiError(error: unknown, defaultMessage: string): never {\r\n    // If it's already an ApiError from the interceptor, re-throw it\r\n    if (error instanceof ApiError) {\r\n        throw error;\r\n    }\r\n\r\n    // Handle Axios errors that weren't caught by interceptor\r\n    if (error && typeof error === 'object' && 'response' in error) {\r\n        const axiosError = error as { response?: { status?: number; data?: any }; message?: string };\r\n        const status = axiosError.response?.status || 500;\r\n        const message = axiosError.message || defaultMessage;\r\n        const errorCode = axiosError.response?.data?.error_code || 'UNKNOWN_ERROR';\r\n        const details = axiosError.response?.data?.details || null;\r\n\r\n        throw new ApiError(message, status, errorCode, details);\r\n    }\r\n\r\n    // Handle generic errors\r\n    if (error instanceof Error) {\r\n        throw new ApiError(error.message, 500, 'GENERIC_ERROR', null);\r\n    }\r\n\r\n    // Fallback for unknown error types\r\n    throw new ApiError(defaultMessage, 500, 'UNKNOWN_ERROR', null);\r\n}\r\n\r\n// Create a simple API client instance\r\nconst api = getRayuela();\r\n\r\n// --- Authentication Functions ---\r\n\r\nexport const loginUser = async (credentials: { email: string; password: string }): Promise<LoginResponse> => {\r\n    try {\r\n        return await api.loginApiV1AuthTokenPost(credentials);\r\n    } catch (error: unknown) {\r\n        handleApiError(error, 'Login failed');\r\n    }\r\n};\r\n\r\nexport const registerUser = async (\r\n    accountName: string,\r\n    email: string,\r\n    password: string\r\n): Promise<RegisterResponse> => {\r\n    try {\r\n        const registerRequest: RegisterRequest = {\r\n            accountName: accountName,\r\n            email,\r\n            password\r\n        };\r\n        // The backend returns RegisterResponse with proper camelCase fields\r\n        const response = await api.registerApiV1AuthRegisterPost(registerRequest);\r\n        return response;\r\n    } catch (error: unknown) {\r\n        handleApiError(error, 'Registration failed');\r\n    }\r\n};\r\n\r\nexport const logout = async (): Promise<unknown> => {\r\n    return api.logoutApiV1AuthLogoutPost();\r\n};\r\n\r\nexport const requestEmailVerification = async (): Promise<unknown> => {\r\n    return api.sendVerificationEmailApiV1AuthSendVerificationEmailPost();\r\n};\r\n\r\nexport const verifyEmail = async (token: string): Promise<unknown> => {\r\n    return api.verifyEmailApiV1AuthVerifyEmailGet({ token });\r\n};\r\n\r\n// --- User Functions ---\r\n\r\nexport const getCurrentUser = async (): Promise<SystemUserResponse> => {\r\n    try {\r\n        return await api.getCurrentUserInfoApiV1SystemUsersMeGet();\r\n    } catch (error: unknown) {\r\n        handleApiError(error, 'Failed to get current user');\r\n    }\r\n};\r\n\r\n// Alias for compatibility\r\nexport const getMe = getCurrentUser;\r\n\r\n// --- Account Functions ---\r\n\r\nexport const getCurrentAccount = async (): Promise<AccountResponse> => {\r\n    try {\r\n        // Authentication is handled by the client configuration\r\n        return await api.getAccountInfoApiV1AccountsCurrentGet();\r\n    } catch (error: unknown) {\r\n        handleApiError(error, 'Failed to get current account');\r\n    }\r\n};\r\n\r\n// Alias for compatibility\r\nexport const getMyAccount = getCurrentAccount;\r\n\r\nexport const getAccountUsage = async (): Promise<UsageStats> => {\r\n    try {\r\n        return await api.getApiUsageApiV1AccountsUsageGet();\r\n    } catch (error: unknown) {\r\n        handleApiError(error, 'Failed to get account usage');\r\n    }\r\n};\r\n\r\nexport const getPlans = async (): Promise<Record<string, SrcApiV1EndpointsPlansPlanInfo>> => {\r\n    try {\r\n        return await api.getAvailablePlansApiV1PlansGet();\r\n    } catch (error: unknown) {\r\n        handleApiError(error, 'Failed to get plans');\r\n    }\r\n};\r\n\r\n// Alias for compatibility\r\nexport const getAvailablePlans = getPlans;\r\n\r\n// --- Usage Functions ---\r\n\r\nexport const getUsageHistory = async (\r\n    startDate?: string,\r\n    endDate?: string\r\n): Promise<unknown[]> => {\r\n    const params: Record<string, string> = {};\r\n    if (startDate) params.start_date = startDate;\r\n    if (endDate) params.end_date = endDate;\r\n\r\n    return api.getUsageHistoryApiV1UsageGet(params);\r\n};\r\n\r\nexport const getUsageSummary = async (): Promise<UsageSummaryResponse> => {\r\n    try {\r\n        return await api.getUsageSummaryApiV1UsageSummaryGet();\r\n    } catch (error: unknown) {\r\n        handleApiError(error, 'Failed to get usage summary');\r\n    }\r\n};\r\n\r\n// --- API Key Functions ---\r\n\r\nexport const getApiKeys = async (): Promise<ApiKeyListResponse> => {\r\n    try {\r\n        return await api.listApiKeysApiV1ApiKeysGet();\r\n    } catch (error: unknown) {\r\n        handleApiError(error, 'Failed to get API keys');\r\n    }\r\n};\r\n\r\n// Alias for compatibility\r\nexport const listApiKeys = getApiKeys;\r\n\r\nexport const createApiKey = async (apiKeyData: { name: string; permissions?: string[] }): Promise<NewApiKeyResponse> => {\r\n    try {\r\n        const createRequest: ApiKeyCreate = {\r\n            name: apiKeyData.name\r\n        };\r\n        return await api.createApiKeyApiV1ApiKeysPost(createRequest);\r\n    } catch (error: unknown) {\r\n        handleApiError(error, 'Failed to create API key');\r\n    }\r\n};\r\n\r\nexport const deleteApiKey = async (keyId: string): Promise<void> => {\r\n    try {\r\n        await api.revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete(Number(keyId));\r\n    } catch (error: unknown) {\r\n        handleApiError(error, 'Failed to delete API key');\r\n    }\r\n};\r\n\r\nexport const updateApiKey = async (keyId: string, updateData: { name?: string; permissions?: string[] }): Promise<ApiKeyResponse> => {\r\n    try {\r\n        const updateRequest: ApiKeyUpdate = {\r\n            name: updateData.name\r\n        };\r\n        return await api.updateApiKeyApiV1ApiKeysApiKeyIdPut(Number(keyId), updateRequest);\r\n    } catch (error: unknown) {\r\n        handleApiError(error, 'Failed to update API key');\r\n    }\r\n};\r\n\r\nexport const revokeApiKey = async (apiKeyId: number): Promise<void> => {\r\n    return api.revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete(apiKeyId);\r\n};\r\n\r\nexport const getApiKey = async (): Promise<ApiKeyResponse> => {\r\n    return api.getCurrentApiKeyApiV1ApiKeysCurrentGet();\r\n};\r\n\r\nexport const revokeAllApiKeys = async (): Promise<void> => {\r\n    return api.revokeApiKeyApiV1ApiKeysDelete();\r\n};\r\n\r\n// --- Billing Functions ---\r\n\r\nexport const createCheckoutSession = async (priceId: string): Promise<unknown> => {\r\n    const request = { price_id: priceId };\r\n    return api.createCheckoutSessionApiV1BillingCreateCheckoutSessionPost(request);\r\n};\r\n\r\nexport const createBillingPortalSession = async (): Promise<unknown> => {\r\n    return api.createPortalSessionApiV1BillingCreatePortalSessionPost({});\r\n};\r\n\r\n// --- Health Functions ---\r\n\r\nexport const healthCheck = async (): Promise<{ status: string }> => {\r\n    try {\r\n        return await api.healthCheckHealthGet();\r\n    } catch (error: unknown) {\r\n        handleApiError(error, 'Health check failed');\r\n    }\r\n};\r\n\r\n// --- Analytics Functions ---\r\n\r\nexport const getRecommendationPerformance = async (\r\n    modelId?: number,\r\n    metricType?: string\r\n): Promise<unknown> => {\r\n    try {\r\n        const params: { model_id?: number; metric_type?: string; account_id?: number } = {};\r\n        if (modelId !== undefined) params.model_id = modelId;\r\n        if (metricType !== undefined) params.metric_type = metricType;\r\n        \r\n        return await api.getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet(params);\r\n    } catch (error: unknown) {\r\n        handleApiError(error, 'Failed to get recommendation performance');\r\n    }\r\n};\r\n\r\nexport const getConfidenceMetrics = async (): Promise<unknown> => {\r\n    try {\r\n        return await api.getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet();\r\n    } catch (error: unknown) {\r\n        handleApiError(error, 'Failed to get confidence metrics');\r\n    }\r\n};\r\n\r\n// --- Missing interfaces for proper API response handling ---\r\n\r\n// Remove manual interface definitions - they should come from generated types\r\n// export interface LoginResponse { ... }     // DELETE\r\n// export interface RegisterResponse { ... }  // DELETE\r\n\r\n// Export more types for compatibility\r\nexport type {\r\n    RegisterRequest,\r\n    SystemUserResponse,\r\n    AccountResponse,\r\n    UsageStats,\r\n    SrcApiV1EndpointsPlansPlanInfo as PlanInfo,\r\n    ApiKeyResponse,\r\n    NewApiKeyResponse,\r\n    ApiKeyListResponse,\r\n    ApiKeyCreate,\r\n    ApiKeyUpdate\r\n};\r\n\r\n// Re-export types for backward compatibility\r\nexport type ApiKey = ApiKeyResponse;\r\nexport type AccountInfo = AccountResponse;\r\n\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,2CAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAC3C;AAkBA,yDAAyD;AACzD;;;;AAKA,oDAAoD;AACpD,SAAS,eAAe,KAAc,EAAE,cAAsB;IAC1D,gEAAgE;IAChE,IAAI,iBAAiB,2IAAA,CAAA,WAAQ,EAAE;QAC3B,MAAM;IACV;IAEA,yDAAyD;IACzD,IAAI,SAAS,OAAO,UAAU,YAAY,cAAc,OAAO;QAC3D,MAAM,aAAa;QACnB,MAAM,SAAS,WAAW,QAAQ,EAAE,UAAU;QAC9C,MAAM,UAAU,WAAW,OAAO,IAAI;QACtC,MAAM,YAAY,WAAW,QAAQ,EAAE,MAAM,cAAc;QAC3D,MAAM,UAAU,WAAW,QAAQ,EAAE,MAAM,WAAW;QAEtD,MAAM,IAAI,2IAAA,CAAA,WAAQ,CAAC,SAAS,QAAQ,WAAW;IACnD;IAEA,wBAAwB;IACxB,IAAI,iBAAiB,OAAO;QACxB,MAAM,IAAI,2IAAA,CAAA,WAAQ,CAAC,MAAM,OAAO,EAAE,KAAK,iBAAiB;IAC5D;IAEA,mCAAmC;IACnC,MAAM,IAAI,2IAAA,CAAA,WAAQ,CAAC,gBAAgB,KAAK,iBAAiB;AAC7D;AAEA,sCAAsC;AACtC,MAAM,MAAM,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;AAId,MAAM,YAAY,OAAO;IAC5B,IAAI;QACA,OAAO,MAAM,IAAI,uBAAuB,CAAC;IAC7C,EAAE,OAAO,OAAgB;QACrB,eAAe,OAAO;IAC1B;AACJ;AAEO,MAAM,eAAe,OACxB,aACA,OACA;IAEA,IAAI;QACA,MAAM,kBAAmC;YACrC,aAAa;YACb;YACA;QACJ;QACA,oEAAoE;QACpE,MAAM,WAAW,MAAM,IAAI,6BAA6B,CAAC;QACzD,OAAO;IACX,EAAE,OAAO,OAAgB;QACrB,eAAe,OAAO;IAC1B;AACJ;AAEO,MAAM,SAAS;IAClB,OAAO,IAAI,yBAAyB;AACxC;AAEO,MAAM,2BAA2B;IACpC,OAAO,IAAI,uDAAuD;AACtE;AAEO,MAAM,cAAc,OAAO;IAC9B,OAAO,IAAI,kCAAkC,CAAC;QAAE;IAAM;AAC1D;AAIO,MAAM,iBAAiB;IAC1B,IAAI;QACA,OAAO,MAAM,IAAI,uCAAuC;IAC5D,EAAE,OAAO,OAAgB;QACrB,eAAe,OAAO;IAC1B;AACJ;AAGO,MAAM,QAAQ;AAId,MAAM,oBAAoB;IAC7B,IAAI;QACA,wDAAwD;QACxD,OAAO,MAAM,IAAI,qCAAqC;IAC1D,EAAE,OAAO,OAAgB;QACrB,eAAe,OAAO;IAC1B;AACJ;AAGO,MAAM,eAAe;AAErB,MAAM,kBAAkB;IAC3B,IAAI;QACA,OAAO,MAAM,IAAI,gCAAgC;IACrD,EAAE,OAAO,OAAgB;QACrB,eAAe,OAAO;IAC1B;AACJ;AAEO,MAAM,WAAW;IACpB,IAAI;QACA,OAAO,MAAM,IAAI,8BAA8B;IACnD,EAAE,OAAO,OAAgB;QACrB,eAAe,OAAO;IAC1B;AACJ;AAGO,MAAM,oBAAoB;AAI1B,MAAM,kBAAkB,OAC3B,WACA;IAEA,MAAM,SAAiC,CAAC;IACxC,IAAI,WAAW,OAAO,UAAU,GAAG;IACnC,IAAI,SAAS,OAAO,QAAQ,GAAG;IAE/B,OAAO,IAAI,4BAA4B,CAAC;AAC5C;AAEO,MAAM,kBAAkB;IAC3B,IAAI;QACA,OAAO,MAAM,IAAI,mCAAmC;IACxD,EAAE,OAAO,OAAgB;QACrB,eAAe,OAAO;IAC1B;AACJ;AAIO,MAAM,aAAa;IACtB,IAAI;QACA,OAAO,MAAM,IAAI,0BAA0B;IAC/C,EAAE,OAAO,OAAgB;QACrB,eAAe,OAAO;IAC1B;AACJ;AAGO,MAAM,cAAc;AAEpB,MAAM,eAAe,OAAO;IAC/B,IAAI;QACA,MAAM,gBAA8B;YAChC,MAAM,WAAW,IAAI;QACzB;QACA,OAAO,MAAM,IAAI,4BAA4B,CAAC;IAClD,EAAE,OAAO,OAAgB;QACrB,eAAe,OAAO;IAC1B;AACJ;AAEO,MAAM,eAAe,OAAO;IAC/B,IAAI;QACA,MAAM,IAAI,8CAA8C,CAAC,OAAO;IACpE,EAAE,OAAO,OAAgB;QACrB,eAAe,OAAO;IAC1B;AACJ;AAEO,MAAM,eAAe,OAAO,OAAe;IAC9C,IAAI;QACA,MAAM,gBAA8B;YAChC,MAAM,WAAW,IAAI;QACzB;QACA,OAAO,MAAM,IAAI,mCAAmC,CAAC,OAAO,QAAQ;IACxE,EAAE,OAAO,OAAgB;QACrB,eAAe,OAAO;IAC1B;AACJ;AAEO,MAAM,eAAe,OAAO;IAC/B,OAAO,IAAI,8CAA8C,CAAC;AAC9D;AAEO,MAAM,YAAY;IACrB,OAAO,IAAI,sCAAsC;AACrD;AAEO,MAAM,mBAAmB;IAC5B,OAAO,IAAI,8BAA8B;AAC7C;AAIO,MAAM,wBAAwB,OAAO;IACxC,MAAM,UAAU;QAAE,UAAU;IAAQ;IACpC,OAAO,IAAI,0DAA0D,CAAC;AAC1E;AAEO,MAAM,6BAA6B;IACtC,OAAO,IAAI,sDAAsD,CAAC,CAAC;AACvE;AAIO,MAAM,cAAc;IACvB,IAAI;QACA,OAAO,MAAM,IAAI,oBAAoB;IACzC,EAAE,OAAO,OAAgB;QACrB,eAAe,OAAO;IAC1B;AACJ;AAIO,MAAM,+BAA+B,OACxC,SACA;IAEA,IAAI;QACA,MAAM,SAA2E,CAAC;QAClF,IAAI,YAAY,WAAW,OAAO,QAAQ,GAAG;QAC7C,IAAI,eAAe,WAAW,OAAO,WAAW,GAAG;QAEnD,OAAO,MAAM,IAAI,+EAA+E,CAAC;IACrG,EAAE,OAAO,OAAgB;QACrB,eAAe,OAAO;IAC1B;AACJ;AAEO,MAAM,uBAAuB;IAChC,IAAI;QACA,OAAO,MAAM,IAAI,4DAA4D;IACjF,EAAE,OAAO,OAAgB;QACrB,eAAe,OAAO;IAC1B;AACJ", "debugId": null}}, {"offset": {"line": 1922, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 1941, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/button.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-body-sm font-medium disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none rayuela-focus-ring\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/95 rayuela-interactive rayuela-shadow-hover rayuela-scale-hover rayuela-active-press\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90 active:bg-destructive/95 rayuela-interactive rayuela-shadow-hover rayuela-scale-hover rayuela-active-press\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground active:bg-accent/80 rayuela-interactive rayuela-shadow-hover rayuela-scale-hover rayuela-active-press\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90 rayuela-interactive rayuela-shadow-hover rayuela-scale-hover rayuela-active-press\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground active:bg-accent/80 rayuela-interactive rayuela-scale-hover rayuela-active-press\",\n        link: \"text-primary underline-offset-4 hover:underline active:text-primary/80 rayuela-interactive rayuela-active-press\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-lg px-3 text-caption-lg\",\n        lg: \"h-11 rounded-lg px-8 text-body-lg\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,mRACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2009, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background rayuela-focus-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator className={cn(\"flex items-center justify-center text-current\")}>\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox } \n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iOACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;sBACzC,cAAA,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2061, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/auth/InitialApiKeyModal.tsx"], "sourcesContent": ["import { useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { Copy, Check } from 'lucide-react';\n\ninterface InitialApiKeyModalProps {\n  apiKey: string;\n  onContinue: (path: string) => void;\n}\n\nexport function InitialApiKeyModal({ apiKey, onContinue }: InitialApiKeyModalProps) {\n  const [copied, setCopied] = useState(false);\n  const [confirmed, setConfirmed] = useState(false);\n\n  const handleCopy = async () => {\n    await navigator.clipboard.writeText(apiKey);\n    setCopied(true);\n    setTimeout(() => setCopied(false), 2000);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <h2 className=\"text-2xl font-bold\">¡Tu primera API Key está lista!</h2>\n        <p className=\"text-muted-foreground mt-2\">\n          Esta es tu primera API Key. Puedes generar más claves para diferentes \n          entornos o equipos en la sección 'API Keys' de tu dashboard.\n        </p>\n      </div>\n\n      <div className=\"bg-muted p-4 rounded-lg\">\n        <div className=\"flex items-center justify-between\">\n          <code className=\"text-sm font-mono\">{apiKey}</code>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={handleCopy}\n            className=\"ml-2\"\n          >\n            {copied ? <Check className=\"h-4 w-4\" /> : <Copy className=\"h-4 w-4\" />}\n            {copied ? 'Copiado' : 'Copiar'}\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"bg-warning-light border border-warning/20 p-4 rounded-lg\">\n        <p className=\"text-sm text-warning-foreground\">\n          ⚠️ <strong>Importante:</strong> Esta clave no se mostrará de nuevo.\n          Si la pierdes, puedes generar una nueva en la sección API Keys.\n        </p>\n      </div>\n\n      {/* Email verification notice */}\n      <div className=\"bg-blue-50 border border-blue-200 p-4 rounded-lg\">\n        <p className=\"text-sm text-blue-800\">\n          📧 Antes de poder usar tu API Key, <strong>debes verificar tu dirección de correo electrónico</strong>.\n          Revisa tu bandeja de entrada y sigue el enlace de verificación. Hasta entonces, cualquier llamada a la API\n          devolverá un error <em>EmailNotVerifiedError</em>.\n        </p>\n      </div>\n\n      <div className=\"flex items-center space-x-2\">\n        <Checkbox \n          id=\"confirmed\" \n          checked={confirmed}\n          onCheckedChange={(val) => setConfirmed(val === true)}\n        />\n        <label htmlFor=\"confirmed\" className=\"text-sm\">\n          He guardado mi API Key de forma segura\n        </label>\n      </div>\n\n      <div className=\"flex gap-3\">\n        <Button \n          onClick={() => onContinue('/dashboard/api-keys')}\n          disabled={!confirmed}\n          className=\"flex-1\"\n        >\n          Ir a Gestión de API Keys\n        </Button>\n        <Button \n          variant=\"outline\"\n          onClick={() => onContinue('/dashboard')}\n          disabled={!confirmed}\n          className=\"flex-1\"\n        >\n          Continuar al Dashboard\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;;;;;;;AAOO,SAAS,mBAAmB,EAAE,MAAM,EAAE,UAAU,EAA2B;;IAChF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,aAAa;QACjB,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,UAAU;QACV,WAAW,IAAM,UAAU,QAAQ;IACrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAM5C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAAqB;;;;;;sCACrC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;gCAET,uBAAS,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCACzD,SAAS,YAAY;;;;;;;;;;;;;;;;;;0BAK5B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAAkC;sCAC1C,6LAAC;sCAAO;;;;;;wBAAoB;;;;;;;;;;;;0BAMnC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAAwB;sCACA,6LAAC;sCAAO;;;;;;wBAA2D;sCAEnF,6LAAC;sCAAG;;;;;;wBAA0B;;;;;;;;;;;;0BAIrD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uIAAA,CAAA,WAAQ;wBACP,IAAG;wBACH,SAAS;wBACT,iBAAiB,CAAC,MAAQ,aAAa,QAAQ;;;;;;kCAEjD,6LAAC;wBAAM,SAAQ;wBAAY,WAAU;kCAAU;;;;;;;;;;;;0BAKjD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,WAAW;wBAC1B,UAAU,CAAC;wBACX,WAAU;kCACX;;;;;;kCAGD,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS,IAAM,WAAW;wBAC1B,UAAU,CAAC;wBACX,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;GAjFgB;KAAA", "debugId": null}}, {"offset": {"line": 2298, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/lib/auth.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport {\n  getMe,\n  loginUser,\n  registerUser,\n  ApiError,\n  logout as logoutApi,\n  requestEmailVerification\n} from './api';\nimport { toast } from \"sonner\";\nimport { InitialApiKeyModal } from '@/components/auth/InitialApiKeyModal';\n\n// Interfaz para los datos del usuario que obtendremos de /system-users/me\ninterface User {\n  id: number;\n  email: string;\n  account_id: number;\n  is_admin: boolean;\n  is_active: boolean;\n  email_verified?: boolean; // Nuevo campo opcional para seguimiento de verificación\n}\n\ninterface AuthContextType {\n  user: User | null;\n  token: string | null; // Token JWT del SystemUser\n  apiKey: string | null; // API Key de la cuenta (para identificar tenant)\n  setApiKey: (apiKey: string) => void; // Función para actualizar la API Key\n  login: (email: string, password: string) => Promise<boolean>; // Devuelve true/false para éxito/fallo\n  register: (accountName: string, email: string, password: string) => Promise<{ success: boolean; apiKey?: string; error?: unknown }>; // Registra un nuevo usuario\n  logout: () => void;\n  isLoading: boolean; // Estado de carga inicial y durante login/fetch\n  emailVerificationError: {\n    email: string;\n    password: string;\n    message: string;\n  } | null; // Error de verificación de email\n  requestNewVerificationEmail: () => Promise<boolean>; // Función para solicitar un nuevo email de verificación\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// --- AuthProvider ---\nexport const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [token, setToken] = useState<string | null>(null);\n  const [apiKey, setApiKey] = useState<string | null>(null); // API Key de la cuenta\n  const [isLoading, setIsLoading] = useState(true); // Carga inicial\n  const [showApiKeyModal, setShowApiKeyModal] = useState(false); // Estado para el modal\n  const [initialApiKeyToShow, setInitialApiKeyToShow] = useState<string | null>(null); // Key a mostrar\n  const router = useRouter();\n  const pathname = usePathname();\n\n  // Estado para manejar el error de email no verificado\n  const [emailVerificationError, setEmailVerificationError] = useState<{\n    email: string;\n    password: string;\n    message: string;\n  } | null>(null);\n\n  // Función para limpiar el estado y localStorage\n  const clearAuthData = useCallback(() => {\n    localStorage.removeItem('rayuela-token');\n    localStorage.removeItem('rayuela-apiKey');\n    setUser(null);\n    setToken(null);\n    setApiKey(null);\n  }, []);\n\n  // Función para validar token y obtener datos del usuario\n  const fetchUserData = useCallback(async (currentToken: string, currentApiKey?: string | null) => {\n    setIsLoading(true);\n    try {\n      // Llama a /system-users/me usando el token (y opcionalmente la API Key)\n      const userData = await getMe();\n\n      if (!userData.is_active) {\n        throw new Error(\"User account is inactive.\");\n      }\n\n      setUser(userData);\n      setToken(currentToken);\n      if (currentApiKey) {\n        setApiKey(currentApiKey);\n      }\n      console.log(\"User data fetched successfully:\", userData);\n      return true; // Indica éxito\n\n    } catch (error: unknown) {\n      console.error(\"Token validation/fetch user data failed:\", error);\n      clearAuthData(); // Limpiar datos si el token es inválido o hay error\n      // Si estamos en una ruta protegida y falla la validación, redirigir a login\n      if (pathname?.startsWith('/dashboard')) { // Ajusta '/dashboard' si tu ruta es diferente\n        toast.error(\"Tu sesión ha expirado o es inválida. Por favor, inicia sesión de nuevo.\");\n        router.push('/login');\n      }\n      return false; // Indica fallo\n    } finally {\n      setIsLoading(false);\n    }\n  }, [router, pathname, clearAuthData]);\n\n  // Efecto para cargar estado inicial desde localStorage y validar\n  useEffect(() => {\n    console.log(\"AuthProvider Mounted. Checking localStorage...\");\n    const storedToken = localStorage.getItem('rayuela-token');\n    const storedApiKey = localStorage.getItem('rayuela-apiKey');\n    if (storedToken && storedApiKey) {\n      console.log(\"Found token and apiKey in localStorage. Validating...\");\n      fetchUserData(storedToken, storedApiKey);\n    } else {\n      console.log(\"No token or apiKey found in localStorage.\");\n      setIsLoading(false); // No hay token, terminamos la carga inicial\n    }\n  }, [fetchUserData]);\n\n  // Función para solicitar un nuevo email de verificación\n  const requestNewVerificationEmail = useCallback(async () => {\n    if (!token) {\n      toast.error(\"No hay sesión activa. Por favor, inicia sesión de nuevo.\");\n      return false;\n    }\n\n    try {\n      await requestEmailVerification();\n      toast.success(\"Email de verificación enviado. Por favor, revisa tu bandeja de entrada.\");\n      return true;\n    } catch (error) {\n      console.error(\"Error al solicitar email de verificación:\", error);\n      if (error instanceof ApiError) {\n        toast.error(error.message || \"Error al solicitar email de verificación.\");\n      } else {\n        toast.error(\"Error inesperado al solicitar email de verificación\");\n      }\n      return false;\n    }\n  }, [token]);\n\n  // Función de Login\n  const login = useCallback(async (email: string, password: string): Promise<boolean> => {\n    setIsLoading(true);\n    // Limpiar cualquier error previo de verificación de email\n    setEmailVerificationError(null);\n\n    try {\n      // Preparar los datos para la API\n      const credentials = { email, password };\n\n      // Llamar directamente a la API de login\n      const response = await loginUser(credentials);\n\n      const accessTokenRaw = (response as any).access_token ?? response.accessToken;\n      if (accessTokenRaw) {\n        // Guardar token en localStorage\n        localStorage.setItem('rayuela-token', accessTokenRaw);\n        setToken(accessTokenRaw);\n\n        // El endpoint /auth/token solo devuelve JWT, no API Key\n        // Intentar cargar API Key existente de localStorage\n        const storedApiKey = localStorage.getItem('rayuela-apiKey');\n        if (storedApiKey) {\n          setApiKey(storedApiKey);\n        }\n\n        // Validar el token y obtener datos del usuario (con o sin API Key)\n        const fetchSuccess = await fetchUserData(accessTokenRaw, storedApiKey);\n\n        if (fetchSuccess) {\n          toast.success(\"Login exitoso!\");\n          router.push('/dashboard'); // Redirigir al dashboard\n          return true; // Login exitoso\n        } else {\n          // fetchUserData ya manejó el error y limpió el estado\n          return false; // Login fallido (validación post-login falló)\n        }\n      } else {\n        throw new Error(\"No se recibió token de acceso.\");\n      }\n    } catch (error: unknown) {\n      console.error(\"Login processing failed:\", error);\n\n              // Verificar si es un error de email no verificado\n        if (error && typeof error === 'object' && 'error_code' in error) {\n          const apiError = error as { error_code: string; message?: string };\n          if (apiError.error_code === \"EMAIL_NOT_VERIFIED\") {\n            // Guardar la información para poder reenviar el email de verificación\n            setEmailVerificationError({\n              email,\n              password,\n              message: apiError.message || \"Por favor, verifica tu email para continuar.\"\n            });\n\n            // No limpiar los datos de autenticación, ya que necesitamos el token para solicitar un nuevo email\n            setIsLoading(false);\n            return false;\n          }\n        }\n\n      // Para otros errores, mostrar mensaje y limpiar datos\n      const errorMessage = error && typeof error === 'object' && 'message' in error ? \n        (error as { message: string }).message : \"Error al iniciar sesión. Verifica tus credenciales.\";\n      toast.error(errorMessage);\n      clearAuthData(); // Asegurarse de limpiar en caso de error\n      setIsLoading(false); // Terminar carga en caso de error\n      return false; // Login fallido\n    }\n  }, [fetchUserData, router, clearAuthData]);\n\n  // Función de registro\n  const register = useCallback(async (accountName: string, email: string, password: string) => {\n    setIsLoading(true);\n\n    try {\n      // Llamar a la API de registro\n      const response = await registerUser(accountName, email, password);\n\n      const accessTokenRaw = (response as any).access_token ?? response.accessToken;\n      if (accessTokenRaw) {\n        // CRITICAL FIX: Extract the API key from the registration response\n        // Backend now returns apiKey in camelCase due to CamelCaseModel\n        const apiKey = response.apiKey;\n        const accessToken = accessTokenRaw;\n\n        // Store both token and API key\n        localStorage.setItem('rayuela-token', accessToken);\n        localStorage.setItem('rayuela-apiKey', apiKey);\n\n        // Set both in state\n        setToken(accessToken);\n        setApiKey(apiKey);\n\n        // Show the API key modal immediately\n        setInitialApiKeyToShow(apiKey);\n        setShowApiKeyModal(true);\n\n        // Validar el token y obtener datos del usuario\n        await fetchUserData(accessToken, apiKey);\n\n        // Return success with the API key\n        return {\n          success: true,\n          apiKey: apiKey\n        };\n      } else {\n        throw new Error(\"No se recibió token de acceso\");\n      }\n    } catch (error: unknown) {\n      console.error(\"Register processing failed:\", error);\n\n      // Mostrar mensaje de error\n      if (error instanceof ApiError) {\n        toast.error(error.message || \"Error al registrarse\");\n      } else {\n        toast.error(\"Error inesperado al registrarse\");\n      }\n\n      // Limpiar datos\n      clearAuthData();\n\n      return { success: false, error };\n    } finally {\n      setIsLoading(false);\n    }\n  }, [fetchUserData, clearAuthData]);\n\n  // Función para manejar el cierre del modal de API Key\n  const handleModalClose = useCallback(async () => {\n    // Guardar los valores actuales para evitar problemas de concurrencia\n    const currentToken = token;\n    const currentApiKey = apiKey;\n\n    // Ocultar el modal inmediatamente para mejorar UX\n    setShowApiKeyModal(false);\n    setInitialApiKeyToShow(null);\n\n    // Mostrar toast de carga\n    const loadingToast = toast.loading(\"Configurando tu cuenta...\");\n    setIsLoading(true);\n\n    try {\n      // Validar que tenemos los datos necesarios\n      if (!currentToken || !currentApiKey) {\n        throw new ApiError(\n          \"Token o API Key no disponibles\",\n          401,\n          \"AUTH_REQUIRED\"\n        );\n      }\n\n      // Intentar obtener los datos del usuario con reintentos\n      let fetchSuccess = false;\n      let attempts = 0;\n      const maxAttempts = 3;\n      let lastError: ApiError | null = null;\n\n      while (!fetchSuccess && attempts < maxAttempts) {\n        try {\n          await fetchUserData(currentToken, currentApiKey);\n          fetchSuccess = true;\n        } catch (e) {\n          lastError = e as ApiError;\n          attempts++;\n          if (attempts < maxAttempts) {\n            // Esperar 500ms antes de reintentar\n            await new Promise(resolve => setTimeout(resolve, 500));\n          }\n        }\n      }\n\n      if (!fetchSuccess && lastError) {\n        throw lastError;\n      }\n\n      // Si llegamos aquí, es que todo salió bien\n      toast.dismiss(loadingToast);\n      toast.success(\"¡Cuenta configurada correctamente!\");\n      router.push('/dashboard');\n\n    } catch (error) {\n      toast.dismiss(loadingToast);\n      if (error instanceof ApiError) {\n        toast.error(error.message || \"Error al inicializar la cuenta\");\n      } else {\n        toast.error(\"Error inesperado al configurar la cuenta\");\n      }\n      // No hacer logout aquí, dejar que el usuario intente de nuevo\n    } finally {\n      setIsLoading(false);\n    }\n  }, [token, apiKey, fetchUserData, router]);\n\n  // Función Logout\n  const logout = useCallback(async () => {\n    try {\n      // Solo llamar a la API si hay token\n      if (token) {\n        await logoutApi();\n      }\n\n      // Limpiar estado y localStorage\n      clearAuthData();\n\n      // Redirigir al login\n      router.push('/login');\n      toast.success(\"Sesión cerrada correctamente\");\n    } catch (error) {\n      console.error(\"Logout error:\", error);\n      // Limpiar de todas formas\n      clearAuthData();\n      router.push('/login');\n    }\n  }, [token, clearAuthData, router]);\n\n  // Renderizado del proveedor de contexto\n  return (\n    <AuthContext.Provider\n      value={{\n        user,\n        token,\n        apiKey,\n        setApiKey,\n        login,\n        register,\n        logout,\n        isLoading,\n        emailVerificationError,\n        requestNewVerificationEmail,\n      }}\n    >\n      {children}\n\n      {/* Modal para mostrar la API Key inicial */}\n      {showApiKeyModal && initialApiKeyToShow && (\n        <InitialApiKeyModal\n          apiKey={initialApiKeyToShow}\n          onContinue={handleModalClose}\n        />\n      )}\n    </AuthContext.Provider>\n  );\n};\n\n// Hook para usar el contexto de autenticación\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}; \n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAQA;AACA;;;AAbA;;;;;;AA0CA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAGxD,MAAM,eAAkD,CAAC,EAAE,QAAQ,EAAE;;IAC1E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,OAAO,uBAAuB;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,gBAAgB;IAClE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,uBAAuB;IACtF,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,OAAO,gBAAgB;IACrG,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,sDAAsD;IACtD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAIzD;IAEV,gDAAgD;IAChD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAChC,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,QAAQ;YACR,SAAS;YACT,UAAU;QACZ;kDAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,OAAO,cAAsB;YAC7D,aAAa;YACb,IAAI;gBACF,wEAAwE;gBACxE,MAAM,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,QAAK,AAAD;gBAE3B,IAAI,CAAC,SAAS,SAAS,EAAE;oBACvB,MAAM,IAAI,MAAM;gBAClB;gBAEA,QAAQ;gBACR,SAAS;gBACT,IAAI,eAAe;oBACjB,UAAU;gBACZ;gBACA,QAAQ,GAAG,CAAC,mCAAmC;gBAC/C,OAAO,MAAM,eAAe;YAE9B,EAAE,OAAO,OAAgB;gBACvB,QAAQ,KAAK,CAAC,4CAA4C;gBAC1D,iBAAiB,oDAAoD;gBACrE,4EAA4E;gBAC5E,IAAI,UAAU,WAAW,eAAe;oBACtC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO,IAAI,CAAC;gBACd;gBACA,OAAO,OAAO,eAAe;YAC/B,SAAU;gBACR,aAAa;YACf;QACF;kDAAG;QAAC;QAAQ;QAAU;KAAc;IAEpC,iEAAiE;IACjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,QAAQ,GAAG,CAAC;YACZ,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,eAAe,cAAc;gBAC/B,QAAQ,GAAG,CAAC;gBACZ,cAAc,aAAa;YAC7B,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,aAAa,QAAQ,4CAA4C;YACnE;QACF;iCAAG;QAAC;KAAc;IAElB,wDAAwD;IACxD,MAAM,8BAA8B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE;YAC9C,IAAI,CAAC,OAAO;gBACV,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;YAEA,IAAI;gBACF,MAAM,CAAA,GAAA,oIAAA,CAAA,2BAAwB,AAAD;gBAC7B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6CAA6C;gBAC3D,IAAI,iBAAiB,2IAAA,CAAA,WAAQ,EAAE;oBAC7B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;gBAC/B,OAAO;oBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;gBACA,OAAO;YACT;QACF;gEAAG;QAAC;KAAM;IAEV,mBAAmB;IACnB,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2CAAE,OAAO,OAAe;YAC9C,aAAa;YACb,0DAA0D;YAC1D,0BAA0B;YAE1B,IAAI;gBACF,iCAAiC;gBACjC,MAAM,cAAc;oBAAE;oBAAO;gBAAS;gBAEtC,wCAAwC;gBACxC,MAAM,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,YAAS,AAAD,EAAE;gBAEjC,MAAM,iBAAiB,AAAC,SAAiB,YAAY,IAAI,SAAS,WAAW;gBAC7E,IAAI,gBAAgB;oBAClB,gCAAgC;oBAChC,aAAa,OAAO,CAAC,iBAAiB;oBACtC,SAAS;oBAET,wDAAwD;oBACxD,oDAAoD;oBACpD,MAAM,eAAe,aAAa,OAAO,CAAC;oBAC1C,IAAI,cAAc;wBAChB,UAAU;oBACZ;oBAEA,mEAAmE;oBACnE,MAAM,eAAe,MAAM,cAAc,gBAAgB;oBAEzD,IAAI,cAAc;wBAChB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBACd,OAAO,IAAI,CAAC,eAAe,yBAAyB;wBACpD,OAAO,MAAM,gBAAgB;oBAC/B,OAAO;wBACL,sDAAsD;wBACtD,OAAO,OAAO,8CAA8C;oBAC9D;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAgB;gBACvB,QAAQ,KAAK,CAAC,4BAA4B;gBAElC,kDAAkD;gBACxD,IAAI,SAAS,OAAO,UAAU,YAAY,gBAAgB,OAAO;oBAC/D,MAAM,WAAW;oBACjB,IAAI,SAAS,UAAU,KAAK,sBAAsB;wBAChD,sEAAsE;wBACtE,0BAA0B;4BACxB;4BACA;4BACA,SAAS,SAAS,OAAO,IAAI;wBAC/B;wBAEA,mGAAmG;wBACnG,aAAa;wBACb,OAAO;oBACT;gBACF;gBAEF,sDAAsD;gBACtD,MAAM,eAAe,SAAS,OAAO,UAAU,YAAY,aAAa,QACtE,AAAC,MAA8B,OAAO,GAAG;gBAC3C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,iBAAiB,yCAAyC;gBAC1D,aAAa,QAAQ,kCAAkC;gBACvD,OAAO,OAAO,gBAAgB;YAChC;QACF;0CAAG;QAAC;QAAe;QAAQ;KAAc;IAEzC,sBAAsB;IACtB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,OAAO,aAAqB,OAAe;YACtE,aAAa;YAEb,IAAI;gBACF,8BAA8B;gBAC9B,MAAM,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAAE,aAAa,OAAO;gBAExD,MAAM,iBAAiB,AAAC,SAAiB,YAAY,IAAI,SAAS,WAAW;gBAC7E,IAAI,gBAAgB;oBAClB,mEAAmE;oBACnE,gEAAgE;oBAChE,MAAM,SAAS,SAAS,MAAM;oBAC9B,MAAM,cAAc;oBAEpB,+BAA+B;oBAC/B,aAAa,OAAO,CAAC,iBAAiB;oBACtC,aAAa,OAAO,CAAC,kBAAkB;oBAEvC,oBAAoB;oBACpB,SAAS;oBACT,UAAU;oBAEV,qCAAqC;oBACrC,uBAAuB;oBACvB,mBAAmB;oBAEnB,+CAA+C;oBAC/C,MAAM,cAAc,aAAa;oBAEjC,kCAAkC;oBAClC,OAAO;wBACL,SAAS;wBACT,QAAQ;oBACV;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAgB;gBACvB,QAAQ,KAAK,CAAC,+BAA+B;gBAE7C,2BAA2B;gBAC3B,IAAI,iBAAiB,2IAAA,CAAA,WAAQ,EAAE;oBAC7B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;gBAC/B,OAAO;oBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;gBAEA,gBAAgB;gBAChB;gBAEA,OAAO;oBAAE,SAAS;oBAAO;gBAAM;YACjC,SAAU;gBACR,aAAa;YACf;QACF;6CAAG;QAAC;QAAe;KAAc;IAEjC,sDAAsD;IACtD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YACnC,qEAAqE;YACrE,MAAM,eAAe;YACrB,MAAM,gBAAgB;YAEtB,kDAAkD;YAClD,mBAAmB;YACnB,uBAAuB;YAEvB,yBAAyB;YACzB,MAAM,eAAe,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACnC,aAAa;YAEb,IAAI;gBACF,2CAA2C;gBAC3C,IAAI,CAAC,gBAAgB,CAAC,eAAe;oBACnC,MAAM,IAAI,2IAAA,CAAA,WAAQ,CAChB,kCACA,KACA;gBAEJ;gBAEA,wDAAwD;gBACxD,IAAI,eAAe;gBACnB,IAAI,WAAW;gBACf,MAAM,cAAc;gBACpB,IAAI,YAA6B;gBAEjC,MAAO,CAAC,gBAAgB,WAAW,YAAa;oBAC9C,IAAI;wBACF,MAAM,cAAc,cAAc;wBAClC,eAAe;oBACjB,EAAE,OAAO,GAAG;wBACV,YAAY;wBACZ;wBACA,IAAI,WAAW,aAAa;4BAC1B,oCAAoC;4BACpC,MAAM,IAAI;8EAAQ,CAAA,UAAW,WAAW,SAAS;;wBACnD;oBACF;gBACF;gBAEA,IAAI,CAAC,gBAAgB,WAAW;oBAC9B,MAAM;gBACR;gBAEA,2CAA2C;gBAC3C,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;YAEd,EAAE,OAAO,OAAO;gBACd,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,IAAI,iBAAiB,2IAAA,CAAA,WAAQ,EAAE;oBAC7B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;gBAC/B,OAAO;oBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;YACA,8DAA8D;YAChE,SAAU;gBACR,aAAa;YACf;QACF;qDAAG;QAAC;QAAO;QAAQ;QAAe;KAAO;IAEzC,iBAAiB;IACjB,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE;YACzB,IAAI;gBACF,oCAAoC;gBACpC,IAAI,OAAO;oBACT,MAAM,CAAA,GAAA,oIAAA,CAAA,SAAS,AAAD;gBAChB;gBAEA,gCAAgC;gBAChC;gBAEA,qBAAqB;gBACrB,OAAO,IAAI,CAAC;gBACZ,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;gBAC/B,0BAA0B;gBAC1B;gBACA,OAAO,IAAI,CAAC;YACd;QACF;2CAAG;QAAC;QAAO;QAAe;KAAO;IAEjC,wCAAwC;IACxC,qBACE,6LAAC,YAAY,QAAQ;QACnB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;;YAEC;YAGA,mBAAmB,qCAClB,6LAAC,mJAAA,CAAA,qBAAkB;gBACjB,QAAQ;gBACR,YAAY;;;;;;;;;;;;AAKtB;GAjVa;;QAOI,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KARjB;AAoVN,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}]}