{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/lib/auth';\n\nexport default function RootPage() {\n  const { user, isLoading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!isLoading) {\n      if (user) {\n        // If user is authenticated, redirect to dashboard\n        router.push('/dashboard');\n      } else {\n        // If user is not authenticated, redirect to public home\n        router.push('/home');\n      }\n    }\n  }, [user, isLoading, router]);\n\n  // Show a loading state while checking authentication\n  return (\n    <div className=\"flex items-center justify-center min-h-screen\">\n      <div className=\"text-center\">\n        <h1 className=\"text-2xl font-bold mb-4\">Loading...</h1>\n        <p>Please wait while we redirect you to the appropriate page.</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,UAAO,AAAD;IAClC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;YACd,IAAI,MAAM;gBACR,kDAAkD;gBAClD,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,wDAAwD;gBACxD,OAAO,IAAI,CAAC;YACd;QACF;IACF,GAAG;QAAC;QAAM;QAAW;KAAO;IAE5B,qDAAqD;IACrD,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,8OAAC;8BAAE;;;;;;;;;;;;;;;;;AAIX", "debugId": null}}]}