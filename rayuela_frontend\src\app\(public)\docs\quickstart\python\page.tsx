import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowLeft, Package, CheckCircle } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'Inicio Rápido con Python - Documentación',
  description: 'Aprende a integrar Rayuela en tu aplicación Python en menos de 5 minutos. Guía paso a paso con ejemplos de código.',
  path: '/docs/quickstart/python',
  keywords: ['python', 'quickstart', 'tutorial', 'API', 'integración', 'SDK'],
  type: 'article',
});

export default function PythonQuickstartPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="mb-8">
          <Button variant="ghost" asChild className="mb-4">
            <Link href="/docs">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver a Documentación
            </Link>
          </Button>
          
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            Inicio Rápido - Python
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl">
            Integra Rayuela en tu aplicación Python y obtén tu primera recomendación en menos de 5 minutos.
          </p>
        </div>

        {/* Prerequisites */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="w-5 h-5" />
              Requisitos Previos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-muted-foreground">
              <li>• Python 3.7 o superior</li>
              <li>• pip (gestor de paquetes de Python)</li>
              <li>• Una cuenta en Rayuela (regístrate en <Link href="/register" className="text-primary hover:underline">rayuela.ai</Link>)</li>
              <li>• Tu API Key (disponible en el dashboard después del registro)</li>
            </ul>
          </CardContent>
        </Card>

        {/* Step 1: Installation */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Paso 1: Instalación</CardTitle>
            <CardDescription>
              Instala las dependencias necesarias para interactuar con la API de Rayuela.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm mb-4">
              <pre className="text-sm font-mono overflow-x-auto">
                <span className="text-emerald-400">pip install</span> <span className="text-blue-400">requests</span>
              </pre>
            </div>
            <p className="text-sm text-muted-foreground">
              O si usas poetry:
            </p>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm mt-2">
              <pre className="text-sm font-mono overflow-x-auto">
                <span className="text-emerald-400">poetry add</span> <span className="text-blue-400">requests</span>
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Step 2: Setup */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Paso 2: Configuración Inicial</CardTitle>
            <CardDescription>
              Crea un cliente Python para interactuar con la API de Rayuela.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm mb-4">
              <pre className="text-sm font-mono overflow-x-auto">
{`import requests
import json

# Configuración
API_KEY = "sk_prod_tu_api_key_aqui"
BASE_URL = "https://api.rayuela.ai/api/v1"
headers = {
    "X-API-Key": API_KEY,
    "Content-Type": "application/json"
}

# Verificar conexión
response = requests.get(f"{BASE_URL}/health/auth", headers=headers)
if response.status_code == 200:
    print("✅ Conexión exitosa!")
else:
    print("❌ Error de conexión. Verifica tu API Key.")`}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Step 3: Get Recommendations */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Paso 3: Obtener Recomendaciones</CardTitle>
            <CardDescription>
              Solicita recomendaciones personalizadas para un usuario.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm mb-4">
              <pre className="text-sm font-mono overflow-x-auto">
{`# Obtener recomendaciones para un usuario
payload = {
    "user_external_id": "user_123",
    "limit": 5,
    "filters": {
        "category": "electronics",
        "price_max": 1000
    }
}

response = requests.post(
    f"{BASE_URL}/recommendations/personalized/query",
    headers=headers,
    json=payload
)

if response.status_code == 200:
    recommendations = response.json()
    print("Recomendaciones:")
    for rec in recommendations["items"]:
        print(f"- Producto {rec['product_external_id']}: {rec['score']:.2f}")`}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-success" />
              ¡Listo! Siguientes Pasos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                Ya tienes configurado el cliente Python básico. Ahora puedes:
              </p>
              <div className="grid gap-4 md:grid-cols-2">
                <Button asChild variant="outline">
                  <Link href="/docs/api/recommendations">
                    Ver API de Recomendaciones
                  </Link>
                </Button>
                <Button asChild variant="outline">
                  <Link href="/docs/guides/data-ingestion">
                    Guía de Ingesta de Datos
                  </Link>
                </Button>
                <Button asChild variant="outline">
                  <Link href="/docs/api/batch">
                    Carga Masiva de Datos
                  </Link>
                </Button>
                <Button asChild variant="outline">
                  <Link href="/docs/examples/python">
                    Ejemplos Avanzados
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
