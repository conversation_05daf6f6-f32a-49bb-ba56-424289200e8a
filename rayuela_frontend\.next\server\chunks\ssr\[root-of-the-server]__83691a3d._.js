module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/generated/rayuelaAPI.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Rayuela
 * OpenAPI spec version: v1
 */ __turbopack_context__.s({
    "DeviceType": (()=>DeviceType),
    "ExplanationLevel": (()=>ExplanationLevel),
    "ExplanationReason": (()=>ExplanationReason),
    "FilterOperator": (()=>FilterOperator),
    "GetCategoryProductsApiV1RecommendationsCategoryCategoryGetSortBy": (()=>GetCategoryProductsApiV1RecommendationsCategoryCategoryGetSortBy),
    "GetMostSearchedApiV1RecommendationsMostSearchedGetTimeframe": (()=>GetMostSearchedApiV1RecommendationsMostSearchedGetTimeframe),
    "GetMostSoldApiV1RecommendationsMostSoldGetTimeframe": (()=>GetMostSoldApiV1RecommendationsMostSoldGetTimeframe),
    "InteractionType": (()=>InteractionType),
    "LogicalOperator": (()=>LogicalOperator),
    "PageType": (()=>PageType),
    "PermissionType": (()=>PermissionType),
    "RoleType": (()=>RoleType),
    "SortDirection": (()=>SortDirection),
    "getRayuela": (()=>getRayuela)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
;
const GetCategoryProductsApiV1RecommendationsCategoryCategoryGetSortBy = {
    popularity: "popularity",
    rating: "rating",
    price_asc: "price_asc",
    price_desc: "price_desc"
};
const GetMostSoldApiV1RecommendationsMostSoldGetTimeframe = {
    day: "day",
    week: "week",
    month: "month"
};
const GetMostSearchedApiV1RecommendationsMostSearchedGetTimeframe = {
    day: "day",
    week: "week",
    month: "month"
};
const SortDirection = {
    asc: "asc",
    desc: "desc"
};
const RoleType = {
    ADMIN: "ADMIN",
    EDITOR: "EDITOR",
    VIEWER: "VIEWER"
};
const PermissionType = {
    READ: "READ",
    WRITE: "WRITE",
    DELETE: "DELETE",
    ADMIN: "ADMIN",
    PRODUCT_READ: "PRODUCT_READ",
    PRODUCT_CREATE: "PRODUCT_CREATE",
    PRODUCT_UPDATE: "PRODUCT_UPDATE",
    PRODUCT_DELETE: "PRODUCT_DELETE",
    USER_READ: "USER_READ",
    USER_CREATE: "USER_CREATE",
    USER_UPDATE: "USER_UPDATE",
    USER_DELETE: "USER_DELETE",
    SYSTEM_USER_READ: "SYSTEM_USER_READ",
    SYSTEM_USER_CREATE: "SYSTEM_USER_CREATE",
    SYSTEM_USER_UPDATE: "SYSTEM_USER_UPDATE",
    SYSTEM_USER_DELETE: "SYSTEM_USER_DELETE",
    ROLE_READ: "ROLE_READ",
    ROLE_CREATE: "ROLE_CREATE",
    ROLE_UPDATE: "ROLE_UPDATE",
    ROLE_DELETE: "ROLE_DELETE",
    PERMISSION_ASSIGN: "PERMISSION_ASSIGN",
    ANALYTICS_READ: "ANALYTICS_READ",
    MODEL_READ: "MODEL_READ",
    MODEL_CREATE: "MODEL_CREATE",
    MODEL_UPDATE: "MODEL_UPDATE",
    MODEL_DELETE: "MODEL_DELETE",
    TRAINING_JOB_READ: "TRAINING_JOB_READ",
    TRAINING_JOB_CREATE: "TRAINING_JOB_CREATE",
    TRAINING_JOB_UPDATE: "TRAINING_JOB_UPDATE",
    TRAINING_JOB_CANCEL: "TRAINING_JOB_CANCEL"
};
const PageType = {
    home: "home",
    product_detail: "product_detail",
    category: "category",
    search_results: "search_results",
    cart: "cart",
    checkout: "checkout",
    user_profile: "user_profile"
};
const LogicalOperator = {
    and: "and",
    or: "or"
};
const InteractionType = {
    VIEW: "VIEW",
    LIKE: "LIKE",
    PURCHASE: "PURCHASE",
    CART: "CART",
    RATING: "RATING",
    WISHLIST: "WISHLIST",
    CLICK: "CLICK",
    SEARCH: "SEARCH",
    FAVORITE: "FAVORITE"
};
const FilterOperator = {
    eq: "eq",
    ne: "ne",
    gt: "gt",
    gte: "gte",
    lt: "lt",
    lte: "lte",
    in: "in",
    nin: "nin",
    contains: "contains",
    startswith: "startswith",
    endswith: "endswith"
};
const ExplanationReason = {
    similar_users: "similar_users",
    similar_items: "similar_items",
    category_affinity: "category_affinity",
    popular_item: "popular_item",
    trending_item: "trending_item",
    attribute_match: "attribute_match",
    complementary_item: "complementary_item",
    recent_interaction: "recent_interaction",
    personalized_ranking: "personalized_ranking",
    new_item: "new_item",
    diversity: "diversity",
    seasonal: "seasonal",
    promotional: "promotional"
};
const ExplanationLevel = {
    simple: "simple",
    detailed: "detailed"
};
const DeviceType = {
    desktop: "desktop",
    mobile: "mobile",
    tablet: "tablet",
    tv: "tv",
    other: "other"
};
const getRayuela = ()=>{
    /**
   * @summary Health Check
   */ const healthCheckHealthGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/health`, options);
    };
    /**
 * Endpoint para verificar el estado de la API.
No requiere autenticación.
 * @summary Health Check
 */ const healthCheckApiV1HealthGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/health`, options);
    };
    /**
 * Endpoint para verificar la conexión a la base de datos.
No requiere autenticación.
 * @summary Db Health Check
 */ const dbHealthCheckApiV1HealthDbGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/health/db`, options);
    };
    /**
 * Endpoint para verificar la autenticación.
Requiere autenticación con API Key.
 * @summary Auth Health Check
 */ const authHealthCheckApiV1HealthAuthGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/health/auth`, options);
    };
    /**
   * Envía un email de verificación al usuario actual.
   * @summary Send Verification Email
   */ const sendVerificationEmailApiV1AuthSendVerificationEmailPost = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/auth/send-verification-email`, undefined, options);
    };
    /**
   * Verifica el email de un usuario usando un token.
   * @summary Verify Email
   */ const verifyEmailApiV1AuthVerifyEmailGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/auth/verify-email`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
   * Creates a new account with global email uniqueness validation and returns a JWT token and the first API Key
   * @summary Register a new account
   */ const registerApiV1AuthRegisterPost = (registerRequest, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/auth/register`, registerRequest, options);
    };
    /**
   * Authenticates a user and returns a JWT token for dashboard access. Accepts JSON payload with email and password.
   * @summary Login to obtain JWT token
   */ const loginApiV1AuthTokenPost = (loginRequest, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/auth/token`, loginRequest, options);
    };
    /**
   * Revoca el token JWT actual.
   * @summary Logout
   */ const logoutApiV1AuthLogoutPost = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/auth/logout`, undefined, options);
    };
    /**
   * List all accounts.
   * @summary List Accounts
   */ const listAccountsApiV1AccountsGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/accounts/`, options);
    };
    /**
 * Crea una nueva cuenta.

Utiliza la columna Identity para generar automáticamente el ID de la cuenta.
 * @summary Create Account
 */ const createAccountApiV1AccountsAccountsPost = (accountCreate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/accounts/accounts`, accountCreate, options);
    };
    /**
   * Get account by ID.
   * @summary Get Account
   */ const getAccountApiV1AccountsAccountIdGet = (accountId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/accounts/${accountId}`, options);
    };
    /**
   * @summary Deactivate Account
   */ const deactivateAccountApiV1AccountsAccountIdDeactivatePatch = (accountId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patch(`/api/v1/accounts/${accountId}/deactivate`, undefined, options);
    };
    /**
   * Activate an account.
   * @summary Activate Account
   */ const activateAccountApiV1AccountsAccountIdActivatePatch = (accountId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patch(`/api/v1/accounts/${accountId}/activate`, undefined, options);
    };
    /**
   * Get information about the current account.
   * @summary Get Account Info
   */ const getAccountInfoApiV1AccountsCurrentGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/accounts/current`, options);
    };
    /**
 * Update the current account information.

This endpoint allows administrators to update their own account details,
such as the account name.
 * @summary Update Current Account
 */ const updateCurrentAccountApiV1AccountsCurrentPut = (accountUpdate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].put(`/api/v1/accounts/current`, accountUpdate, options);
    };
    /**
 * Partially update the current account information.

This endpoint allows administrators to update specific fields of their own account,
such as the account name, without having to provide all fields.
 * @summary Patch Current Account
 */ const patchCurrentAccountApiV1AccountsCurrentPatch = (accountUpdate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patch(`/api/v1/accounts/current`, accountUpdate, options);
    };
    /**
   * Get audit logs with optional filters for a specific account.
   * @summary Get Audit Logs
   */ const getAuditLogsApiV1AccountsAccountIdAuditLogsGet = (accountId, params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/accounts/${accountId}/audit-logs`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
   * Get API usage statistics for the current account.
   * @summary Get Api Usage
   */ const getApiUsageApiV1AccountsUsageGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/accounts/usage`, options);
    };
    /**
 * Get all available subscription plans with their details.

Returns:
    Dict with plan information for all available plans.
 * @summary Get Available Plans
 */ const getAvailablePlansApiV1PlansGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/plans/`, options);
    };
    /**
 * Get information about the current authenticated user.

This endpoint only requires a valid JWT token, no API Key is needed.
It's useful for the initial login flow when the user hasn't confirmed
the API Key yet.
 * @summary Get Current User Info
 */ const getCurrentUserInfoApiV1SystemUsersMeGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/system-users/me`, options);
    };
    /**
   * Update current user's information.
   * @summary Update User Me
   */ const updateUserMeApiV1SystemUsersMePut = (systemUserUpdate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].put(`/api/v1/system-users/me`, systemUserUpdate, options);
    };
    /**
   * Delete current user.
   * @summary Delete User Me
   */ const deleteUserMeApiV1SystemUsersMeDelete = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`/api/v1/system-users/me`, options);
    };
    /**
   * Create a new system user.
   * @summary Create System User
   */ const createSystemUserApiV1SystemUsersPost = (systemUserCreate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/system-users/`, systemUserCreate, options);
    };
    /**
   * Get a system user by ID.
   * @summary Get System User
   */ const getSystemUserApiV1SystemUsersUserIdGet = (userId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/system-users/${userId}`, options);
    };
    /**
   * Create a new role.
   * @summary Create Role
   */ const createRoleApiV1SystemUsersRolesPost = (roleCreate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/system-users/roles/`, roleCreate, options);
    };
    /**
   * Assign a role to a system user.
   * @summary Assign Role
   */ const assignRoleApiV1SystemUsersUserIdRolesRoleIdPost = (userId, roleId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/system-users/${userId}/roles/${roleId}`, undefined, options);
    };
    /**
   * Remove a role from a system user.
   * @summary Remove Role
   */ const removeRoleApiV1SystemUsersUserIdRolesRoleIdDelete = (userId, roleId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`/api/v1/system-users/${userId}/roles/${roleId}`, options);
    };
    /**
   * Get all roles assigned to a system user.
   * @summary Get User Roles
   */ const getUserRolesApiV1SystemUsersUserIdRolesGet = (userId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/system-users/${userId}/roles`, options);
    };
    /**
   * Get all permissions assigned to a system user through their roles.
   * @summary Get User Permissions
   */ const getUserPermissionsApiV1SystemUsersUserIdPermissionsGet = (userId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/system-users/${userId}/permissions`, options);
    };
    /**
   * @summary Create End User
   */ const createEndUserApiV1EndUsersPost = (endUserCreate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/end-users/`, endUserCreate, options);
    };
    /**
   * @summary Read End Users
   */ const readEndUsersApiV1EndUsersGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/end-users/`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
   * Get an end user by ID.
   * @summary Read End User
   */ const readEndUserApiV1EndUsersUserIdGet = (userId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/end-users/${userId}`, options);
    };
    /**
   * @summary Create Product
   */ const createProductApiV1ProductsPost = (productCreate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/products/`, productCreate, options);
    };
    /**
   * Obtener productos paginados para la cuenta actual
   * @summary Read Products
   */ const readProductsApiV1ProductsGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/products/`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
   * Get a product by ID.
   * @summary Get Product
   */ const getProductApiV1ProductsProductIdGet = (productId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/products/${productId}`, options);
    };
    /**
   * @summary Update Product
   */ const updateProductApiV1ProductsProductIdPut = (productId, productUpdate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].put(`/api/v1/products/${productId}`, productUpdate, options);
    };
    /**
   * Update product inventory.
   * @summary Update Inventory
   */ const updateInventoryApiV1ProductsProductIdInventoryPatch = (productId, inventoryUpdate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patch(`/api/v1/products/${productId}/inventory`, inventoryUpdate, options);
    };
    /**
   * Get a product by its external_id.
   * @summary Get Product By External Id
   */ const getProductByExternalIdApiV1ProductsExternalExternalIdGet = (externalId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/products/external/${externalId}`, options);
    };
    /**
   * Update a product by its external_id.
   * @summary Update Product By External Id
   */ const updateProductByExternalIdApiV1ProductsExternalExternalIdPut = (externalId, productUpdate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].put(`/api/v1/products/external/${externalId}`, productUpdate, options);
    };
    /**
   * Delete (soft delete) a product by its external_id.
   * @summary Delete Product By External Id
   */ const deleteProductByExternalIdApiV1ProductsExternalExternalIdDelete = (externalId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`/api/v1/products/external/${externalId}`, options);
    };
    /**
   * Update product inventory by external_id.
   * @summary Update Inventory By External Id
   */ const updateInventoryByExternalIdApiV1ProductsExternalExternalIdInventoryPatch = (externalId, inventoryUpdate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patch(`/api/v1/products/external/${externalId}/inventory`, inventoryUpdate, options);
    };
    /**
   * @summary Get Most Searched
   */ const getMostSearchedApiV1RecommendationsMostSearchedGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/recommendations/most-searched/`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
   * @summary Get Trending Searches
   */ const getTrendingSearchesApiV1RecommendationsTrendingSearchesGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/recommendations/trending-searches/`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
   * @summary Get Popular Trends
   */ const getPopularTrendsApiV1RecommendationsPopularTrendsGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/recommendations/popular-trends/`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
   * @summary Get Related Searches
   */ const getRelatedSearchesApiV1RecommendationsRelatedSearchesProductIdGet = (productId, params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/recommendations/related-searches/${productId}`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
   * @summary Get Most Sold
   */ const getMostSoldApiV1RecommendationsMostSoldGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/recommendations/most-sold/`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
   * @summary Get Top Rated
   */ const getTopRatedApiV1RecommendationsTopRatedGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/recommendations/top-rated/`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
   * @summary Get Category Products
   */ const getCategoryProductsApiV1RecommendationsCategoryCategoryGet = (category, params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/recommendations/category/${category}`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
   * @summary Get Related Categories
   */ const getRelatedCategoriesApiV1RecommendationsRelatedCategoriesCategoryGet = (category, params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/recommendations/related-categories/${category}`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
   * @summary Get Also Bought
   */ const getAlsoBoughtApiV1RecommendationsAlsoBoughtProductIdGet = (productId, params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/recommendations/also-bought/${productId}`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Obtiene productos similares al producto especificado basados en contenido.

Utiliza vectores de características de productos para calcular similitud coseno
y encontrar productos con características similares.

Parámetros:
- product_id: ID del producto para el que se buscan similares
- limit: Número máximo de productos similares a devolver
- include_explanation: Si es True, incluye explicaciones de por qué los productos son similares
- explanation_level: Nivel de detalle de la explicación (simple o detailed)

Retorna:
- Lista paginada de productos similares ordenados por similitud
- Cada producto incluye un score de similitud
- Si se solicita, incluye explicaciones de la similitud
 * @summary Get Similar Products
 */ const getSimilarProductsApiV1RecommendationsProductsProductIdSimilarGet = (productId, params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/recommendations/products/${productId}/similar`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Invalida la caché de recomendaciones para un usuario específico.

Args:
    user_id: ID del usuario
    account: Cuenta actual
    db: Sesión de base de datos

Returns:
    Mensaje de confirmación
 * @summary Invalidate User Cache
 */ const invalidateUserCacheApiV1RecommendationsInvalidateCacheUserIdPost = (userId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/recommendations/invalidate-cache/${userId}`, undefined, options);
    };
    /**
   * Invalida toda la caché de recomendaciones para una cuenta.
   * @summary Invalidate Account Cache
   */ const invalidateAccountCacheApiV1RecommendationsInvalidateCachePost = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/recommendations/invalidate-cache`, undefined, options);
    };
    /**
   * Main endpoint for obtaining personalized recommendations with complex filters and structured context.
   * @summary Get personalized recommendations
   */ const queryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPost = (queryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPostBody, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/recommendations/personalized/query`, queryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPostBody, options);
    };
    /**
 * Obtiene una explicación detallada de por qué un producto específico se recomienda a un usuario.

Esta explicación incluye:
- Razón principal de la recomendación
- Razones secundarias
- Nivel de confianza
- Evidencia que respalda la recomendación (productos similares, categorías afines, etc.)
- Explicación en texto plano

Args:
    user_id: ID del usuario
    item_id: ID del producto
    account: Información de la cuenta autenticada
    db: Sesión de base de datos
    limit_service: Servicio de límites

Returns:
    Explicación detallada de la recomendación
 * @summary Get Recommendation Explanation
 */ const getRecommendationExplanationApiV1RecommendationsExplainUserIdItemIdGet = (userId, itemId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/recommendations/explain/${userId}/${itemId}`, options);
    };
    /**
   * Get explanation using external IDs by resolving to internal IDs.
   * @summary Get explanation using external IDs
   */ const getRecommendationExplanationExternalApiV1RecommendationsExplainExternalExternalUserIdExternalItemIdGet = (externalUserId, externalItemId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/recommendations/explain/external/${externalUserId}/${externalItemId}`, options);
    };
    /**
   * @summary Get similar products using external ID
   */ const getSimilarProductsExternalApiV1RecommendationsProductsExternalExternalProductIdSimilarGet = (externalProductId, params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/recommendations/products/external/${externalProductId}/similar`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
   * @summary Get also-bought products using external ID
   */ const getAlsoBoughtExternalApiV1RecommendationsAlsoBoughtExternalExternalProductIdGet = (externalProductId, params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/recommendations/also-bought/external/${externalProductId}`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Obtiene métricas de confianza para las recomendaciones.

Estas métricas incluyen:
- Distribución de scores de confianza por tipo de modelo (colaborativo, contenido, híbrido)
- Confianza promedio por categoría de producto
- Factores que influyen en la confianza
- Tendencias de confianza a lo largo del tiempo

Returns:
    Diccionario con métricas de confianza
 * @summary Get Confidence Metrics
 */ const getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/recommendations/confidence-metrics`, options);
    };
    /**
   * @summary Rollback Model
   */ const rollbackModelApiV1RecommendationsRollbackArtifactVersionPost = (artifactVersion, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/recommendations/rollback/${artifactVersion}`, undefined, options);
    };
    /**
 * Get performance metrics for recommendation models.

Returns metrics such as:
- Precision/Recall/NDCG/MAP: Accuracy of recommendations
- Coverage: Percentage of catalog being recommended
- Diversity: Variety in recommendations
- Novelty: Measure of how unpopular/unknown the recommended items are (based on inverse popularity)
- Serendipity: Measure of how surprising but relevant the recommendations are
- CTR (Click-Through Rate): Percentage of recommended items that receive clicks
- CVR (Conversion Rate): Percentage of recommended items that result in conversions
- Training time: Time taken to train models
- Inference time: Time taken to generate recommendations
- System metrics: CPU/Memory usage, latency percentiles

If account_id is provided and the user has admin privileges, returns data for that account.
Otherwise, returns data for the current account.
 * @summary Get Recommendation Performance
 */ const getRecommendationPerformanceApiV1RecommendationsPerformanceGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/recommendations/performance`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
   * Create a new interaction.
   * @summary Create Interaction
   */ const createInteractionApiV1InteractionsPost = (interactionCreate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/interactions/`, interactionCreate, options);
    };
    /**
   * Get all interactions for the current account.
   * @summary Read Interactions
   */ const readInteractionsApiV1InteractionsGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/interactions/`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
   * Crear una nueva interacción usando external_user_id y external_product_id.
   * @summary Create Interaction External
   */ const createInteractionExternalApiV1InteractionsExternalPost = (interactionExternalCreate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/interactions/external`, interactionExternalCreate, options);
    };
    /**
 * Inicia el entrenamiento de modelos usando Celery.

Verifica los límites de API y la frecuencia de entrenamiento permitida según el plan de suscripción
antes de crear el trabajo de entrenamiento y encolar la tarea.

Returns:
    TrainingResponse: Respuesta con el ID del trabajo y la tarea iniciada.
    Status: 202 Accepted - El trabajo ha sido aceptado y está siendo procesado.
 * @summary Train Models
 */ const trainModelsApiV1PipelineTrainPost = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/pipeline/train`, undefined, options);
    };
    /**
 * Obtiene el estado del último entrenamiento completado.

Este endpoint devuelve las métricas del último modelo entrenado exitosamente,
no el estado de un trabajo en curso. Para consultar el estado de un trabajo
específico, use el endpoint /pipeline/jobs/{job_id}/status.
 * @summary Get Training Status
 */ const getTrainingStatusApiV1PipelineStatusGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/pipeline/status`, options);
    };
    /**
 * Consulta el estado de un trabajo de entrenamiento específico.

Args:
    job_id: ID del trabajo de entrenamiento
    account: Cuenta actual
    db: Sesión de base de datos

Returns:
    Estado detallado del trabajo de entrenamiento
 * @summary Get Training Job Status
 */ const getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet = (jobId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/pipeline/jobs/${jobId}/status`, options);
    };
    /**
   * Lista todos los modelos entrenados para la cuenta.
   * @summary List Models
   */ const listModelsApiV1PipelineModelsGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/pipeline/models`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
   * Obtiene las métricas de un modelo específico.
   * @summary Get Model Metrics
   */ const getModelMetricsApiV1PipelineModelsModelIdMetricsGet = (modelId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/pipeline/models/${modelId}/metrics`, options);
    };
    /**
   * Invalida la caché de modelos y métricas.
   * @summary Invalidate Cache
   */ const invalidateCacheApiV1PipelineInvalidateCachePost = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/pipeline/invalidate-cache`, undefined, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Inicia un entrenamiento específico para la cuenta `account_id`.
Solo para administradores del sistema.

Returns:
    Dict[str, Any]: Respuesta con el ID del trabajo y la tarea iniciada.
    Status: 202 Accepted - El trabajo ha sido aceptado y está siendo procesado.
 * @summary Train Artifact For Account
 */ const trainArtifactForAccountApiV1PipelineTrainAccountIdPost = (accountId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/pipeline/train/${accountId}`, undefined, options);
    };
    /**
 * Endpoint para procesar trabajos de entrenamiento manualmente.
Este endpoint es para uso administrativo o debugging.
 * @summary Process Training Job
 */ const processTrainingJobApiV1PipelineProcessPost = (processTrainingJobApiV1PipelineProcessPostBody, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/pipeline/process`, processTrainingJobApiV1PipelineProcessPostBody, options);
    };
    /**
 * Endpoint de callback para notificaciones de finalización de entrenamiento.
Este endpoint puede ser llamado por Celery o por sistemas externos.
 * @summary Training Callback
 */ const trainingCallbackApiV1PipelineCallbackJobIdPost = (jobId, trainingCallbackApiV1PipelineCallbackJobIdPostBody, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/pipeline/callback/${jobId}`, trainingCallbackApiV1PipelineCallbackJobIdPostBody, options);
    };
    /**
 * Lista los trabajos de entrenamiento recientes para la cuenta actual.

Este endpoint permite obtener una lista de trabajos de entrenamiento,
ordenados por fecha de creación (más recientes primero).

Args:
    limit: Número máximo de trabajos a devolver (máximo 100)
    status: Filtrar por estado del trabajo (opcional)
    account: Cuenta actual
    db: Sesión de base de datos

Returns:
    Lista de trabajos de entrenamiento
 * @summary List Training Jobs
 */ const listTrainingJobsApiV1PipelineJobsGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/pipeline/jobs`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
   * @summary Test Cache
   */ const testCacheApiV1CacheTestCacheGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/cache/test-cache`, options);
    };
    /**
   * @summary Check Redis
   */ const checkRedisApiV1CacheRedisHealthGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/cache/redis-health`, options);
    };
    /**
 * Get analytics data for the current account.

If account_id is provided and the user has admin privileges, returns data for that account.
Otherwise, returns data for the current account.
 * @summary Get Account Analytics
 */ const getAccountAnalyticsApiV1AnalyticsAnalyticsAccountGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/analytics/analytics/account`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Get analytics data for API endpoints.

If account_id is provided and the user has admin privileges, returns data for that account.
Otherwise, returns data for the current account.

Can be filtered by endpoint path and HTTP method.
 * @summary Get Endpoint Analytics
 */ const getEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/analytics/analytics/endpoints`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Get performance metrics for recommendation models.

Returns metrics such as:
- Precision/Recall/NDCG/MAP: Accuracy of recommendations
- Coverage: Percentage of catalog being recommended
- Diversity: Variety in recommendations
- Novelty: Measure of how unpopular/unknown the recommended items are (based on inverse popularity)
- Serendipity: Measure of how surprising but relevant the recommendations are
- CTR (Click-Through Rate): Percentage of recommended items that receive clicks
- CVR (Conversion Rate): Percentage of recommended items that result in conversions
- Training time: Time taken to train models
- Inference time: Time taken to generate recommendations
- System metrics: CPU/Memory usage, latency percentiles

If account_id is provided and the user has admin privileges, returns data for that account.
Otherwise, returns data for the current account.
 * @summary Get Recommendation Performance
 */ const getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/analytics/analytics/recommendation_performance`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Compara métricas entre diferentes versiones de modelos.

Este endpoint permite comparar las métricas de rendimiento entre diferentes modelos
para analizar mejoras o regresiones en el tiempo.

Incluye métricas estándar de ML así como métricas proxy de negocio como:
- Tasa de conversión en el conjunto de prueba
- ROI estimado
- Engagement estimado
- Valor del cliente estimado

Si se proporcionan model_ids, compara específicamente esos modelos.
Si no, compara los últimos N modelos según el parámetro limit.

Returns:
    Diccionario con comparación detallada de métricas entre versiones de modelos
 * @summary Compare Model Versions
 */ const compareModelVersionsApiV1AnalyticsAnalyticsModelsCompareGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/analytics/analytics/models/compare`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Obtiene el historial de valores para una métrica específica.

Este endpoint permite visualizar la evolución de una métrica a lo largo del tiempo
para diferentes versiones de modelos.

Útil para análisis de tendencias y para evaluar el impacto de cambios en el modelo.

Args:
    metric_name: Nombre de la métrica a consultar
    limit: Número máximo de puntos de datos a devolver
    account_id: ID opcional de la cuenta
    
Returns:
    Lista con valores históricos de la métrica
 * @summary Get Metrics History
 */ const getMetricsHistoryApiV1AnalyticsAnalyticsMetricsHistoryGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/analytics/analytics/metrics/history`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Crea una sesión de checkout para suscribirse o cambiar de plan.

Requiere autenticación con API Key y JWT.
 * @summary Create Checkout Session
 */ const createCheckoutSessionApiV1BillingCreateCheckoutSessionPost = (createCheckoutSessionRequest, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/billing/create-checkout-session`, createCheckoutSessionRequest, options);
    };
    /**
 * Crea una sesión del portal de facturación para gestionar la suscripción.

Requiere autenticación con API Key y JWT.
 * @summary Create Portal Session
 */ const createPortalSessionApiV1BillingCreatePortalSessionPost = (createPortalSessionRequest, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/billing/create-portal-session`, createPortalSessionRequest, options);
    };
    /**
 * Webhook para recibir eventos de Mercado Pago.

No requiere autenticación, pero verifica la firma de Mercado Pago.
 * @summary Mercadopago Webhook
 */ const mercadopagoWebhookApiV1BillingWebhookMercadopagoPost = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/billing/webhook/mercadopago`, undefined, options);
    };
    /**
 * List all roles available for the current account.

Requires admin privileges.
 * @summary List Roles
 */ const listRolesApiV1RolesGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/roles/`, options);
    };
    /**
 * Create a new role.

Requires admin privileges.
 * @summary Create Role
 */ const createRoleApiV1RolesPost = (roleCreate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/roles/`, roleCreate, options);
    };
    /**
 * Get a specific role by ID.

Requires admin privileges.
 * @summary Get Role
 */ const getRoleApiV1RolesRoleIdGet = (roleId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/roles/${roleId}`, options);
    };
    /**
 * Update an existing role.

Requires admin privileges.
 * @summary Update Role
 */ const updateRoleApiV1RolesRoleIdPut = (roleId, roleCreate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].put(`/api/v1/roles/${roleId}`, roleCreate, options);
    };
    /**
 * Delete a role.

Requires admin privileges.
 * @summary Delete Role
 */ const deleteRoleApiV1RolesRoleIdDelete = (roleId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`/api/v1/roles/${roleId}`, options);
    };
    /**
 * Get all permissions assigned to a role.

Requires admin privileges.
 * @summary Get Role Permissions
 */ const getRolePermissionsApiV1RolesRoleIdPermissionsGet = (roleId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/roles/${roleId}/permissions`, options);
    };
    /**
 * Assign a permission to a role.

Requires admin privileges.
 * @summary Assign Permission To Role
 */ const assignPermissionToRoleApiV1RolesRoleIdPermissionsPermissionIdPost = (roleId, permissionId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/roles/${roleId}/permissions/${permissionId}`, undefined, options);
    };
    /**
 * Remove a permission from a role.

Requires admin privileges.
 * @summary Remove Permission From Role
 */ const removePermissionFromRoleApiV1RolesRoleIdPermissionsPermissionIdDelete = (roleId, permissionId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`/api/v1/roles/${roleId}/permissions/${permissionId}`, options);
    };
    /**
 * List all permissions available for the current account.

Requires admin privileges.
 * @summary List Permissions
 */ const listPermissionsApiV1PermissionsGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/permissions/`, options);
    };
    /**
 * Create a new permission.

Requires admin privileges.
 * @summary Create Permission
 */ const createPermissionApiV1PermissionsPost = (permissionCreate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/permissions/`, permissionCreate, options);
    };
    /**
 * Get a specific permission by ID.

Requires admin privileges.
 * @summary Get Permission
 */ const getPermissionApiV1PermissionsPermissionIdGet = (permissionId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/permissions/${permissionId}`, options);
    };
    /**
 * Get all roles that have a specific permission.

Requires admin privileges.
 * @summary Get Roles With Permission
 */ const getRolesWithPermissionApiV1PermissionsPermissionIdRolesGet = (permissionId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/permissions/${permissionId}/roles`, options);
    };
    /**
 * Limpia logs de auditoría más antiguos que el período especificado.

Args:
    days_to_keep: Número de días a mantener los logs (por defecto 90)
    account_id: ID de la cuenta específica (None para todas las cuentas)
    run_async: Si es True, ejecuta la limpieza en segundo plano

Returns:
    Diccionario con información sobre la operación o el ID de la tarea
 * @summary Cleanup Audit Logs
 */ const cleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPost = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/maintenance/maintenance/cleanup-audit-logs`, undefined, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Limpia interacciones más antiguas que el período especificado.

Args:
    days_to_keep: Número de días a mantener las interacciones (por defecto 180)
    account_id: ID de la cuenta específica (None para todas las cuentas)
    batch_size: Tamaño del lote para eliminación (por defecto 10000)
    run_async: Si es True, ejecuta la limpieza en segundo plano

Returns:
    Diccionario con información sobre la operación o el ID de la tarea
 * @summary Cleanup Interactions
 */ const cleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPost = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/maintenance/maintenance/cleanup-interactions`, undefined, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Obtiene el estado de una tarea de mantenimiento.

Args:
    task_id: ID de la tarea

Returns:
    Diccionario con información sobre la tarea
 * @summary Get Task Status
 */ const getTaskStatusApiV1MaintenanceMaintenanceTaskTaskIdGet = (taskId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/maintenance/maintenance/task/${taskId}`, options);
    };
    /**
 * Archiva y luego limpia logs de auditoría más antiguos que el período especificado.

Esta función primero exporta los datos a GCS y luego los elimina de Cloud SQL,
proporcionando una estrategia de retención de datos costo-efectiva.

Args:
    days_to_keep: Número de días a mantener los logs en Cloud SQL (por defecto 90)
    account_id: ID de la cuenta específica (None para todas las cuentas)
    batch_size: Tamaño del lote para procesamiento (por defecto 10000)
    run_async: Si es True, ejecuta el archivado en segundo plano

Returns:
    Diccionario con información sobre la operación o el ID de la tarea
 * @summary Archive And Cleanup Audit Logs
 */ const archiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPost = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/maintenance/maintenance/archive-and-cleanup-audit-logs`, undefined, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Archiva y luego limpia interacciones más antiguas que el período especificado.

Esta función primero exporta los datos a GCS y luego los elimina de Cloud SQL,
proporcionando una estrategia de retención de datos costo-efectiva.

Args:
    days_to_keep: Número de días a mantener las interacciones en Cloud SQL (por defecto 180)
    account_id: ID de la cuenta específica (None para todas las cuentas)
    batch_size: Tamaño del lote para procesamiento (por defecto 10000)
    run_async: Si es True, ejecuta el archivado en segundo plano

Returns:
    Diccionario con información sobre la operación o el ID de la tarea
 * @summary Archive And Cleanup Interactions
 */ const archiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPost = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/maintenance/maintenance/archive-and-cleanup-interactions`, undefined, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Lista archivos archivados para una tabla específica.

Args:
    table_name: Nombre de la tabla (audit_logs o interactions)
    start_date: Fecha de inicio para filtrar (formato ISO)
    end_date: Fecha de fin para filtrar (formato ISO)
    account_id: ID de cuenta para filtrar

Returns:
    Lista de archivos archivados con metadatos
 * @summary List Archived Files
 */ const listArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGet = (tableName, params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/maintenance/maintenance/archived-files/${tableName}`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Limpia registros con soft delete que han excedido el período de retención final.

Esta función identifica y elimina permanentemente (o archiva y luego elimina)
registros que tienen is_active = FALSE y deleted_at anterior al umbral definido.

Args:
    retention_days: Número de días de retención después del soft delete (por defecto 365)
    account_id: ID de la cuenta específica (None para todas las cuentas)
    dry_run: Si es True, solo reporta qué se eliminaría sin hacer cambios
    run_async: Si es True, ejecuta la limpieza en segundo plano

Returns:
    Diccionario con información sobre la operación o el ID de la tarea
 * @summary Cleanup Soft Deleted Records
 */ const cleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPost = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/maintenance/maintenance/cleanup-soft-deleted-records`, undefined, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Obtiene estadísticas sobre registros con soft delete.

Args:
    account_id: ID de cuenta para filtrar (None para todas las cuentas)
    run_async: Si es True, ejecuta como tarea en segundo plano

Returns:
    Estadísticas por tabla o ID de tarea si run_async=True
 * @summary Get Soft Delete Statistics
 */ const getSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/maintenance/maintenance/soft-delete-statistics`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Monitorea las tablas de alto volumen y devuelve estadísticas.

Args:
    run_async: Si es True, ejecuta el monitoreo en segundo plano

Returns:
    Diccionario con estadísticas de las tablas o el ID de la tarea
 * @summary Monitor Tables
 */ const monitorTablesApiV1MaintenanceMaintenanceMonitorTablesPost = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/maintenance/maintenance/monitor-tables`, undefined, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Get subscription usage information for the current account.

Returns:
    Dict with subscription usage information including:
    - API calls used and limit
    - Storage used and limit
    - Available models
    - Reset date for API calls counter
 * @summary Get Subscription Usage
 */ const getSubscriptionUsageApiV1SubscriptionUsageGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/subscription/usage`, options);
    };
    /**
 * Get storage usage information for the current account.

Returns:
    Dict with storage usage information including:
    - Storage used and limit
    - Breakdown by data type
    - Last measurement time
 * @summary Get Storage Usage
 */ const getStorageUsageApiV1StorageUsageGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/storage/usage`, options);
    };
    /**
 * Force a refresh of storage usage calculation for the current account.

Returns:
    Dict with updated storage usage information
 * @summary Refresh Storage Usage
 */ const refreshStorageUsageApiV1StorageRefreshPost = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/storage/refresh`, undefined, options);
    };
    /**
 * Carga masiva de datos de usuarios, productos e interacciones.

Este endpoint permite cargar datos en lote para inicializar o actualizar el sistema de recomendación.
Los datos se procesan de forma asíncrona utilizando Celery para mayor robustez.
Los datos sensibles se almacenan de forma segura en GCS o en el sistema de archivos local.

Para una guía detallada sobre cómo formatear y enviar datos, consulte la
[Guía de Ingesta de Datos Masiva](/docs/guides/data_ingestion_guide).
 * @summary Batch Data Ingestion
 */ const batchDataIngestionApiV1IngestionBatchPost = (batchIngestionRequest, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/ingestion/batch`, batchIngestionRequest, options);
    };
    /**
 * Lista los trabajos de ingesta masiva recientes para la cuenta actual.

Este endpoint permite obtener una lista de trabajos de ingesta masiva,
ordenados por fecha de creación (más recientes primero).

Args:
    limit: Número máximo de trabajos a devolver (máximo 100)
    status: Filtrar por estado del trabajo (opcional)
    account: Cuenta actual
    db: Sesión de base de datos

Returns:
    Lista de trabajos de ingesta masiva
 * @summary List Batch Jobs
 */ const listBatchJobsApiV1IngestionBatchGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/ingestion/batch`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Consulta el estado de un trabajo de ingesta masiva.

Este endpoint permite verificar el progreso de un trabajo de ingesta masiva,
incluyendo la cantidad de registros procesados, errores encontrados y tiempo estimado
de finalización.

Para más detalles sobre cómo monitorear el proceso de ingesta y manejar errores,
consulte la sección [Monitoreo del Proceso](/docs/guides/data_ingestion_guide#monitoreo-del-proceso)
en la Guía de Ingesta de Datos.

Args:
    job_id: ID del trabajo de ingesta masiva
    account: Cuenta actual
    db: Sesión de base de datos

Returns:
    Estado del trabajo de ingesta masiva
 * @summary Get Batch Job Status
 */ const getBatchJobStatusApiV1IngestionBatchJobIdGet = (jobId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/ingestion/batch/${jobId}`, options);
    };
    /**
   * Return a consolidated summary of usage information for the current account.
   * @summary Get Usage Summary
   */ const getUsageSummaryApiV1UsageSummaryGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/usage/summary`, options);
    };
    /**
 * Get historical usage data for the current account.

Args:
    start_date: Optional start date for filtering data
    end_date: Optional end date for filtering data
    
Returns:
    List of daily usage data points
 * @summary Get Usage History
 */ const getUsageHistoryApiV1UsageGet = (params, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/usage/`, {
            ...options,
            params: {
                ...params,
                ...options?.params
            }
        });
    };
    /**
 * Lista todas las API Keys activas para la cuenta actual.

Retorna una lista de todas las API Keys activas asociadas a la cuenta,
incluyendo información como nombre, prefijo, últimos caracteres, fecha de creación
y último uso.

**Nota**: No se incluyen las API Keys completas por seguridad.
 * @summary List Api Keys
 */ const listApiKeysApiV1ApiKeysGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/api-keys/`, options);
    };
    /**
 * Crea una nueva API Key para la cuenta actual.

Este endpoint permite crear múltiples API Keys para una cuenta, lo que mejora
la seguridad y flexibilidad:
- Diferentes claves para diferentes entornos (desarrollo, staging, producción)
- Claves específicas para diferentes miembros del equipo
- Posibilidad de revocar claves específicas sin afectar otras integraciones

**IMPORTANTE**:
- Requiere autenticación JWT (debes estar logueado)
- La API Key completa solo se devolverá una vez
- Puedes crear múltiples API Keys activas
- Cada API Key puede tener un nombre descriptivo

**Autenticación requerida**: JWT token en el header Authorization: Bearer <token>

Returns:
- **id**: ID único de la API Key
- **api_key**: Tu nueva API Key completa (solo se muestra una vez)
- **name**: Nombre descriptivo de la API Key
- **prefix**: Prefijo de la API Key para identificación
- **created_at**: Fecha y hora de creación
- **message**: Mensaje informativo sobre el uso seguro
 * @summary Create Api Key
 */ const createApiKeyApiV1ApiKeysPost = (apiKeyCreate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/api-keys/`, apiKeyCreate, options);
    };
    /**
 * Revoca todas las API Keys de la cuenta actual.
Esta acción es irreversible. Después de revocar, tendrás que generar nuevas API Keys.
 * @summary Revoke Api Key
 */ const revokeApiKeyApiV1ApiKeysDelete = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`/api/v1/api-keys/`, options);
    };
    /**
 * Obtiene información sobre la API Key actual.
No devuelve la API Key completa, solo metadatos como:
- Prefijo de la API Key
- Últimos caracteres
- Fecha de creación
- Último uso (si está disponible)

Esta información permite identificar la API Key sin comprometer seguridad.
 * @summary Get Current Api Key
 */ const getCurrentApiKeyApiV1ApiKeysCurrentGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/api-keys/current`, options);
    };
    /**
 * Actualiza los metadatos de una API Key específica.

Permite actualizar información como el nombre descriptivo de la API Key.
No se puede cambiar la clave en sí, solo sus metadatos.

**IMPORTANTE**:
- Solo puedes actualizar API Keys que pertenecen a tu cuenta
- No se puede cambiar la API Key en sí, solo metadatos
- La API Key debe estar activa para poder actualizarla
 * @summary Update Api Key
 */ const updateApiKeyApiV1ApiKeysApiKeyIdPut = (apiKeyId, apiKeyUpdate, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].put(`/api/v1/api-keys/${apiKeyId}`, apiKeyUpdate, options);
    };
    /**
 * Revoca una API Key específica.

Esta acción es irreversible. La API Key será desactivada y no podrá
utilizarse para autenticar solicitudes.

**IMPORTANTE**:
- Solo puedes revocar API Keys que pertenecen a tu cuenta
- Esta acción es irreversible
- La API Key dejará de funcionar inmediatamente
- Otras API Keys de la cuenta no se verán afectadas
 * @summary Revoke Specific Api Key
 */ const revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete = (apiKeyId, options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`/api/v1/api-keys/${apiKeyId}`, options);
    };
    /**
 * Reset all sandbox data for FREE plan users.

This endpoint allows FREE plan users to clean their experimental data
to start fresh with new experiments.

Only available for FREE plan users.
 * @summary Reset Sandbox Data
 */ const resetSandboxDataApiV1SandboxResetPost = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/sandbox/reset`, undefined, options);
    };
    /**
 * Get current sandbox status and data counts.

Returns information about the current state of sandbox data.
 * @summary Get Sandbox Status
 */ const getSandboxStatusApiV1SandboxStatusGet = (options)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/sandbox/status`, options);
    };
    return {
        healthCheckHealthGet,
        healthCheckApiV1HealthGet,
        dbHealthCheckApiV1HealthDbGet,
        authHealthCheckApiV1HealthAuthGet,
        sendVerificationEmailApiV1AuthSendVerificationEmailPost,
        verifyEmailApiV1AuthVerifyEmailGet,
        registerApiV1AuthRegisterPost,
        loginApiV1AuthTokenPost,
        logoutApiV1AuthLogoutPost,
        listAccountsApiV1AccountsGet,
        createAccountApiV1AccountsAccountsPost,
        getAccountApiV1AccountsAccountIdGet,
        deactivateAccountApiV1AccountsAccountIdDeactivatePatch,
        activateAccountApiV1AccountsAccountIdActivatePatch,
        getAccountInfoApiV1AccountsCurrentGet,
        updateCurrentAccountApiV1AccountsCurrentPut,
        patchCurrentAccountApiV1AccountsCurrentPatch,
        getAuditLogsApiV1AccountsAccountIdAuditLogsGet,
        getApiUsageApiV1AccountsUsageGet,
        getAvailablePlansApiV1PlansGet,
        getCurrentUserInfoApiV1SystemUsersMeGet,
        updateUserMeApiV1SystemUsersMePut,
        deleteUserMeApiV1SystemUsersMeDelete,
        createSystemUserApiV1SystemUsersPost,
        getSystemUserApiV1SystemUsersUserIdGet,
        createRoleApiV1SystemUsersRolesPost,
        assignRoleApiV1SystemUsersUserIdRolesRoleIdPost,
        removeRoleApiV1SystemUsersUserIdRolesRoleIdDelete,
        getUserRolesApiV1SystemUsersUserIdRolesGet,
        getUserPermissionsApiV1SystemUsersUserIdPermissionsGet,
        createEndUserApiV1EndUsersPost,
        readEndUsersApiV1EndUsersGet,
        readEndUserApiV1EndUsersUserIdGet,
        createProductApiV1ProductsPost,
        readProductsApiV1ProductsGet,
        getProductApiV1ProductsProductIdGet,
        updateProductApiV1ProductsProductIdPut,
        updateInventoryApiV1ProductsProductIdInventoryPatch,
        getProductByExternalIdApiV1ProductsExternalExternalIdGet,
        updateProductByExternalIdApiV1ProductsExternalExternalIdPut,
        deleteProductByExternalIdApiV1ProductsExternalExternalIdDelete,
        updateInventoryByExternalIdApiV1ProductsExternalExternalIdInventoryPatch,
        getMostSearchedApiV1RecommendationsMostSearchedGet,
        getTrendingSearchesApiV1RecommendationsTrendingSearchesGet,
        getPopularTrendsApiV1RecommendationsPopularTrendsGet,
        getRelatedSearchesApiV1RecommendationsRelatedSearchesProductIdGet,
        getMostSoldApiV1RecommendationsMostSoldGet,
        getTopRatedApiV1RecommendationsTopRatedGet,
        getCategoryProductsApiV1RecommendationsCategoryCategoryGet,
        getRelatedCategoriesApiV1RecommendationsRelatedCategoriesCategoryGet,
        getAlsoBoughtApiV1RecommendationsAlsoBoughtProductIdGet,
        getSimilarProductsApiV1RecommendationsProductsProductIdSimilarGet,
        invalidateUserCacheApiV1RecommendationsInvalidateCacheUserIdPost,
        invalidateAccountCacheApiV1RecommendationsInvalidateCachePost,
        queryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPost,
        getRecommendationExplanationApiV1RecommendationsExplainUserIdItemIdGet,
        getRecommendationExplanationExternalApiV1RecommendationsExplainExternalExternalUserIdExternalItemIdGet,
        getSimilarProductsExternalApiV1RecommendationsProductsExternalExternalProductIdSimilarGet,
        getAlsoBoughtExternalApiV1RecommendationsAlsoBoughtExternalExternalProductIdGet,
        getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet,
        rollbackModelApiV1RecommendationsRollbackArtifactVersionPost,
        getRecommendationPerformanceApiV1RecommendationsPerformanceGet,
        createInteractionApiV1InteractionsPost,
        readInteractionsApiV1InteractionsGet,
        createInteractionExternalApiV1InteractionsExternalPost,
        trainModelsApiV1PipelineTrainPost,
        getTrainingStatusApiV1PipelineStatusGet,
        getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet,
        listModelsApiV1PipelineModelsGet,
        getModelMetricsApiV1PipelineModelsModelIdMetricsGet,
        invalidateCacheApiV1PipelineInvalidateCachePost,
        trainArtifactForAccountApiV1PipelineTrainAccountIdPost,
        processTrainingJobApiV1PipelineProcessPost,
        trainingCallbackApiV1PipelineCallbackJobIdPost,
        listTrainingJobsApiV1PipelineJobsGet,
        testCacheApiV1CacheTestCacheGet,
        checkRedisApiV1CacheRedisHealthGet,
        getAccountAnalyticsApiV1AnalyticsAnalyticsAccountGet,
        getEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGet,
        getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet,
        compareModelVersionsApiV1AnalyticsAnalyticsModelsCompareGet,
        getMetricsHistoryApiV1AnalyticsAnalyticsMetricsHistoryGet,
        createCheckoutSessionApiV1BillingCreateCheckoutSessionPost,
        createPortalSessionApiV1BillingCreatePortalSessionPost,
        mercadopagoWebhookApiV1BillingWebhookMercadopagoPost,
        listRolesApiV1RolesGet,
        createRoleApiV1RolesPost,
        getRoleApiV1RolesRoleIdGet,
        updateRoleApiV1RolesRoleIdPut,
        deleteRoleApiV1RolesRoleIdDelete,
        getRolePermissionsApiV1RolesRoleIdPermissionsGet,
        assignPermissionToRoleApiV1RolesRoleIdPermissionsPermissionIdPost,
        removePermissionFromRoleApiV1RolesRoleIdPermissionsPermissionIdDelete,
        listPermissionsApiV1PermissionsGet,
        createPermissionApiV1PermissionsPost,
        getPermissionApiV1PermissionsPermissionIdGet,
        getRolesWithPermissionApiV1PermissionsPermissionIdRolesGet,
        cleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPost,
        cleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPost,
        getTaskStatusApiV1MaintenanceMaintenanceTaskTaskIdGet,
        archiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPost,
        archiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPost,
        listArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGet,
        cleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPost,
        getSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGet,
        monitorTablesApiV1MaintenanceMaintenanceMonitorTablesPost,
        getSubscriptionUsageApiV1SubscriptionUsageGet,
        getStorageUsageApiV1StorageUsageGet,
        refreshStorageUsageApiV1StorageRefreshPost,
        batchDataIngestionApiV1IngestionBatchPost,
        listBatchJobsApiV1IngestionBatchGet,
        getBatchJobStatusApiV1IngestionBatchJobIdGet,
        getUsageSummaryApiV1UsageSummaryGet,
        getUsageHistoryApiV1UsageGet,
        listApiKeysApiV1ApiKeysGet,
        createApiKeyApiV1ApiKeysPost,
        revokeApiKeyApiV1ApiKeysDelete,
        getCurrentApiKeyApiV1ApiKeysCurrentGet,
        updateApiKeyApiV1ApiKeysApiKeyIdPut,
        revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete,
        resetSandboxDataApiV1SandboxResetPost,
        getSandboxStatusApiV1SandboxStatusGet
    };
};
}}),
"[project]/src/lib/generated/api-client.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiError": (()=>ApiError),
    "customInstance": (()=>customInstance),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
;
class ApiError extends Error {
    status;
    errorCode;
    details;
    constructor(message, status, errorCode, details){
        super(message), this.status = status, this.errorCode = errorCode, this.details = details;
        this.name = "ApiError";
    }
    static isApiError(error) {
        return error instanceof ApiError;
    }
    static fromResponse(response) {
        return new ApiError(response.message, response.status_code, response.error_code, response.details);
    }
}
// Obtén la URL base de las variables de entorno
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:8001") || "";
// Set global axios baseURL so that generated getRayuela() calls use it as well
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].defaults.baseURL = API_BASE_URL;
const customInstance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: API_BASE_URL,
    headers: {
        "Content-Type": "application/json"
    }
});
// Interceptor de solicitud para añadir cabeceras de autenticación
customInstance.interceptors.request.use((config)=>{
    // Detect execution in browser
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return config;
});
// Interceptor de respuesta para manejar errores
customInstance.interceptors.response.use((response)=>{
    return response;
}, (error)=>{
    if (error.response) {
        const errorResponse = error.response.data;
        throw ApiError.fromResponse(errorResponse);
    } else if (error.request) {
        // La solicitud se realizó pero no se recibió respuesta
        throw new ApiError("No se recibió respuesta del servidor", 0, "NETWORK_ERROR", null);
    } else {
        // Error al configurar la solicitud
        throw new ApiError(error.message, 0, "REQUEST_ERROR", null);
    }
});
const __TURBOPACK__default__export__ = customInstance;
}}),
"[project]/src/lib/api.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Consolidated API client using generated code from OpenAPI spec
 * This file provides a clean interface over the generated API client
 */ // Import generated API functions and types
__turbopack_context__.s({
    "createApiKey": (()=>createApiKey),
    "createBillingPortalSession": (()=>createBillingPortalSession),
    "createCheckoutSession": (()=>createCheckoutSession),
    "deleteApiKey": (()=>deleteApiKey),
    "getAccountUsage": (()=>getAccountUsage),
    "getApiKey": (()=>getApiKey),
    "getApiKeys": (()=>getApiKeys),
    "getAvailablePlans": (()=>getAvailablePlans),
    "getConfidenceMetrics": (()=>getConfidenceMetrics),
    "getCurrentAccount": (()=>getCurrentAccount),
    "getCurrentUser": (()=>getCurrentUser),
    "getMe": (()=>getMe),
    "getMyAccount": (()=>getMyAccount),
    "getPlans": (()=>getPlans),
    "getRecommendationPerformance": (()=>getRecommendationPerformance),
    "getUsageHistory": (()=>getUsageHistory),
    "getUsageSummary": (()=>getUsageSummary),
    "healthCheck": (()=>healthCheck),
    "listApiKeys": (()=>listApiKeys),
    "loginUser": (()=>loginUser),
    "logout": (()=>logout),
    "registerUser": (()=>registerUser),
    "requestEmailVerification": (()=>requestEmailVerification),
    "revokeAllApiKeys": (()=>revokeAllApiKeys),
    "revokeApiKey": (()=>revokeApiKey),
    "updateApiKey": (()=>updateApiKey),
    "verifyEmail": (()=>verifyEmail)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$generated$2f$rayuelaAPI$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/generated/rayuelaAPI.ts [app-ssr] (ecmascript)");
// Import the consolidated ApiError from generated client
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$generated$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/generated/api-client.ts [app-ssr] (ecmascript)");
;
;
;
// Helper function to handle API errors consistently
function handleApiError(error, defaultMessage) {
    // If it's already an ApiError from the interceptor, re-throw it
    if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$generated$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"]) {
        throw error;
    }
    // Handle Axios errors that weren't caught by interceptor
    if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error;
        const status = axiosError.response?.status || 500;
        const message = axiosError.message || defaultMessage;
        const errorCode = axiosError.response?.data?.error_code || 'UNKNOWN_ERROR';
        const details = axiosError.response?.data?.details || null;
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$generated$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"](message, status, errorCode, details);
    }
    // Handle generic errors
    if (error instanceof Error) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$generated$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"](error.message, 500, 'GENERIC_ERROR', null);
    }
    // Fallback for unknown error types
    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$generated$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"](defaultMessage, 500, 'UNKNOWN_ERROR', null);
}
// Create a simple API client instance
const api = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$generated$2f$rayuelaAPI$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getRayuela"])();
const loginUser = async (credentials)=>{
    try {
        return await api.loginApiV1AuthTokenPost(credentials);
    } catch (error) {
        handleApiError(error, 'Login failed');
    }
};
const registerUser = async (accountName, email, password)=>{
    try {
        const registerRequest = {
            accountName: accountName,
            email,
            password
        };
        // The backend returns RegisterResponse with proper camelCase fields
        const response = await api.registerApiV1AuthRegisterPost(registerRequest);
        return response;
    } catch (error) {
        handleApiError(error, 'Registration failed');
    }
};
const logout = async ()=>{
    return api.logoutApiV1AuthLogoutPost();
};
const requestEmailVerification = async ()=>{
    return api.sendVerificationEmailApiV1AuthSendVerificationEmailPost();
};
const verifyEmail = async (token)=>{
    return api.verifyEmailApiV1AuthVerifyEmailGet({
        token
    });
};
const getCurrentUser = async ()=>{
    try {
        return await api.getCurrentUserInfoApiV1SystemUsersMeGet();
    } catch (error) {
        handleApiError(error, 'Failed to get current user');
    }
};
const getMe = getCurrentUser;
const getCurrentAccount = async ()=>{
    try {
        // Authentication is handled by the client configuration
        return await api.getAccountInfoApiV1AccountsCurrentGet();
    } catch (error) {
        handleApiError(error, 'Failed to get current account');
    }
};
const getMyAccount = getCurrentAccount;
const getAccountUsage = async ()=>{
    try {
        return await api.getApiUsageApiV1AccountsUsageGet();
    } catch (error) {
        handleApiError(error, 'Failed to get account usage');
    }
};
const getPlans = async ()=>{
    try {
        return await api.getAvailablePlansApiV1PlansGet();
    } catch (error) {
        handleApiError(error, 'Failed to get plans');
    }
};
const getAvailablePlans = getPlans;
const getUsageHistory = async (startDate, endDate)=>{
    const params = {};
    if (startDate) params.start_date = startDate;
    if (endDate) params.end_date = endDate;
    return api.getUsageHistoryApiV1UsageGet(params);
};
const getUsageSummary = async ()=>{
    try {
        return await api.getUsageSummaryApiV1UsageSummaryGet();
    } catch (error) {
        handleApiError(error, 'Failed to get usage summary');
    }
};
const getApiKeys = async ()=>{
    try {
        return await api.listApiKeysApiV1ApiKeysGet();
    } catch (error) {
        handleApiError(error, 'Failed to get API keys');
    }
};
const listApiKeys = getApiKeys;
const createApiKey = async (apiKeyData)=>{
    try {
        const createRequest = {
            name: apiKeyData.name
        };
        return await api.createApiKeyApiV1ApiKeysPost(createRequest);
    } catch (error) {
        handleApiError(error, 'Failed to create API key');
    }
};
const deleteApiKey = async (keyId)=>{
    try {
        await api.revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete(Number(keyId));
    } catch (error) {
        handleApiError(error, 'Failed to delete API key');
    }
};
const updateApiKey = async (keyId, updateData)=>{
    try {
        const updateRequest = {
            name: updateData.name
        };
        return await api.updateApiKeyApiV1ApiKeysApiKeyIdPut(Number(keyId), updateRequest);
    } catch (error) {
        handleApiError(error, 'Failed to update API key');
    }
};
const revokeApiKey = async (apiKeyId)=>{
    return api.revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete(apiKeyId);
};
const getApiKey = async ()=>{
    return api.getCurrentApiKeyApiV1ApiKeysCurrentGet();
};
const revokeAllApiKeys = async ()=>{
    return api.revokeApiKeyApiV1ApiKeysDelete();
};
const createCheckoutSession = async (priceId)=>{
    const request = {
        price_id: priceId
    };
    return api.createCheckoutSessionApiV1BillingCreateCheckoutSessionPost(request);
};
const createBillingPortalSession = async ()=>{
    return api.createPortalSessionApiV1BillingCreatePortalSessionPost({});
};
const healthCheck = async ()=>{
    try {
        return await api.healthCheckHealthGet();
    } catch (error) {
        handleApiError(error, 'Health check failed');
    }
};
const getRecommendationPerformance = async (modelId, metricType)=>{
    try {
        const params = {};
        if (modelId !== undefined) params.model_id = modelId;
        if (metricType !== undefined) params.metric_type = metricType;
        return await api.getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet(params);
    } catch (error) {
        handleApiError(error, 'Failed to get recommendation performance');
    }
};
const getConfidenceMetrics = async ()=>{
    try {
        return await api.getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet();
    } catch (error) {
        handleApiError(error, 'Failed to get confidence metrics');
    }
};
}}),
"[project]/src/lib/api.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$generated$2f$rayuelaAPI$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/generated/rayuelaAPI.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$generated$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/generated/api-client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": (()=>cn)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-ssr] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
}}),
"[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": (()=>Button),
    "buttonVariants": (()=>buttonVariants)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slot/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
const buttonVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cva"])("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-body-sm font-medium disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none rayuela-focus-ring", {
    variants: {
        variant: {
            default: "bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/95 rayuela-interactive rayuela-shadow-hover rayuela-scale-hover rayuela-active-press",
            destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90 active:bg-destructive/95 rayuela-interactive rayuela-shadow-hover rayuela-scale-hover rayuela-active-press",
            outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground active:bg-accent/80 rayuela-interactive rayuela-shadow-hover rayuela-scale-hover rayuela-active-press",
            secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90 rayuela-interactive rayuela-shadow-hover rayuela-scale-hover rayuela-active-press",
            ghost: "hover:bg-accent hover:text-accent-foreground active:bg-accent/80 rayuela-interactive rayuela-scale-hover rayuela-active-press",
            link: "text-primary underline-offset-4 hover:underline active:text-primary/80 rayuela-interactive rayuela-active-press"
        },
        size: {
            default: "h-10 px-4 py-2",
            sm: "h-9 rounded-lg px-3 text-caption-lg",
            lg: "h-11 rounded-lg px-8 text-body-lg",
            icon: "h-10 w-10"
        }
    },
    defaultVariants: {
        variant: "default",
        size: "default"
    }
});
const Button = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, variant, size, asChild = false, ...props }, ref)=>{
    const Comp = asChild ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Slot"] : "button";
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Comp, {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])(buttonVariants({
            variant,
            size,
            className
        })),
        ref: ref,
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/button.tsx",
        lineNumber: 48,
        columnNumber: 7
    }, this);
});
Button.displayName = "Button";
;
}}),
"[project]/src/components/ui/checkbox.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Checkbox": (()=>Checkbox)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$checkbox$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-checkbox/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-ssr] (ecmascript) <export default as Check>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
const Checkbox = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$checkbox$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background rayuela-focus-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground", className),
        ...props,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$checkbox$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Indicator"], {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex items-center justify-center text-current"),
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__["Check"], {
                className: "h-4 w-4"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/checkbox.tsx",
                lineNumber: 22,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ui/checkbox.tsx",
            lineNumber: 21,
            columnNumber: 5
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/checkbox.tsx",
        lineNumber: 13,
        columnNumber: 3
    }, this));
Checkbox.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$checkbox$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"].displayName;
;
}}),
"[project]/src/components/auth/InitialApiKeyModal.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "InitialApiKeyModal": (()=>InitialApiKeyModal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$checkbox$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/checkbox.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Copy$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/copy.js [app-ssr] (ecmascript) <export default as Copy>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-ssr] (ecmascript) <export default as Check>");
;
;
;
;
;
function InitialApiKeyModal({ apiKey, onContinue }) {
    const [copied, setCopied] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [confirmed, setConfirmed] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const handleCopy = async ()=>{
        await navigator.clipboard.writeText(apiKey);
        setCopied(true);
        setTimeout(()=>setCopied(false), 2000);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-2xl font-bold",
                        children: "¡Tu primera API Key está lista!"
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                        lineNumber: 24,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-muted-foreground mt-2",
                        children: "Esta es tu primera API Key. Puedes generar más claves para diferentes entornos o equipos en la sección 'API Keys' de tu dashboard."
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                        lineNumber: 25,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                lineNumber: 23,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-muted p-4 rounded-lg",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("code", {
                            className: "text-sm font-mono",
                            children: apiKey
                        }, void 0, false, {
                            fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                            lineNumber: 33,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                            variant: "outline",
                            size: "sm",
                            onClick: handleCopy,
                            className: "ml-2",
                            children: [
                                copied ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__["Check"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                                    lineNumber: 40,
                                    columnNumber: 23
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Copy$3e$__["Copy"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                                    lineNumber: 40,
                                    columnNumber: 55
                                }, this),
                                copied ? 'Copiado' : 'Copiar'
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                            lineNumber: 34,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                    lineNumber: 32,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                lineNumber: 31,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-warning-light border border-warning/20 p-4 rounded-lg",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-sm text-warning-foreground",
                    children: [
                        "⚠️ ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                            children: "Importante:"
                        }, void 0, false, {
                            fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                            lineNumber: 48,
                            columnNumber: 14
                        }, this),
                        " Esta clave no se mostrará de nuevo. Si la pierdes, puedes generar una nueva en la sección API Keys."
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                    lineNumber: 47,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                lineNumber: 46,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-blue-50 border border-blue-200 p-4 rounded-lg",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-sm text-blue-800",
                    children: [
                        "📧 Antes de poder usar tu API Key, ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                            children: "debes verificar tu dirección de correo electrónico"
                        }, void 0, false, {
                            fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                            lineNumber: 56,
                            columnNumber: 46
                        }, this),
                        ". Revisa tu bandeja de entrada y sigue el enlace de verificación. Hasta entonces, cualquier llamada a la API devolverá un error ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("em", {
                            children: "EmailNotVerifiedError"
                        }, void 0, false, {
                            fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                            lineNumber: 58,
                            columnNumber: 30
                        }, this),
                        "."
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                    lineNumber: 55,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                lineNumber: 54,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center space-x-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$checkbox$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Checkbox"], {
                        id: "confirmed",
                        checked: confirmed,
                        onCheckedChange: (val)=>setConfirmed(val === true)
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                        lineNumber: 63,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                        htmlFor: "confirmed",
                        className: "text-sm",
                        children: "He guardado mi API Key de forma segura"
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                        lineNumber: 68,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                lineNumber: 62,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex gap-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        onClick: ()=>onContinue('/dashboard/api-keys'),
                        disabled: !confirmed,
                        className: "flex-1",
                        children: "Ir a Gestión de API Keys"
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                        lineNumber: 74,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        variant: "outline",
                        onClick: ()=>onContinue('/dashboard'),
                        disabled: !confirmed,
                        className: "flex-1",
                        children: "Continuar al Dashboard"
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                        lineNumber: 81,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
                lineNumber: 73,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/auth/InitialApiKeyModal.tsx",
        lineNumber: 22,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/lib/auth.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$generated$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/generated/api-client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$InitialApiKeyModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/auth/InitialApiKeyModal.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const AuthProvider = ({ children })=>{
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [token, setToken] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [apiKey, setApiKey] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null); // API Key de la cuenta
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true); // Carga inicial
    const [showApiKeyModal, setShowApiKeyModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false); // Estado para el modal
    const [initialApiKeyToShow, setInitialApiKeyToShow] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null); // Key a mostrar
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    // Estado para manejar el error de email no verificado
    const [emailVerificationError, setEmailVerificationError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // Función para limpiar el estado y localStorage
    const clearAuthData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        localStorage.removeItem('rayuela-token');
        localStorage.removeItem('rayuela-apiKey');
        setUser(null);
        setToken(null);
        setApiKey(null);
    }, []);
    // Función para validar token y obtener datos del usuario
    const fetchUserData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (currentToken, currentApiKey)=>{
        setIsLoading(true);
        try {
            // Llama a /system-users/me usando el token (y opcionalmente la API Key)
            const userData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getMe"])();
            if (!userData.is_active) {
                throw new Error("User account is inactive.");
            }
            setUser(userData);
            setToken(currentToken);
            if (currentApiKey) {
                setApiKey(currentApiKey);
            }
            console.log("User data fetched successfully:", userData);
            return true; // Indica éxito
        } catch (error) {
            console.error("Token validation/fetch user data failed:", error);
            clearAuthData(); // Limpiar datos si el token es inválido o hay error
            // Si estamos en una ruta protegida y falla la validación, redirigir a login
            if (pathname?.startsWith('/dashboard')) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Tu sesión ha expirado o es inválida. Por favor, inicia sesión de nuevo.");
                router.push('/login');
            }
            return false; // Indica fallo
        } finally{
            setIsLoading(false);
        }
    }, [
        router,
        pathname,
        clearAuthData
    ]);
    // Efecto para cargar estado inicial desde localStorage y validar
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        console.log("AuthProvider Mounted. Checking localStorage...");
        const storedToken = localStorage.getItem('rayuela-token');
        const storedApiKey = localStorage.getItem('rayuela-apiKey');
        if (storedToken && storedApiKey) {
            console.log("Found token and apiKey in localStorage. Validating...");
            fetchUserData(storedToken, storedApiKey);
        } else {
            console.log("No token or apiKey found in localStorage.");
            setIsLoading(false); // No hay token, terminamos la carga inicial
        }
    }, [
        fetchUserData
    ]);
    // Función para solicitar un nuevo email de verificación
    const requestNewVerificationEmail = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (!token) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("No hay sesión activa. Por favor, inicia sesión de nuevo.");
            return false;
        }
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["requestEmailVerification"])();
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Email de verificación enviado. Por favor, revisa tu bandeja de entrada.");
            return true;
        } catch (error) {
            console.error("Error al solicitar email de verificación:", error);
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$generated$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"]) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(error.message || "Error al solicitar email de verificación.");
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Error inesperado al solicitar email de verificación");
            }
            return false;
        }
    }, [
        token
    ]);
    // Función de Login
    const login = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (email, password)=>{
        setIsLoading(true);
        // Limpiar cualquier error previo de verificación de email
        setEmailVerificationError(null);
        try {
            // Preparar los datos para la API
            const credentials = {
                email,
                password
            };
            // Llamar directamente a la API de login
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["loginUser"])(credentials);
            const accessTokenRaw = response.access_token ?? response.accessToken;
            if (accessTokenRaw) {
                // Guardar token en localStorage
                localStorage.setItem('rayuela-token', accessTokenRaw);
                setToken(accessTokenRaw);
                // El endpoint /auth/token solo devuelve JWT, no API Key
                // Intentar cargar API Key existente de localStorage
                const storedApiKey = localStorage.getItem('rayuela-apiKey');
                if (storedApiKey) {
                    setApiKey(storedApiKey);
                }
                // Validar el token y obtener datos del usuario (con o sin API Key)
                const fetchSuccess = await fetchUserData(accessTokenRaw, storedApiKey);
                if (fetchSuccess) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Login exitoso!");
                    router.push('/dashboard'); // Redirigir al dashboard
                    return true; // Login exitoso
                } else {
                    // fetchUserData ya manejó el error y limpió el estado
                    return false; // Login fallido (validación post-login falló)
                }
            } else {
                throw new Error("No se recibió token de acceso.");
            }
        } catch (error) {
            console.error("Login processing failed:", error);
            // Verificar si es un error de email no verificado
            if (error && typeof error === 'object' && 'error_code' in error) {
                const apiError = error;
                if (apiError.error_code === "EMAIL_NOT_VERIFIED") {
                    // Guardar la información para poder reenviar el email de verificación
                    setEmailVerificationError({
                        email,
                        password,
                        message: apiError.message || "Por favor, verifica tu email para continuar."
                    });
                    // No limpiar los datos de autenticación, ya que necesitamos el token para solicitar un nuevo email
                    setIsLoading(false);
                    return false;
                }
            }
            // Para otros errores, mostrar mensaje y limpiar datos
            const errorMessage = error && typeof error === 'object' && 'message' in error ? error.message : "Error al iniciar sesión. Verifica tus credenciales.";
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(errorMessage);
            clearAuthData(); // Asegurarse de limpiar en caso de error
            setIsLoading(false); // Terminar carga en caso de error
            return false; // Login fallido
        }
    }, [
        fetchUserData,
        router,
        clearAuthData
    ]);
    // Función de registro
    const register = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (accountName, email, password)=>{
        setIsLoading(true);
        try {
            // Llamar a la API de registro
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["registerUser"])(accountName, email, password);
            const accessTokenRaw = response.access_token ?? response.accessToken;
            if (accessTokenRaw) {
                // CRITICAL FIX: Extract the API key from the registration response
                // Backend now returns apiKey in camelCase due to CamelCaseModel
                const apiKey = response.apiKey;
                const accessToken = accessTokenRaw;
                // Store both token and API key
                localStorage.setItem('rayuela-token', accessToken);
                localStorage.setItem('rayuela-apiKey', apiKey);
                // Set both in state
                setToken(accessToken);
                setApiKey(apiKey);
                // Show the API key modal immediately
                setInitialApiKeyToShow(apiKey);
                setShowApiKeyModal(true);
                // Validar el token y obtener datos del usuario
                await fetchUserData(accessToken, apiKey);
                // Return success with the API key
                return {
                    success: true,
                    apiKey: apiKey
                };
            } else {
                throw new Error("No se recibió token de acceso");
            }
        } catch (error) {
            console.error("Register processing failed:", error);
            // Mostrar mensaje de error
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$generated$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"]) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(error.message || "Error al registrarse");
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Error inesperado al registrarse");
            }
            // Limpiar datos
            clearAuthData();
            return {
                success: false,
                error
            };
        } finally{
            setIsLoading(false);
        }
    }, [
        fetchUserData,
        clearAuthData
    ]);
    // Función para manejar el cierre del modal de API Key
    const handleModalClose = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        // Guardar los valores actuales para evitar problemas de concurrencia
        const currentToken = token;
        const currentApiKey = apiKey;
        // Ocultar el modal inmediatamente para mejorar UX
        setShowApiKeyModal(false);
        setInitialApiKeyToShow(null);
        // Mostrar toast de carga
        const loadingToast = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].loading("Configurando tu cuenta...");
        setIsLoading(true);
        try {
            // Validar que tenemos los datos necesarios
            if (!currentToken || !currentApiKey) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$generated$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"]("Token o API Key no disponibles", 401, "AUTH_REQUIRED");
            }
            // Intentar obtener los datos del usuario con reintentos
            let fetchSuccess = false;
            let attempts = 0;
            const maxAttempts = 3;
            let lastError = null;
            while(!fetchSuccess && attempts < maxAttempts){
                try {
                    await fetchUserData(currentToken, currentApiKey);
                    fetchSuccess = true;
                } catch (e) {
                    lastError = e;
                    attempts++;
                    if (attempts < maxAttempts) {
                        // Esperar 500ms antes de reintentar
                        await new Promise((resolve)=>setTimeout(resolve, 500));
                    }
                }
            }
            if (!fetchSuccess && lastError) {
                throw lastError;
            }
            // Si llegamos aquí, es que todo salió bien
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].dismiss(loadingToast);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("¡Cuenta configurada correctamente!");
            router.push('/dashboard');
        } catch (error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].dismiss(loadingToast);
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$generated$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"]) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(error.message || "Error al inicializar la cuenta");
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Error inesperado al configurar la cuenta");
            }
        // No hacer logout aquí, dejar que el usuario intente de nuevo
        } finally{
            setIsLoading(false);
        }
    }, [
        token,
        apiKey,
        fetchUserData,
        router
    ]);
    // Función Logout
    const logout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            // Solo llamar a la API si hay token
            if (token) {
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["logout"])();
            }
            // Limpiar estado y localStorage
            clearAuthData();
            // Redirigir al login
            router.push('/login');
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Sesión cerrada correctamente");
        } catch (error) {
            console.error("Logout error:", error);
            // Limpiar de todas formas
            clearAuthData();
            router.push('/login');
        }
    }, [
        token,
        clearAuthData,
        router
    ]);
    // Renderizado del proveedor de contexto
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: {
            user,
            token,
            apiKey,
            setApiKey,
            login,
            register,
            logout,
            isLoading,
            emailVerificationError,
            requestNewVerificationEmail
        },
        children: [
            children,
            showApiKeyModal && initialApiKeyToShow && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$InitialApiKeyModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InitialApiKeyModal"], {
                apiKey: initialApiKeyToShow,
                onContinue: handleModalClose
            }, void 0, false, {
                fileName: "[project]/src/lib/auth.tsx",
                lineNumber: 376,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/lib/auth.tsx",
        lineNumber: 358,
        columnNumber: 5
    }, this);
};
const useAuth = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__83691a3d._.js.map