"use client";

import { useState } from 'react';
import { Button, ButtonProps } from '@/components/ui/button';
import { useAuth } from '@/lib/auth';
import { createCheckoutSession } from '@/lib/api';
import { toast } from 'sonner';
import { CreditCardIcon, LoaderIcon } from 'lucide-react';

interface BillingButtonProps extends ButtonProps {
  priceId: string;
  planName: string;
  actionType?: 'upgrade' | 'downgrade' | 'contact' | 'subscribe';
}

export function BillingButton({
  priceId,
  planName,
  actionType = 'subscribe',
  children,
  className,
  variant = "default",
  ...props
}: BillingButtonProps) {
  const { token, apiKey } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  // Función para determinar el texto del botón según el tipo de acción
  const getButtonText = () => {
    if (actionType === 'upgrade') {
      return `Actualizar a ${planName}`;
    } else if (actionType === 'downgrade') {
      return `Cambiar a ${planName}`;
    } else if (actionType === 'contact') {
      return 'Contactar con Ventas';
    } else {
      return `Suscribirse a ${planName}`;
    }
  };

  const handleBilling = async () => {
    if (!token || !apiKey) {
      toast.error("Debes iniciar sesión para realizar esta acción");
      return;
    }

    setIsLoading(true);
    try {
      // Si es contacto con ventas, redirigir a un formulario o página de contacto
      if (actionType === 'contact') {
        window.location.href = '/contact-sales';
        return;
      }

      // Llamar a la API para crear una sesión de checkout con Mercado Pago
      const response = await createCheckoutSession(priceId) as { url?: string };

      // Redirigir a la página de checkout
      if (response.url) {
        window.location.href = response.url;
      } else {
        throw new Error("No se recibió una URL de redirección");
      }
    } catch (error: unknown) {
      console.error("Error al crear sesión de checkout:", error);
      toast.error((error as Error).message || `Error al procesar la suscripción al plan ${planName}`);
      setIsLoading(false);
    }
    // No necesitamos un finally con setIsLoading(false) porque la página se recargará después de la redirección
  };

  return (
    <Button
      onClick={handleBilling}
      disabled={isLoading}
      className={className}
      variant={variant}
      {...props}
    >
      {isLoading ? (
        <>
          <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
          Procesando...
        </>
      ) : (
        <>
          <CreditCardIcon className="mr-2 h-4 w-4" />
          {children || getButtonText()}
        </>
      )}
    </Button>
  );
}
