"use client";

import { useState, useEffect } from "react";
import { CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { DensePageLayout, DenseCard } from "@/components/ui/layout";
import { InfoIcon, ExternalLinkIcon, BrainCircuit, Calendar, Gauge } from "lucide-react";
import { useModels, type ModelInfo } from "@/lib/hooks/useModels";
import { format } from "date-fns";
import { es } from "date-fns/locale";

export default function ModelsPage() {
  const { models, isLoading, error } = useModels();

  // Estado para el error
  const [errorState, setErrorState] = useState<string | null>(null);

  // Actualizar el estado de error cuando cambia error
  useEffect(() => {
    if (error) {
      console.error("Error al obtener datos de modelos:", error);
      const errorMessage = typeof error === 'string' 
        ? error 
        : "Error al cargar los datos de modelos";
      setErrorState(errorMessage);
    } else {
      setErrorState(null);
    }
  }, [error]);

  // Function to render trained models
  const renderTrainedModels = () => {
    if (isLoading) {
      return (
        <div className="space-y-4">
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-24 w-full" />
        </div>
      );
    }

    if (errorState) {
      return (
        <Alert variant="destructive">
          <AlertTitle>Error cargando modelos</AlertTitle>
          <AlertDescription>{errorState}</AlertDescription>
        </Alert>
      );
    }

    if (models.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center h-40 border-2 border-dashed rounded-md border-gray-300 dark:border-gray-700 p-4">
          <BrainCircuit className="h-8 w-8 text-gray-400 dark:text-gray-500 mb-2" />
          <p className="text-gray-500 dark:text-gray-400 text-center">
            No hay modelos entrenados aún
          </p>
          <p className="text-sm text-muted-foreground text-center">
            Los modelos aparecerán aquí después de completar un entrenamiento
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {models.map((model: ModelInfo) => (
          <div key={model.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
            <div className="flex items-start justify-between">
              <div className="flex-grow">
                <div className="flex items-center gap-3 mb-2">
                  <BrainCircuit className="h-5 w-5 text-purple-500" />
                  <h3 className="font-semibold text-gray-900 dark:text-white">
                    {model.artifact_name}
                  </h3>
                  <Badge variant="secondary">
                    {model.artifact_version}
                  </Badge>
                </div>
                
                {model.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    {model.description}
                  </p>
                )}

                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>
                      Entrenado: {format(new Date(model.training_date), 'dd/MM/yyyy HH:mm', { locale: es })}
                    </span>
                  </div>
                  
                  {model.performance_metrics && Object.keys(model.performance_metrics).length > 0 && (
                    <div className="flex items-center gap-1">
                      <Gauge className="h-4 w-4" />
                      <span>
                        {Object.entries(model.performance_metrics).slice(0, 2).map(([key, value]) => 
                          `${key}: ${typeof value === 'number' ? value.toFixed(3) : value}`
                        ).join(', ')}
                      </span>
                    </div>
                  )}
                </div>

                {model.parameters && Object.keys(model.parameters).length > 0 && (
                  <div className="mt-2 text-xs text-muted-foreground">
                    <strong>Parámetros:</strong> {Object.entries(model.parameters).slice(0, 3).map(([key, value]) => 
                      `${key}: ${value}`
                    ).join(', ')}
                  </div>
                )}
              </div>
              
              <Badge variant="success">
                Disponible
              </Badge>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <DensePageLayout
      title="Models"
      description="Manage and configure your recommendation models."
    >
      <Alert variant="info">
        <InfoIcon className="h-4 w-4" />
        <AlertTitle>Próximamente</AlertTitle>
        <AlertDescription>
          <p>La gestión avanzada de modelos estará disponible en nuestra próxima actualización, prevista para el Q3 2023.</p>
          <p className="mt-2">Esta sección te permitirá:</p>
          <ul className="list-disc list-inside mt-1 ml-2 space-y-1">
            <li>Entrenar modelos personalizados con tus propios datos</li>
            <li>Ajustar parámetros de recomendación</li>
            <li>Monitorear el rendimiento de tus modelos</li>
            <li>Implementar A/B testing entre diferentes modelos</li>
          </ul>
        </AlertDescription>
      </Alert>

      {/* Placeholder content */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <DenseCard
          title="Modelos Disponibles"
          description="Modelos que puedes usar para generar recomendaciones"
          icon={<BrainCircuit className="h-5 w-5" />}
          headerActions={
            <Button asChild variant="outline" size="sm">
              <a
                href="https://docs.rayuela.ai/models/overview"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center"
              >
                <ExternalLinkIcon className="mr-2 h-4 w-4" />
                Ver documentación
              </a>
            </Button>
          }
        >
          <CardContent>
            {renderTrainedModels()}
          </CardContent>
        </DenseCard>

        <DenseCard
          title="Modelos Entrenados"
          description="Tus modelos de recomendación personalizados entrenados"
          icon={<Gauge className="h-5 w-5" />}
          headerActions={
            <Button variant="outline" size="sm" disabled>
              Ver detalles de métricas
            </Button>
          }
        >
          <CardContent>
            {renderTrainedModels()}
          </CardContent>
        </DenseCard>
      </div>
    </DensePageLayout>
  );
}
