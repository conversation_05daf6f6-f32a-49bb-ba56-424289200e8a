import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowLeft, Upload, Database, Clock, AlertTriangle } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'API de Ingesta Masiva - Rayuela.ai',
  description: 'Documentación completa del endpoint de ingesta masiva de Rayuela. Carga productos, usuarios e interacciones en lotes para máxima eficiencia.',
  path: '/docs/api/batch',
  keywords: ['API', 'ingesta masiva', 'batch', 'productos', 'usuarios', 'interacciones', 'bulk upload'],
});

export default function BatchApiPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="mb-8">
          <Button variant="ghost" asChild className="mb-4">
            <Link href="/docs">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver a Documentación
            </Link>
          </Button>
          
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            API de Ingesta Masiva
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl">
            Carga grandes volúmenes de datos de forma eficiente usando el endpoint de ingesta masiva.
          </p>
        </div>

        {/* Overview */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="w-5 h-5" />
              Visión General
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              El endpoint de ingesta masiva permite cargar múltiples productos, usuarios e interacciones 
              en una sola operación, optimizando el rendimiento para grandes volúmenes de datos.
            </p>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="flex items-center gap-2">
                <Database className="w-5 h-5 text-primary" />
                <span className="text-sm">Hasta 1000 elementos por tipo</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-primary" />
                <span className="text-sm">Procesamiento asíncrono</span>
              </div>
              <div className="flex items-center gap-2">
                <Upload className="w-5 h-5 text-primary" />
                <span className="text-sm">Operación atómica</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Endpoint */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Endpoint Principal</CardTitle>
            <div className="flex gap-2 mt-2">
              <Badge variant="default">POST</Badge>
              <Badge variant="outline">/ingestion/batch</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Carga múltiples tipos de datos en una sola operación.
            </p>

            <h4 className="font-semibold mb-3">Estructura del Request</h4>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm mb-4">
              <pre className="text-sm font-mono overflow-x-auto">
{`{
  "products": [
    {
      "external_id": "prod_001",
      "name": "Producto 1",
      "description": "Descripción del producto",
      "price": 99.99,
      "category": "electronics",
      "brand": "Apple",
      "in_stock": true,
      "attributes": {
        "color": "black",
        "storage": "128GB"
      }
    }
  ],
  "end_users": [
    {
      "external_id": "user_001",
      "preferred_categories": ["electronics"],
      "price_range_min": 50,
      "price_range_max": 1000,
      "attributes": {
        "age": 28,
        "location": "Madrid"
      }
    }
  ],
  "interactions": [
    {
      "user_external_id": "user_001",
      "product_external_id": "prod_001",
      "interaction_type": "purchase",
      "value": 1.0,
      "timestamp": "2024-01-15T10:30:00Z"
    }
  ]
}`}
              </pre>
            </div>

            <h4 className="font-semibold mb-3">Ejemplo de Request</h4>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
              <pre className="text-sm font-mono overflow-x-auto">
{`curl -X POST "https://api.rayuela.ai/api/v1/ingestion/batch" \\
     -H "X-API-Key: sk_prod_tu_api_key" \\
     -H "Content-Type: application/json" \\
     -d '{
       "products": [
         {
           "external_id": "smartphone_001",
           "name": "iPhone 15 Pro",
           "price": 1199.99,
           "category": "electronics/smartphones",
           "brand": "Apple"
         }
       ],
       "interactions": [
         {
           "user_external_id": "user_123",
           "product_external_id": "smartphone_001",
           "interaction_type": "view",
           "value": 1.0
         }
       ]
     }'`}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Response Format */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Formato de Respuesta</CardTitle>
          </CardHeader>
          <CardContent>
            <h4 className="font-semibold mb-3">Respuesta Exitosa (200)</h4>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm mb-4">
              <pre className="text-sm font-mono overflow-x-auto">
{`{
  "message": "Ingesta masiva completada exitosamente",
  "job_id": "batch_job_abc123",
  "summary": {
    "products_created": 150,
    "products_updated": 25,
    "end_users_created": 75,
    "end_users_updated": 10,
    "interactions_created": 500
  },
  "processing_time_ms": 1250,
  "timestamp": "2024-01-15T10:30:00Z"
}`}
              </pre>
            </div>

            <h4 className="font-semibold mb-3">Respuesta con Errores Parciales (207)</h4>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
              <pre className="text-sm font-mono overflow-x-auto">
{`{
  "message": "Ingesta masiva completada con errores parciales",
  "job_id": "batch_job_def456",
  "summary": {
    "products_created": 140,
    "products_failed": 10,
    "interactions_created": 480,
    "interactions_failed": 20
  },
  "errors": [
    {
      "type": "product",
      "external_id": "prod_invalid",
      "error": "Precio inválido: debe ser mayor a 0"
    }
  ]
}`}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Limits and Constraints */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Límites y Restricciones</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">Límites por Request</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Máximo 1000 productos</li>
                    <li>• Máximo 1000 usuarios</li>
                    <li>• Máximo 1000 interacciones</li>
                    <li>• Tamaño máximo: 10MB</li>
                  </ul>
                </div>
                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">Límites de Rate</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• 10 requests por minuto</li>
                    <li>• 100 requests por hora</li>
                    <li>• 1000 requests por día</li>
                  </ul>
                </div>
              </div>

              <div className="p-4 bg-warning/10 border border-warning/20 rounded-lg">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="w-5 h-5 text-warning-foreground mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-warning-foreground mb-1">Importante</h4>
                    <p className="text-sm text-warning-foreground">
                      Para volúmenes mayores a los límites, divide los datos en múltiples requests. 
                      La operación es atómica: si falla, ningún dato se procesa.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Validation Rules */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Reglas de Validación</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Productos</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• <code>external_id</code>: Requerido, único, máximo 100 caracteres</li>
                  <li>• <code>name</code>: Requerido, máximo 200 caracteres</li>
                  <li>• <code>price</code>: Requerido, debe ser mayor a 0</li>
                  <li>• <code>category</code>: Requerido, máximo 50 caracteres</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Usuarios</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• <code>external_id</code>: Requerido, único, máximo 100 caracteres</li>
                  <li>• <code>price_range_min/max</code>: Opcional, debe ser mayor a 0</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Interacciones</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• <code>user_external_id</code> y <code>product_external_id</code>: Requeridos</li>
                  <li>• <code>interaction_type</code>: Debe ser view, purchase, rating, o cart_add</li>
                  <li>• <code>value</code>: Requerido, debe ser mayor a 0</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error Codes */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Códigos de Error</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Badge variant="destructive">400</Badge>
                <span className="text-sm">Bad Request - Datos inválidos o límites excedidos</span>
              </div>
              <div className="flex items-center gap-3">
                <Badge variant="destructive">401</Badge>
                <span className="text-sm">Unauthorized - API Key inválida</span>
              </div>
              <div className="flex items-center gap-3">
                <Badge variant="destructive">413</Badge>
                <span className="text-sm">Payload Too Large - Excede 10MB</span>
              </div>
              <div className="flex items-center gap-3">
                <Badge variant="destructive">429</Badge>
                <span className="text-sm">Rate Limit Exceeded - Demasiadas requests</span>
              </div>
              <div className="flex items-center gap-3">
                <Badge variant="destructive">500</Badge>
                <span className="text-sm">Internal Server Error - Error del servidor</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Siguientes Pasos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <Button asChild variant="outline">
                <Link href="/docs/guides/data-ingestion">
                  Guía de Datos
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/docs/api/recommendations">
                  Obtener Recomendaciones
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/docs/quickstart/python">
                  Ejemplos de Código
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/dashboard">
                  Ver Dashboard
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
