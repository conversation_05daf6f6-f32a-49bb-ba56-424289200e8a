"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  CheckCircle, 
  ArrowRight, 
  Copy, 
  Play, 
  Zap, 
  Target,
  Rocket,
  Code,
  BarChart3,
  Users,
  ShoppingCart,
  Clock,
  Star,
  TrendingUp
} from "lucide-react";

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  estimatedTime: string;
  completed: boolean;
}

interface UserProfile {
  name: string;
  email: string;
  company: string;
  role: string;
  useCase: string;
  industry: string;
}

export default function OnboardingFlow() {
  const [currentStep, setCurrentStep] = useState(0);
  const [userProfile, setUserProfile] = useState<UserProfile>({
    name: '',
    email: '',
    company: '',
    role: '',
    useCase: '',
    industry: ''
  });
  const [apiKey, setApiKey] = useState('');
  const [testResults, setTestResults] = useState<any>(null);
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  const steps: OnboardingStep[] = [
    {
      id: 'profile',
      title: 'Tell us about yourself',
      description: 'Help us personalize your experience',
      estimatedTime: '1 min',
      completed: false
    },
    {
      id: 'setup',
      title: 'Get your API key',
      description: 'Generate your unique API credentials',
      estimatedTime: '30 sec',
      completed: false
    },
    {
      id: 'test',
      title: 'Test with sample data',
      description: 'See recommendations in action',
      estimatedTime: '2 min',
      completed: false
    },
    {
      id: 'integrate',
      title: 'Copy integration code',
      description: 'Get production-ready code',
      estimatedTime: '3 min',
      completed: false
    },
    {
      id: 'deploy',
      title: 'Deploy and monitor',
      description: 'Go live and track results',
      estimatedTime: '5 min',
      completed: false
    }
  ];

  const [completedSteps, setCompletedSteps] = useState<string[]>([]);

  const markStepCompleted = (stepId: string) => {
    if (!completedSteps.includes(stepId)) {
      setCompletedSteps([...completedSteps, stepId]);
    }
  };

  const generateApiKey = () => {
    const key = `ray_${Math.random().toString(36).substr(2, 9)}_${Date.now().toString(36)}`;
    setApiKey(key);
    markStepCompleted('setup');
  };

  const runSampleTest = async () => {
    // Simulate API call
    setTimeout(() => {
      const sampleResults = {
        items: [
          { id: 1, name: "Wireless Headphones", category: "Electronics", price: 99.99, score: 0.95, explanation: "Popular in your industry" },
          { id: 2, name: "Coffee Maker", category: "Home", price: 149.99, score: 0.87, explanation: "Trending this week" },
          { id: 3, name: "Running Shoes", category: "Sports", price: 129.99, score: 0.82, explanation: "High customer satisfaction" }
        ],
        metrics: {
          ctr_lift: 0.23,
          cvr_lift: 0.15,
          response_time: 45
        }
      };
      setTestResults(sampleResults);
      markStepCompleted('test');
    }, 1500);
  };

  const copyToClipboard = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedCode(id);
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const getPersonalizedCode = () => {
    const useCase = userProfile.useCase;
    const industry = userProfile.industry;
    
    if (useCase === 'ecommerce') {
      return `// ${userProfile.company} - E-commerce Integration
import Rayuela from 'rayuela-sdk';

const client = new Rayuela({ 
  apiKey: '${apiKey}',
  industry: '${industry}'
});

// Homepage recommendations
const recommendations = await client.ecommerce('user-123', {
  page: 'homepage',
  limit: 12,
  strategy: 'balanced'
});

// Track user interactions
await client.track({
  userId: 'user-123',
  productId: 'product-456',
  type: 'click'
});

console.log(\`Got \${recommendations.items.length} recommendations\`);
console.log(\`CTR Lift: \${recommendations.metrics?.ctr_lift || 0}%\`);`;
    } else if (useCase === 'content') {
      return `// ${userProfile.company} - Content Recommendations
import Rayuela from 'rayuela-sdk';

const client = new Rayuela({ 
  apiKey: '${apiKey}',
  industry: '${industry}'
});

// Content recommendations
const recommendations = await client.recommend('user-123', {
  limit: 10,
  strategy: 'discovery',
  context: { page: 'article', category: 'technology' }
});

// Track engagement
await client.track({
  userId: 'user-123',
  contentId: 'article-789',
  type: 'view',
  duration: 120 // seconds
});`;
    } else {
      return `// ${userProfile.company} - Custom Integration
import Rayuela from 'rayuela-sdk';

const client = new Rayuela({ 
  apiKey: '${apiKey}',
  industry: '${industry}'
});

// Get personalized recommendations
const recommendations = await client.recommend('user-123', {
  limit: 10,
  strategy: 'balanced'
});

// A/B test against baseline
const abTest = await client.abTest('user-123', { limit: 10 });
console.log(\`Variant: \${abTest.variant}\`);
console.log(\`Performance lift: \${abTest.lifts?.ctrLiftPercentage}\`);`;
    }
  };

  const getEstimatedROI = () => {
    const baseRevenue = userProfile.company === 'Enterprise' ? 10000000 : 
                       userProfile.company === 'Mid-market' ? 1000000 : 100000;
    
    const ctrLift = 0.23; // 23% average
    const cvrLift = 0.15; // 15% average
    const monthlyIncrease = baseRevenue * (ctrLift + cvrLift) * 0.1; // Conservative estimate
    
    return {
      monthly: monthlyIncrease,
      annual: monthlyIncrease * 12,
      roi: (monthlyIncrease * 12) / 12000 // Assuming $1k/month cost
    };
  };

  const progressPercentage = (completedSteps.length / steps.length) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-2">
            Welcome to Rayuela! 🚀
          </h1>
          <p className="text-xl text-muted-foreground mb-4">
            Get personalized recommendations running in less than 10 minutes
          </p>
          <div className="flex items-center justify-center gap-4 mb-6">
            <Progress value={progressPercentage} className="w-64" />
            <span className="text-sm font-medium">
              {completedSteps.length}/{steps.length} completed
            </span>
          </div>
        </div>

        {/* Steps Navigation */}
        <div className="flex justify-center mb-8">
          <div className="flex items-center space-x-4">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`
                  w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium
                  ${completedSteps.includes(step.id) 
                    ? 'bg-green-500 text-white' 
                    : currentStep === index 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-200 text-muted-foreground'
                  }
                `}>
                  {completedSteps.includes(step.id) ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    index + 1
                  )}
                </div>
                {index < steps.length - 1 && (
                  <ArrowRight className="h-4 w-4 text-muted-foreground/70 mx-2" />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Current Step Content */}
        <Card className="mb-8">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  Step {currentStep + 1}: {steps[currentStep].title}
                </CardTitle>
                <CardDescription>
                  {steps[currentStep].description} • {steps[currentStep].estimatedTime}
                </CardDescription>
              </div>
              <Badge variant="outline">
                <Clock className="h-3 w-3 mr-1" />
                {steps[currentStep].estimatedTime}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            {/* Step 1: Profile */}
            {currentStep === 0 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      value={userProfile.name}
                      onChange={(e) => setUserProfile({...userProfile, name: e.target.value})}
                      placeholder="John Doe"
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={userProfile.email}
                      onChange={(e) => setUserProfile({...userProfile, email: e.target.value})}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label htmlFor="company">Company</Label>
                    <Input
                      id="company"
                      value={userProfile.company}
                      onChange={(e) => setUserProfile({...userProfile, company: e.target.value})}
                      placeholder="Acme Corp"
                    />
                  </div>
                  <div>
                    <Label htmlFor="role">Role</Label>
                    <select 
                      className="w-full p-2 border rounded-md"
                      value={userProfile.role}
                      onChange={(e) => setUserProfile({...userProfile, role: e.target.value})}
                    >
                      <option value="">Select role</option>
                      <option value="developer">Developer</option>
                      <option value="product-manager">Product Manager</option>
                      <option value="cto">CTO</option>
                      <option value="founder">Founder</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Primary Use Case</Label>
                    <div className="grid grid-cols-1 gap-2 mt-2">
                      {['ecommerce', 'content', 'marketplace', 'saas'].map((useCase) => (
                        <label key={useCase} className="flex items-center space-x-2 cursor-pointer">
                          <input
                            type="radio"
                            name="useCase"
                            value={useCase}
                            checked={userProfile.useCase === useCase}
                            onChange={(e) => setUserProfile({...userProfile, useCase: e.target.value})}
                          />
                          <span className="capitalize">{useCase}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  <div>
                    <Label>Industry</Label>
                    <select 
                      className="w-full p-2 border rounded-md mt-2"
                      value={userProfile.industry}
                      onChange={(e) => setUserProfile({...userProfile, industry: e.target.value})}
                    >
                      <option value="">Select industry</option>
                      <option value="retail">Retail</option>
                      <option value="media">Media & Entertainment</option>
                      <option value="fintech">Fintech</option>
                      <option value="healthcare">Healthcare</option>
                      <option value="education">Education</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                </div>

                <Button 
                  onClick={() => {
                    markStepCompleted('profile');
                    setCurrentStep(1);
                  }}
                  disabled={!userProfile.name || !userProfile.email || !userProfile.useCase}
                  className="w-full"
                >
                  Continue to API Setup
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            )}

            {/* Step 2: API Key */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="text-center">
                  <Zap className="h-16 w-16 text-blue-500 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Generate Your API Key</h3>
                  <p className="text-muted-foreground mb-6">
                    Your personalized API key for {userProfile.company}
                  </p>
                </div>

                {!apiKey ? (
                  <Button onClick={generateApiKey} size="lg" className="w-full">
                    <Rocket className="h-5 w-5 mr-2" />
                    Generate API Key
                  </Button>
                ) : (
                  <div className="space-y-4">
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-green-800">API Key Generated!</h4>
                          <code className="text-sm text-green-700 font-mono">{apiKey}</code>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(apiKey, 'api-key')}
                        >
                          {copiedCode === 'api-key' ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>
                    
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <h4 className="font-medium text-blue-800 mb-2">Quick Setup Instructions</h4>
                      <ol className="text-sm text-blue-700 space-y-1">
                        <li>1. Save your API key securely</li>
                        <li>2. Add it to your environment variables</li>
                        <li>3. Install the SDK: <code>npm install rayuela-sdk</code></li>
                      </ol>
                    </div>

                    <Button 
                      onClick={() => setCurrentStep(2)}
                      className="w-full"
                    >
                      Test with Sample Data
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                )}
              </div>
            )}

            {/* Step 3: Test */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div className="text-center">
                  <Target className="h-16 w-16 text-green-500 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Test Recommendations</h3>
                  <p className="text-muted-foreground mb-6">
                    See how Rayuela works with sample {userProfile.useCase} data
                  </p>
                </div>

                {!testResults ? (
                  <Button onClick={runSampleTest} size="lg" className="w-full">
                    <Play className="h-5 w-5 mr-2" />
                    Run Sample Test
                  </Button>
                ) : (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                      <Card>
                        <CardContent className="p-4 text-center">
                          <TrendingUp className="h-8 w-8 text-green-500 mx-auto mb-2" />
                          <div className="text-2xl font-bold text-green-600">+23%</div>
                          <div className="text-sm text-muted-foreground">CTR Lift</div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardContent className="p-4 text-center">
                          <BarChart3 className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                          <div className="text-2xl font-bold text-blue-600">+15%</div>
                          <div className="text-sm text-muted-foreground">CVR Lift</div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardContent className="p-4 text-center">
                          <Clock className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                          <div className="text-2xl font-bold text-purple-600">45ms</div>
                          <div className="text-sm text-muted-foreground">Response Time</div>
                        </CardContent>
                      </Card>
                    </div>

                    <div className="space-y-3">
                      <h4 className="font-medium">Sample Recommendations:</h4>
                      {testResults.items.map((item: any, index: number) => (
                        <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <div className="font-medium">{item.name}</div>
                            <div className="text-sm text-muted-foreground">{item.category} • ${item.price}</div>
                            <div className="text-xs text-blue-600">💡 {item.explanation}</div>
                          </div>
                          <div className="text-right">
                            <Badge variant="outline">
                              {(item.score * 100).toFixed(0)}%
                            </Badge>
                            <div className="text-xs text-muted-foreground">#{index + 1}</div>
                          </div>
                        </div>
                      ))}
                    </div>

                    <Button 
                      onClick={() => setCurrentStep(3)}
                      className="w-full"
                    >
                      Get Integration Code
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                )}
              </div>
            )}

            {/* Step 4: Integration Code */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <div className="text-center">
                  <Code className="h-16 w-16 text-purple-500 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Your Personalized Code</h3>
                  <p className="text-muted-foreground mb-6">
                    Production-ready code tailored for {userProfile.company}
                  </p>
                </div>

                <div className="relative">
                  <pre className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm">
                    <code>{getPersonalizedCode()}</code>
                  </pre>
                  <Button
                    size="sm"
                    variant="outline"
                    className="absolute top-2 right-2"
                    onClick={() => copyToClipboard(getPersonalizedCode(), 'integration-code')}
                  >
                    {copiedCode === 'integration-code' ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <h4 className="font-medium mb-2">Estimated ROI</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Monthly increase:</span>
                          <span className="font-medium">${getEstimatedROI().monthly.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Annual increase:</span>
                          <span className="font-medium">${getEstimatedROI().annual.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">ROI:</span>
                          <span className="font-medium text-green-600">{getEstimatedROI().roi.toFixed(1)}x</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-4">
                      <h4 className="font-medium mb-2">Next Steps</h4>
                      <ul className="text-sm space-y-1">
                        <li>✅ Copy the integration code</li>
                        <li>✅ Add to your {userProfile.useCase} app</li>
                        <li>✅ Deploy and start tracking</li>
                        <li>✅ Monitor performance metrics</li>
                      </ul>
                    </CardContent>
                  </Card>
                </div>

                <div className="flex gap-4">
                  <Button 
                    onClick={() => {
                      markStepCompleted('integrate');
                      setCurrentStep(4);
                    }}
                    className="flex-1"
                  >
                    Deploy & Monitor
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                  <Button variant="outline" onClick={() => copyToClipboard(getPersonalizedCode(), 'final-code')}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Code
                  </Button>
                </div>
              </div>
            )}

            {/* Step 5: Deploy */}
            {currentStep === 4 && (
              <div className="space-y-6 text-center">
                <div>
                  <Star className="h-20 w-20 text-yellow-500 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold mb-2">🎉 Congratulations!</h3>
                  <p className="text-lg text-muted-foreground mb-6">
                    You've successfully set up Rayuela recommendations for {userProfile.company}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardContent className="p-6 text-center">
                      <Users className="h-12 w-12 text-blue-500 mx-auto mb-3" />
                      <h4 className="font-semibold mb-2">Join Community</h4>
                      <p className="text-sm text-muted-foreground mb-4">Connect with other developers</p>
                      <Button variant="outline" size="sm">Join Discord</Button>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-6 text-center">
                      <BarChart3 className="h-12 w-12 text-green-500 mx-auto mb-3" />
                      <h4 className="font-semibold mb-2">Monitor Results</h4>
                      <p className="text-sm text-muted-foreground mb-4">Track your performance</p>
                      <Button variant="outline" size="sm">View Dashboard</Button>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-6 text-center">
                      <Rocket className="h-12 w-12 text-purple-500 mx-auto mb-3" />
                      <h4 className="font-semibold mb-2">Scale Up</h4>
                      <p className="text-sm text-muted-foreground mb-4">Explore advanced features</p>
                      <Button variant="outline" size="sm">Upgrade Plan</Button>
                    </CardContent>
                  </Card>
                </div>

                <Button 
                  onClick={() => markStepCompleted('deploy')}
                  size="lg"
                  className="w-full"
                >
                  <CheckCircle className="h-5 w-5 mr-2" />
                  Complete Onboarding
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Progress Summary */}
        {completedSteps.length > 0 && (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Onboarding Progress</h4>
                  <p className="text-sm text-muted-foreground">
                    {completedSteps.length} of {steps.length} steps completed
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-green-600">
                    {Math.round(progressPercentage)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Complete</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
