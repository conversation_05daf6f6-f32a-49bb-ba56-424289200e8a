/* === Animaciones utilitarias === */
@import "tw-animate-css";

/* === Tailwind layers: declara una sola vez === */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* =========================================================
   TOKENS & THEME
   =======================================================*/
@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.129 0.042 264.695);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.129 0.042 264.695);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.129 0.042 264.695);
  --primary: 252 86% 53%;
  --primary-foreground: 0 0% 100%;
  --secondary: oklch(0.968 0.007 247.896);
  --secondary-foreground: oklch(0.208 0.042 265.755);
  --muted: oklch(0.968 0.007 247.896);
  --muted-foreground: oklch(0.554 0.046 257.417);
  --accent: 284 62% 57%;
  --accent-foreground: 0 0% 100%;
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.929 0.013 255.508);
  --input: oklch(0.929 0.013 255.508);
  --ring: oklch(0.704 0.04 256.788);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.984 0.003 247.858);
  --sidebar-foreground: oklch(0.129 0.042 264.695);
  --sidebar-primary: oklch(0.208 0.042 265.755);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.968 0.007 247.896);
  --sidebar-accent-foreground: oklch(0.208 0.042 265.755);
  --sidebar-border: oklch(0.929 0.013 255.508);
  --sidebar-ring: oklch(0.704 0.04 256.788);
  
  /* Colores semánticos adicionales armonizados con la paleta oklch */
  --success: oklch(0.646 0.222 145.0);
  --success-foreground: oklch(0.984 0.003 247.858);
  --success-light: oklch(0.97 0.022 145.0);
  --warning: oklch(0.828 0.189 84.429);
  --warning-foreground: oklch(0.129 0.042 264.695);
  --warning-light: oklch(0.97 0.022 84.429);
  --info: oklch(0.6 0.118 220.0);
  --info-foreground: oklch(0.984 0.003 247.858);
  --info-light: oklch(0.97 0.022 220.0);
  
  /* Variables para gradientes de marketing unificados */
  --marketing-gradient-start: oklch(0.984 0.003 247.858);
  --marketing-gradient-end: oklch(0.968 0.007 247.896);
  --marketing-gradient-accent: oklch(0.929 0.013 255.508);

  /* Variables para gradientes semánticos unificados */
  --gradient-success-start: oklch(0.646 0.222 145.0);
  --gradient-success-end: oklch(0.828 0.189 84.429);
  --gradient-info-start: oklch(0.6 0.118 220.0);
  --gradient-info-end: oklch(0.488 0.243 264.376);
  --gradient-brand-start: oklch(0.704 0.04 256.788);
  --gradient-brand-end: oklch(0.627 0.265 303.9);
}

.dark {
  --background: oklch(0.129 0.042 264.695);
  --foreground: oklch(0.984 0.003 247.858);
  --card: oklch(0.208 0.042 265.755);
  --card-foreground: oklch(0.984 0.003 247.858);
  --popover: oklch(0.208 0.042 265.755);
  --popover-foreground: oklch(0.984 0.003 247.858);
  --primary: 252 100% 70%;
  --primary-foreground: 0 0% 100%;
  --secondary: oklch(0.279 0.041 260.031);
  --secondary-foreground: oklch(0.984 0.003 247.858);
  --muted: oklch(0.279 0.041 260.031);
  --muted-foreground: oklch(0.704 0.04 256.788);
  --accent: 284 100% 75%;
  --accent-foreground: 0 0% 100%;
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.551 0.027 264.364);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.208 0.042 265.755);
  --sidebar-foreground: oklch(0.984 0.003 247.858);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.279 0.041 260.031);
  --sidebar-accent-foreground: oklch(0.984 0.003 247.858);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.551 0.027 264.364);
  
  /* Colores semánticos para modo oscuro */
  --success: oklch(0.696 0.17 145.0);
  --success-foreground: oklch(0.984 0.003 247.858);
  --success-light: oklch(0.279 0.041 145.0);
  --warning: oklch(0.769 0.188 84.429);
  --warning-foreground: oklch(0.984 0.003 247.858);
  --warning-light: oklch(0.279 0.041 84.429);
  --info: oklch(0.488 0.243 220.0);
  --info-foreground: oklch(0.984 0.003 247.858);
  --info-light: oklch(0.279 0.041 220.0);
  
  /* Gradientes de marketing para modo oscuro */
  --marketing-gradient-start: oklch(0.129 0.042 264.695);
  --marketing-gradient-end: oklch(0.208 0.042 265.755);
  --marketing-gradient-accent: oklch(0.279 0.041 260.031);

  /* Gradientes semánticos para modo oscuro */
  --gradient-success-start: oklch(0.696 0.17 145.0);
  --gradient-success-end: oklch(0.769 0.188 84.429);
  --gradient-info-start: oklch(0.488 0.243 220.0);
  --gradient-info-end: oklch(0.627 0.265 303.9);
  --gradient-brand-start: oklch(0.551 0.027 264.364);
  --gradient-brand-end: oklch(0.488 0.243 264.376);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Optimización de carga de fuentes */
  @font-face {
    font-family: 'Inter';
    font-display: swap;
  }
}

@layer components {
  /* Clases tipográficas semánticas */
  .text-display {
    @apply text-display-md font-bold tracking-extra-tight;
  }
  
  .text-display-large {
    @apply text-display-lg font-bold tracking-extra-tight;
  }
  
  .text-heading {
    @apply text-heading-lg font-semibold tracking-tight;
  }
  
  .text-heading-large {
    @apply text-heading-xl font-semibold tracking-tight;
  }
  
  .text-subheading {
    @apply text-heading-md font-medium;
  }
  
  .text-body {
    @apply text-body-md leading-body;
  }
  
  .text-body-large {
    @apply text-body-lg leading-body;
  }
  
  .text-caption {
    @apply text-sm font-medium text-muted-foreground;
  }
  
  .text-caption-large {
    @apply text-caption-lg font-medium text-muted-foreground;
  }
  
  .text-overline {
    @apply text-caption-sm font-semibold uppercase tracking-wider text-muted-foreground;
  }
  
  .text-code {
    @apply font-mono text-body-sm bg-muted px-2 py-1 rounded text-foreground;
  }
  
  .text-code-inline {
    @apply font-mono text-caption-lg bg-muted px-1.5 py-0.5 rounded text-foreground;
  }

  /* Clases para números y métricas */
  .text-metric {
    @apply text-display-lg font-bold tabular-nums tracking-tightest;
  }
  
  .text-metric-large {
    @apply text-display-xl font-bold tabular-nums tracking-tightest;
  }
  
  .text-metric-small {
    @apply text-heading-lg font-bold tabular-nums tracking-tight;
  }

  /* Clases para contenido de texto largo */
  .text-prose {
    @apply text-body leading-relaxed max-w-prose;
  }
  
  .text-prose h1 {
    @apply text-display-large mb-6 mt-8 first:mt-0;
  }
  
  .text-prose h2 {
    @apply text-heading-large mb-4 mt-6;
  }
  
  .text-prose h3 {
    @apply text-heading mb-3 mt-5;
  }
  
  .text-prose h4 {
    @apply text-subheading mb-2 mt-4;
  }
  
  .text-prose p {
    @apply mb-4;
  }
  
  .text-prose ul, .text-prose ol {
    @apply mb-4 pl-6;
  }
  
  .text-prose li {
    @apply mb-1;
  }

  /* Estados de interacción mejorados */
  .text-interactive {
    @apply transition-colors duration-200 ease-in-out;
  }
  
  .text-link {
    @apply text-interactive text-primary hover:text-primary/80 underline-offset-4 hover:underline;
  }

  /* Clases para toque creativo y exploración */
  .rayuela-accent {
    @apply text-primary;
  }
  
  .rayuela-accent-bg {
    @apply bg-primary text-primary-foreground;
  }
  
  .rayuela-info-accent {
    @apply text-info;
  }
  
  .rayuela-info-accent-bg {
    @apply bg-info text-info-foreground;
  }

  /* Gradientes sutiles para profundidad */
  .rayuela-card-gradient {
    @apply bg-gradient-to-br from-background to-muted/30;
  }
  
  .rayuela-subtle-gradient {
    @apply bg-gradient-to-br from-card to-card/90;
  }
  
  .rayuela-marketing-gradient {
    @apply bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end;
  }

  /* === UTILIDADES DE INTERACCIÓN Y ANIMACIÓN RAYUELA === */

  /* Base para todas las transiciones interactivas */
  .rayuela-interactive {
    @apply transition-all duration-200 ease-in-out;
  }

  /* Efecto de elevación sutil al hacer hover */
  .rayuela-hover-lift {
    @apply rayuela-interactive hover:-translate-y-0.5;
  }

  /* Efecto de "presión" al hacer click */
  .rayuela-active-press {
    @apply active:scale-95 transition-transform duration-75;
  }

  /* Sombras para hover en botones y tarjetas */
  .rayuela-shadow-hover {
    @apply shadow-sm hover:shadow-soft;
  }

  /* Efecto de escala sutil en hover */
  .rayuela-scale-hover {
    @apply hover:scale-[1.02];
  }

  /* Combinaciones específicas para componentes */
  .rayuela-card-hover {
    @apply rayuela-interactive rayuela-hover-lift rayuela-shadow-hover;
  }

  .rayuela-button-hover {
    @apply rayuela-interactive rayuela-hover-lift rayuela-shadow-hover rayuela-active-press;
  }

  /* === ANIMACIONES DE ENTRADA === */

  /* Animaciones de entrada para elementos que aparecen */
  .rayuela-fade-in {
    animation: fade-in 0.6s ease-out;
  }

  .rayuela-scale-in {
    animation: scale-in 0.4s ease-out;
  }

  .rayuela-slide-up {
    animation: slide-up 0.5s ease-out;
  }

  /* Utilidades para animaciones staggered */
  .rayuela-stagger-1 {
    animation-delay: 0.1s;
  }

  .rayuela-stagger-2 {
    animation-delay: 0.2s;
  }

  .rayuela-stagger-3 {
    animation-delay: 0.3s;
  }

  .rayuela-stagger-4 {
    animation-delay: 0.4s;
  }

  /* === OTRAS UTILIDADES DE ESTILO RAYUELA === */

  /* Gradient text effect */
  .rayuela-gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Enhanced shadows */
  .rayuela-shadow-glow {
    box-shadow: 0 0 20px -5px hsl(var(--primary) / 0.3);
  }

  /* Pulse animation for CTAs */
  .rayuela-pulse-cta {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* === RESPETO A PREFERENCIAS DE MOVIMIENTO === */
  @media (prefers-reduced-motion: reduce) {
    .rayuela-fade-in, .rayuela-scale-in, .rayuela-slide-up,
    .rayuela-interactive, .rayuela-hover-lift, .rayuela-active-press, .rayuela-scale-hover,
    .rayuela-pulse-cta {
      animation: none !important;
      transition: none !important;
      transform: none !important;
    }
    .animate-shimmer, .rayuela-shimmer {
      animation: none !important;
      background: linear-gradient(90deg, transparent 0%, var(--muted) 50%, transparent 100%) !important;
    }
  }

  /* Efectos de progreso y exploración */
  .rayuela-progress-glow {
    @apply shadow-lg;
  }
  
  .rayuela-exploration-glow {
    @apply shadow-lg;
  }

  /* Estilos específicos para iconografía moderna */
  .rayuela-icon-accent {
    @apply text-primary group-hover:text-primary/80 transition-colors;
  }
  
  .rayuela-icon-progress {
    @apply text-success group-hover:text-success/80 transition-colors;
  }
  
  .rayuela-icon-exploration {
    @apply text-info group-hover:text-info/80 transition-colors;
  }

  /*
  SISTEMA DE COLORES UNIFICADO - RAYUELA
  =====================================
  
  Este sistema utiliza variables CSS basadas en oklch para garantizar:
  - Consistencia entre modo claro y oscuro
  - Armonía entre secciones de marketing y producto  
  - Accesibilidad y contraste óptimo
  
  MEJORES PRÁCTICAS:
  ✅ Usar: bg-background, bg-card, bg-muted
  ❌ Evitar: bg-gray-100, bg-white, bg-blue-50
  
  ✅ Usar: text-foreground, text-muted-foreground  
  ❌ Evitar: text-gray-800, text-blue-600
  
  ✅ Usar: Badge variant="success|warning|info"
  ❌ Evitar: bg-green-100 text-green-800
  
  ✅ Usar: from-marketing-gradient-start to-marketing-gradient-end
  ❌ Evitar: from-blue-50 to-indigo-100
  
  Las variables oklch proporcionan transiciones suaves y 
  control preciso de luminancia para máxima accesibilidad.
  */



}

@layer utilities {
  /* Utilidades adicionales para tipografía */
  .font-feature-small-caps {
    font-feature-settings: "smcp" 1;
  }
  
  .font-feature-tabular {
    font-feature-settings: "tnum" 1;
  }
  
  .font-feature-oldstyle {
    font-feature-settings: "onum" 1;
  }
  
  /* Animaciones mejoradas para skeleton */
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
  
  .animate-shimmer {
    animation: shimmer 2s ease-in-out infinite;
  }

  /* Animaciones de entrada sutiles */
  @keyframes fade-in {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  @keyframes scale-in {
    0% {
      opacity: 0;
      transform: scale(0.95);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes slide-up {
    0% {
      opacity: 0;
      transform: translateY(10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scale-in 0.3s ease-out;
  }

  .animate-slide-up {
    animation: slide-up 0.4s ease-out;
  }

  /* Respect user's motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .animate-fade-in,
    .animate-scale-in,
    .animate-slide-up,
    .rayuela-fade-in,
    .rayuela-scale-in,
    .rayuela-slide-up {
      animation: none;
    }

    .animate-shimmer,
    .rayuela-shimmer {
      animation: none;
      background: linear-gradient(90deg, transparent 0%, var(--muted) 50%, transparent 100%);
    }
  }

  /* Utilidades para el toque creativo de Rayuela */
  .rayuela-shimmer {
    background: linear-gradient(
      110deg,
      transparent 0%,
      transparent 40%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 60%,
      transparent 100%
    );
    background-size: 200% 100%;
    @apply animate-shimmer;
  }

  /* Utilidades para sombras mejoradas */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }
  
  .shadow-medium {
    box-shadow: 0 4px 25px -2px rgba(0, 0, 0, 0.08), 0 10px 30px -2px rgba(0, 0, 0, 0.06);
  }
  
  .shadow-glow {
    box-shadow: 0 0 20px -2px rgba(0, 0, 0, 0.1);
  }

  /* Estados de interacción específicos de Rayuela */
  .rayuela-focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2;
  }




}


/* ==== GRADIENT SECTIONS ==== */
.section-gradient {
  background: linear-gradient(180deg, #fafafa 0%, #ffffff 100%);
}

/* ==== HERO IMAGE ==== */
.hero-img {
  max-width: 540px;
  height: auto;
  object-fit: contain;
}

/* ==== CARDS ==== */
.card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}
.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
}

/* ==== STEP CIRCLES ==== */
.step-circle {
  width: 42px;
  height: 42px;
  background-color: #7c3aed; /* Purple-600 */
  color: white;
  font-weight: bold;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem auto;
  font-size: 1rem;
}

/* ==== BUTTONS ==== */
.btn-primary {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background-color: #7c3aed;
  color: white;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.95rem;
  text-align: center;
  transition: background-color 0.2s ease, transform 0.2s ease;
}
.btn-primary:hover {
  background-color: #6d28d9;
  transform: translateY(-2px);
}

.btn-secondary {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background-color: white;
  color: #374151;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.95rem;
  text-align: center;
  transition: all 0.2s ease;
}
.btn-secondary:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-2px);
}

/* ==== CODE SNIPPET ==== */
pre {
  font-family: 'Fira Code', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* ==== TEXT ==== */
h1, h2, h3 {
  font-family: 'Inter', sans-serif;
}
p, li, button {
  font-family: 'Inter', sans-serif;
}

/* ==== RESPONSIVE ==== */
@media (max-width: 768px) {
  .hero-img {
    max-width: 100%;
  }
}


/* ==== SECCIONES CON GRADIENTE MARCADO ==== */
.section-gradient-strong {
  background: linear-gradient(180deg, #f5f3ff 0%, #ffffff 100%);
  padding: 3rem 0;
}

/* ==== HERO IMAGE ==== */
.hero-img {
  max-width: 540px;
  height: auto;
  object-fit: contain;
}

/* ==== CARDS ==== */
.card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}
.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
}

/* ==== STEP CIRCLES ==== */
.step-circle {
  width: 42px;
  height: 42px;
  background: linear-gradient(135deg, #7c3aed, #9333ea);
  color: white;
  font-weight: bold;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem auto;
  font-size: 1rem;
  box-shadow: 0 4px 10px rgba(124, 58, 237, 0.2);
}

/* ==== BUTTONS ==== */
.btn-primary {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #7c3aed, #9333ea);
  color: white;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.95rem;
  text-align: center;
  transition: background-color 0.2s ease, transform 0.2s ease;
}
.btn-primary:hover {
  background: linear-gradient(135deg, #6d28d9, #7e22ce);
  transform: translateY(-2px);
}

.btn-secondary {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background-color: white;
  color: #374151;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.95rem;
  text-align: center;
  transition: all 0.2s ease;
}
.btn-secondary:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-2px);
}

/* ==== CODE SNIPPET ==== */
pre {
  font-family: 'Fira Code', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* ==== TEXT ==== */
h1, h2, h3 {
  font-family: 'Inter', sans-serif;
}
p, li, button {
  font-family: 'Inter', sans-serif;
}

/* ==== RESPONSIVE ==== */
@media (max-width: 768px) {
  .hero-img {
    max-width: 100%;
  }
}


@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .btn-primary {
    @apply inline-flex items-center justify-center px-5 py-2.5 rounded-lg font-medium text-white
           bg-purple-600 hover:bg-purple-700 transition;
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center px-5 py-2.5 rounded-lg font-medium
           text-gray-700 border border-gray-300 bg-white hover:bg-gray-100 transition;
  }
}

/* === UTILIDADES DE ACCESIBILIDAD === */

/* Screen reader only - oculta visualmente pero mantiene accesible para lectores de pantalla */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Mostrar cuando recibe focus (útil para skip links) */
.sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}