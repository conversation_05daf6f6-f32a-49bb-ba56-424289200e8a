"""
Tests for cache invalidation endpoints with external ID support.

This test suite verifies that cache invalidation works correctly with both
internal and external IDs, ensuring API consistency.
"""

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from unittest.mock import AsyncMock, patch

from src.db.models.end_user import EndUser
from src.db.models.account import Account


class TestCacheInvalidationExternalIds:
    """Test cache invalidation endpoints with external ID support."""

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession, test_account: Account):
        """Create a test user with external_id."""
        user = EndUser(
            account_id=test_account.account_id,
            external_id="cache_test_user_123",
            is_active=True
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @patch("src.core.cache.RecommendationCache.invalidate_user_cache")
    async def test_invalidate_cache_with_internal_user_id(
        self,
        mock_invalidate_cache: AsyncMock,
        async_client: AsyncClient,
        test_user: EndUser,
        test_api_key: str
    ):
        """Test cache invalidation using internal user_id."""
        mock_invalidate_cache.return_value = None

        response = await async_client.post(
            f"/api/v1/recommendations/invalidate-cache/{test_user.user_id}",
            headers={"X-API-Key": test_api_key}
        )

        assert response.status_code == 200
        data = response.json()
        assert f"Cache invalidated for user {test_user.user_id}" in data["message"]
        
        # Verify the cache service was called with correct parameters
        mock_invalidate_cache.assert_called_once_with(
            account_id=test_user.account_id,
            user_id=test_user.user_id
        )

    @patch("src.core.cache.RecommendationCache.invalidate_user_cache")
    async def test_invalidate_cache_with_external_user_id(
        self,
        mock_invalidate_cache: AsyncMock,
        async_client: AsyncClient,
        test_user: EndUser,
        test_api_key: str
    ):
        """Test cache invalidation using external_user_id."""
        mock_invalidate_cache.return_value = None

        response = await async_client.post(
            f"/api/v1/recommendations/invalidate-cache/external/{test_user.external_id}",
            headers={"X-API-Key": test_api_key}
        )

        assert response.status_code == 200
        data = response.json()
        assert f"Cache invalidated for user with external_id '{test_user.external_id}'" in data["message"]
        
        # Verify the cache service was called with correct internal user_id
        mock_invalidate_cache.assert_called_once_with(
            account_id=test_user.account_id,
            user_id=test_user.user_id
        )

    async def test_invalidate_cache_external_user_not_found(
        self,
        async_client: AsyncClient,
        test_api_key: str
    ):
        """Test error handling when external_user_id doesn't exist."""
        response = await async_client.post(
            "/api/v1/recommendations/invalidate-cache/external/nonexistent_user",
            headers={"X-API-Key": test_api_key}
        )

        assert response.status_code == 404
        data = response.json()
        assert "User with external_id 'nonexistent_user' not found" in data["detail"]

    async def test_invalidate_cache_internal_user_not_found(
        self,
        async_client: AsyncClient,
        test_api_key: str
    ):
        """Test error handling when internal user_id doesn't exist."""
        response = await async_client.post(
            "/api/v1/recommendations/invalidate-cache/99999",
            headers={"X-API-Key": test_api_key}
        )

        assert response.status_code == 404

    @patch("src.core.cache.RecommendationCache.invalidate_user_cache")
    async def test_cache_invalidation_consistency(
        self,
        mock_invalidate_cache: AsyncMock,
        async_client: AsyncClient,
        test_user: EndUser,
        test_api_key: str
    ):
        """Test that both endpoints result in the same cache invalidation."""
        mock_invalidate_cache.return_value = None

        # Call internal ID endpoint
        response1 = await async_client.post(
            f"/api/v1/recommendations/invalidate-cache/{test_user.user_id}",
            headers={"X-API-Key": test_api_key}
        )
        assert response1.status_code == 200

        # Call external ID endpoint
        response2 = await async_client.post(
            f"/api/v1/recommendations/invalidate-cache/external/{test_user.external_id}",
            headers={"X-API-Key": test_api_key}
        )
        assert response2.status_code == 200

        # Verify both calls resulted in the same cache invalidation
        assert mock_invalidate_cache.call_count == 2
        call1_args = mock_invalidate_cache.call_args_list[0]
        call2_args = mock_invalidate_cache.call_args_list[1]
        
        # Both calls should have identical arguments
        assert call1_args == call2_args
        assert call1_args.kwargs["account_id"] == test_user.account_id
        assert call1_args.kwargs["user_id"] == test_user.user_id

    async def test_cache_invalidation_tenant_isolation(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        test_api_key: str
    ):
        """Test that cache invalidation respects tenant isolation."""
        # Create users in different accounts with same external_id
        from src.db.models.account import Account
        
        # Create second account
        account2 = Account(
            name="Test Account 2",
            email="<EMAIL>",
            api_key="different_api_key"
        )
        db_session.add(account2)
        await db_session.flush()

        # Create users with same external_id in different accounts
        user1 = EndUser(
            account_id=1,  # First account
            external_id="shared_external_id",
            is_active=True
        )
        user2 = EndUser(
            account_id=account2.account_id,
            external_id="shared_external_id",  # Same external_id
            is_active=True
        )
        db_session.add_all([user1, user2])
        await db_session.commit()

        # Try to invalidate cache for user2 using account1's API key
        response = await async_client.post(
            "/api/v1/recommendations/invalidate-cache/external/shared_external_id",
            headers={"X-API-Key": test_api_key}  # Account 1's API key
        )

        # Should find user1, not user2
        assert response.status_code == 200
