/**
 * Consolidated API client using generated code from OpenAPI spec
 * This file provides a clean interface over the generated API client
 */

// Import generated API functions and types
import {
    getRayuela,
    // Types - these come directly from OpenAPI generation
    RegisterRequest,
    LoginResponse,
    RegisterResponse,
    SystemUserResponse,
    AccountResponse,
    UsageStats,
    SrcApiV1EndpointsPlansPlanInfo,
    ApiKeyResponse,
    NewApiKeyResponse,
    ApiKeyListResponse,
    ApiKeyCreate,
    ApiKeyUpdate,
    UsageSummaryResponse,
} from './generated/rayuelaAPI';

// Import the consolidated ApiError from generated client
import { ApiError } from './generated/api-client';

// Re-export ApiError for backward compatibility
export { ApiError };

// Helper function to handle API errors consistently
function handleApiError(error: unknown, defaultMessage: string): never {
    // If it's already an ApiError from the interceptor, re-throw it
    if (error instanceof ApiError) {
        throw error;
    }

    // Handle Axios errors that weren't caught by interceptor
    if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { status?: number; data?: any }; message?: string };
        const status = axiosError.response?.status || 500;
        const message = axiosError.message || defaultMessage;
        const errorCode = axiosError.response?.data?.error_code || 'UNKNOWN_ERROR';
        const details = axiosError.response?.data?.details || null;

        throw new ApiError(message, status, errorCode, details);
    }

    // Handle generic errors
    if (error instanceof Error) {
        throw new ApiError(error.message, 500, 'GENERIC_ERROR', null);
    }

    // Fallback for unknown error types
    throw new ApiError(defaultMessage, 500, 'UNKNOWN_ERROR', null);
}

// Create a simple API client instance
const api = getRayuela();

// --- Authentication Functions ---

export const loginUser = async (credentials: { email: string; password: string }): Promise<LoginResponse> => {
    try {
        return await api.loginApiV1AuthTokenPost(credentials);
    } catch (error: unknown) {
        handleApiError(error, 'Login failed');
    }
};

export const registerUser = async (
    accountName: string,
    email: string,
    password: string
): Promise<RegisterResponse> => {
    try {
        const registerRequest: RegisterRequest = {
            accountName: accountName,
            email,
            password
        };
        // The backend returns RegisterResponse with proper camelCase fields
        const response = await api.registerApiV1AuthRegisterPost(registerRequest);
        return response;
    } catch (error: unknown) {
        handleApiError(error, 'Registration failed');
    }
};

export const logout = async (): Promise<unknown> => {
    return api.logoutApiV1AuthLogoutPost();
};

export const requestEmailVerification = async (): Promise<unknown> => {
    return api.sendVerificationEmailApiV1AuthSendVerificationEmailPost();
};

export const verifyEmail = async (token: string): Promise<unknown> => {
    return api.verifyEmailApiV1AuthVerifyEmailGet({ token });
};

// --- User Functions ---

export const getCurrentUser = async (): Promise<SystemUserResponse> => {
    try {
        return await api.getCurrentUserInfoApiV1SystemUsersMeGet();
    } catch (error: unknown) {
        handleApiError(error, 'Failed to get current user');
    }
};

// Alias for compatibility
export const getMe = getCurrentUser;

// --- Account Functions ---

export const getCurrentAccount = async (): Promise<AccountResponse> => {
    try {
        // Authentication is handled by the client configuration
        return await api.getAccountInfoApiV1AccountsCurrentGet();
    } catch (error: unknown) {
        handleApiError(error, 'Failed to get current account');
    }
};

// Alias for compatibility
export const getMyAccount = getCurrentAccount;

export const getAccountUsage = async (): Promise<UsageStats> => {
    try {
        return await api.getApiUsageApiV1AccountsUsageGet();
    } catch (error: unknown) {
        handleApiError(error, 'Failed to get account usage');
    }
};

export const getPlans = async (): Promise<Record<string, SrcApiV1EndpointsPlansPlanInfo>> => {
    try {
        return await api.getAvailablePlansApiV1PlansGet();
    } catch (error: unknown) {
        handleApiError(error, 'Failed to get plans');
    }
};

// Alias for compatibility
export const getAvailablePlans = getPlans;

// --- Usage Functions ---

export const getUsageHistory = async (
    startDate?: string,
    endDate?: string
): Promise<unknown[]> => {
    const params: Record<string, string> = {};
    if (startDate) params.start_date = startDate;
    if (endDate) params.end_date = endDate;

    return api.getUsageHistoryApiV1UsageGet(params);
};

export const getUsageSummary = async (): Promise<UsageSummaryResponse> => {
    try {
        return await api.getUsageSummaryApiV1UsageSummaryGet();
    } catch (error: unknown) {
        handleApiError(error, 'Failed to get usage summary');
    }
};

// --- API Key Functions ---

export const getApiKeys = async (): Promise<ApiKeyListResponse> => {
    try {
        return await api.listApiKeysApiV1ApiKeysGet();
    } catch (error: unknown) {
        handleApiError(error, 'Failed to get API keys');
    }
};

// Alias for compatibility
export const listApiKeys = getApiKeys;

export const createApiKey = async (apiKeyData: { name: string; permissions?: string[] }): Promise<NewApiKeyResponse> => {
    try {
        const createRequest: ApiKeyCreate = {
            name: apiKeyData.name
        };
        return await api.createApiKeyApiV1ApiKeysPost(createRequest);
    } catch (error: unknown) {
        handleApiError(error, 'Failed to create API key');
    }
};

export const deleteApiKey = async (keyId: string): Promise<void> => {
    try {
        await api.revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete(Number(keyId));
    } catch (error: unknown) {
        handleApiError(error, 'Failed to delete API key');
    }
};

export const updateApiKey = async (keyId: string, updateData: { name?: string; permissions?: string[] }): Promise<ApiKeyResponse> => {
    try {
        const updateRequest: ApiKeyUpdate = {
            name: updateData.name
        };
        return await api.updateApiKeyApiV1ApiKeysApiKeyIdPut(Number(keyId), updateRequest);
    } catch (error: unknown) {
        handleApiError(error, 'Failed to update API key');
    }
};

export const revokeApiKey = async (apiKeyId: number): Promise<void> => {
    return api.revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete(apiKeyId);
};

export const getApiKey = async (): Promise<ApiKeyResponse> => {
    return api.getCurrentApiKeyApiV1ApiKeysCurrentGet();
};

export const revokeAllApiKeys = async (): Promise<void> => {
    return api.revokeApiKeyApiV1ApiKeysDelete();
};

// --- Billing Functions ---

export const createCheckoutSession = async (priceId: string): Promise<unknown> => {
    const request = { price_id: priceId };
    return api.createCheckoutSessionApiV1BillingCreateCheckoutSessionPost(request);
};

export const createBillingPortalSession = async (): Promise<unknown> => {
    return api.createPortalSessionApiV1BillingCreatePortalSessionPost({});
};

// --- Health Functions ---

export const healthCheck = async (): Promise<{ status: string }> => {
    try {
        return await api.healthCheckHealthGet();
    } catch (error: unknown) {
        handleApiError(error, 'Health check failed');
    }
};

// --- Analytics Functions ---

export const getRecommendationPerformance = async (
    modelId?: number,
    metricType?: string
): Promise<unknown> => {
    try {
        const params: { model_id?: number; metric_type?: string; account_id?: number } = {};
        if (modelId !== undefined) params.model_id = modelId;
        if (metricType !== undefined) params.metric_type = metricType;
        
        return await api.getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet(params);
    } catch (error: unknown) {
        handleApiError(error, 'Failed to get recommendation performance');
    }
};

export const getConfidenceMetrics = async (): Promise<unknown> => {
    try {
        return await api.getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet();
    } catch (error: unknown) {
        handleApiError(error, 'Failed to get confidence metrics');
    }
};

// --- Missing interfaces for proper API response handling ---

// Remove manual interface definitions - they should come from generated types
// export interface LoginResponse { ... }     // DELETE
// export interface RegisterResponse { ... }  // DELETE

// Export more types for compatibility
export type {
    RegisterRequest,
    SystemUserResponse,
    AccountResponse,
    UsageStats,
    SrcApiV1EndpointsPlansPlanInfo as PlanInfo,
    ApiKeyResponse,
    NewApiKeyResponse,
    ApiKeyListResponse,
    ApiKeyCreate,
    ApiKeyUpdate
};

// Re-export types for backward compatibility
export type ApiKey = ApiKeyResponse;
export type AccountInfo = AccountResponse;

