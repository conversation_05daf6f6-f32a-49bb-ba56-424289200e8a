{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/RayuelaLogo.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/RayuelaLogo.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/RayuelaLogo.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/RayuelaLogo.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/RayuelaLogo.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/RayuelaLogo.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiR,GAC9S,+CACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/lib/seo.ts"], "sourcesContent": ["import { Metadata } from 'next'\r\n\r\nconst baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://rayuela.ai'\r\nconst siteName = 'Rayuela.ai'\r\nconst defaultDescription = 'Sistemas de recomendación avanzados para tu negocio, sin la complejidad de construirlos desde cero.'\r\n\r\nexport interface SEOConfig {\r\n  title: string\r\n  description?: string\r\n  path: string\r\n  keywords?: string[]\r\n  type?: 'website' | 'article'\r\n  publishedTime?: string\r\n  modifiedTime?: string\r\n  noIndex?: boolean\r\n}\r\n\r\nexport function generateMetadata({\r\n  title,\r\n  description = defaultDescription,\r\n  path,\r\n  keywords = [],\r\n  type = 'website',\r\n  publishedTime,\r\n  modifiedTime,\r\n  noIndex = false\r\n}: SEOConfig): Metadata {\r\n  const fullTitle = title.includes(siteName) ? title : `${title} | ${siteName}`\r\n  const canonicalUrl = `${baseUrl}${path}`\r\n\r\n  const metadata: Metadata = {\r\n    title: fullTitle,\r\n    description,\r\n    keywords: keywords.join(', '),\r\n    authors: [{ name: 'Rayuela Team' }],\r\n    creator: '<PERSON><PERSON><PERSON>',\r\n    publisher: '<PERSON><PERSON><PERSON>',\r\n    robots: noIndex ? 'noindex, nofollow' : 'index, follow',\r\n    alternates: {\r\n      canonical: canonicalUrl,\r\n    },\r\n    openGraph: {\r\n      title: fullTitle,\r\n      description,\r\n      url: canonicalUrl,\r\n      siteName,\r\n      type,\r\n      locale: 'es_AR',\r\n      images: [\r\n        {\r\n          url: `${baseUrl}/og-image.png`,\r\n          width: 1200,\r\n          height: 630,\r\n          alt: title,\r\n        },\r\n      ],\r\n    },\r\n    twitter: {\r\n      card: 'summary_large_image',\r\n      title: fullTitle,\r\n      description,\r\n      images: [`${baseUrl}/og-image.png`],\r\n      creator: '@rayuela_ai',\r\n    },\r\n  }\r\n\r\n  if (publishedTime) {\r\n    metadata.openGraph = {\r\n      ...metadata.openGraph,\r\n      type: 'article',\r\n      publishedTime,\r\n    }\r\n  }\r\n\r\n  if (modifiedTime) {\r\n    metadata.openGraph = {\r\n      ...metadata.openGraph,\r\n      type: 'article',\r\n      modifiedTime,\r\n    }\r\n  }\r\n\r\n  return metadata\r\n}\r\n\r\nexport function generateJsonLd(type: 'Organization' | 'SoftwareApplication' | 'APIReference', data: any) {\r\n  const baseSchema = {\r\n    '@context': 'https://schema.org',\r\n    '@type': type,\r\n  }\r\n\r\n  switch (type) {\r\n    case 'Organization':\r\n      return {\r\n        ...baseSchema,\r\n        name: 'Rayuela',\r\n        url: baseUrl,\r\n        logo: `${baseUrl}/logo.png`,\r\n        description: defaultDescription,\r\n        foundingDate: '2024',\r\n        industry: 'Software',\r\n        sameAs: [\r\n          'https://twitter.com/rayuela_ai',\r\n          'https://linkedin.com/company/rayuela-ai',\r\n        ],\r\n        ...data,\r\n      }\r\n\r\n    case 'SoftwareApplication':\r\n      return {\r\n        ...baseSchema,\r\n        name: 'Rayuela API',\r\n        applicationCategory: 'DeveloperApplication',\r\n        operatingSystem: 'Any',\r\n        offers: {\r\n          '@type': 'Offer',\r\n          price: '0',\r\n          priceCurrency: 'USD',\r\n          description: 'Free tier available',\r\n        },\r\n        ...data,\r\n      }\r\n\r\n    case 'APIReference':\r\n      return {\r\n        ...baseSchema,\r\n        name: data.name || 'Rayuela API Documentation',\r\n        description: data.description || 'Complete API reference for Rayuela recommendation system',\r\n        url: data.url || `${baseUrl}/docs`,\r\n        programmingLanguage: ['Python', 'JavaScript', 'PHP'],\r\n        ...data,\r\n      }\r\n\r\n    default:\r\n      return baseSchema\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,MAAM,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI;AACpD,MAAM,WAAW;AACjB,MAAM,qBAAqB;AAapB,SAAS,iBAAiB,EAC/B,KAAK,EACL,cAAc,kBAAkB,EAChC,IAAI,EACJ,WAAW,EAAE,EACb,OAAO,SAAS,EAChB,aAAa,EACb,YAAY,EACZ,UAAU,KAAK,EACL;IACV,MAAM,YAAY,MAAM,QAAQ,CAAC,YAAY,QAAQ,GAAG,MAAM,GAAG,EAAE,UAAU;IAC7E,MAAM,eAAe,GAAG,UAAU,MAAM;IAExC,MAAM,WAAqB;QACzB,OAAO;QACP;QACA,UAAU,SAAS,IAAI,CAAC;QACxB,SAAS;YAAC;gBAAE,MAAM;YAAe;SAAE;QACnC,SAAS;QACT,WAAW;QACX,QAAQ,UAAU,sBAAsB;QACxC,YAAY;YACV,WAAW;QACb;QACA,WAAW;YACT,OAAO;YACP;YACA,KAAK;YACL;YACA;YACA,QAAQ;YACR,QAAQ;gBACN;oBACE,KAAK,GAAG,QAAQ,aAAa,CAAC;oBAC9B,OAAO;oBACP,QAAQ;oBACR,KAAK;gBACP;aACD;QACH;QACA,SAAS;YACP,MAAM;YACN,OAAO;YACP;YACA,QAAQ;gBAAC,GAAG,QAAQ,aAAa,CAAC;aAAC;YACnC,SAAS;QACX;IACF;IAEA,IAAI,eAAe;QACjB,SAAS,SAAS,GAAG;YACnB,GAAG,SAAS,SAAS;YACrB,MAAM;YACN;QACF;IACF;IAEA,IAAI,cAAc;QAChB,SAAS,SAAS,GAAG;YACnB,GAAG,SAAS,SAAS;YACrB,MAAM;YACN;QACF;IACF;IAEA,OAAO;AACT;AAEO,SAAS,eAAe,IAA6D,EAAE,IAAS;IACrG,MAAM,aAAa;QACjB,YAAY;QACZ,SAAS;IACX;IAEA,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,GAAG,UAAU;gBACb,MAAM;gBACN,KAAK;gBACL,MAAM,GAAG,QAAQ,SAAS,CAAC;gBAC3B,aAAa;gBACb,cAAc;gBACd,UAAU;gBACV,QAAQ;oBACN;oBACA;iBACD;gBACD,GAAG,IAAI;YACT;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,UAAU;gBACb,MAAM;gBACN,qBAAqB;gBACrB,iBAAiB;gBACjB,QAAQ;oBACN,SAAS;oBACT,OAAO;oBACP,eAAe;oBACf,aAAa;gBACf;gBACA,GAAG,IAAI;YACT;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,UAAU;gBACb,MAAM,KAAK,IAAI,IAAI;gBACnB,aAAa,KAAK,WAAW,IAAI;gBACjC,KAAK,KAAK,GAAG,IAAI,GAAG,QAAQ,KAAK,CAAC;gBAClC,qBAAqB;oBAAC;oBAAU;oBAAc;iBAAM;gBACpD,GAAG,IAAI;YACT;QAEF;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/app/%28public%29/layout.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/button';\nimport <PERSON><PERSON><PERSON><PERSON>ogo from '@/components/ui/RayuelaLogo';\nimport { generateMetadata as generateSEOMetadata } from '@/lib/seo';\n\nexport const metadata = generateSEOMetadata({\n  title: 'Rayuela.ai – Personalización Inteligente para E-commerce',\n  description:\n    'Convierte visitantes en compradores con recomendaciones basadas en IA. Rayuela.ai es la API que potencia a los líderes del e-commerce en LATAM.',\n  path: '/',\n  keywords: [\n    'recomendaciones personalizadas',\n    'inteligencia artificial e-commerce',\n    'API personalización',\n    'machine learning ventas',\n    'Rayuela.ai',\n  ],\n});\n\nexport default function PublicLayout({ children }: { readonly children: React.ReactNode }) {\n  return (\n    <div>\n      <header className=\"sticky top-0 z-50 bg-background/80 backdrop-blur-md border-b border-border/50 shadow-soft\">\n        <nav className=\"container mx-auto flex items-center justify-between py-4 px-4\">\n          <RayuelaLogo size=\"md\" href=\"/\" showText={true} widthPx={28} heightPx={9} textClassName=\"text-[1.05rem] md:text-[1.1rem]\" />\n\n          <div className=\"flex items-center gap-4\">\n            {/* Navigation links */}\n            <div className=\"hidden md:flex items-center gap-6 text-sm\">\n              <Link href=\"/pricing\" className=\"text-muted-foreground hover:text-primary transition-colors font-medium\">\n                Precios\n              </Link>\n              <Link href=\"/docs\" className=\"text-muted-foreground hover:text-primary transition-colors font-medium\">\n                Docs\n              </Link>\n              <Link href=\"/login\" className=\"text-muted-foreground hover:text-primary transition-colors font-medium\">\n                Iniciar sesión\n              </Link>\n            </div>\n\n            {/* CTA Button */}\n            <Button asChild size=\"sm\" className=\"bg-primary hover:bg-primary/90 text-white shadow-glow-primary font-semibold\">\n              <Link href=\"/register?utm_campaign=nav-cta\" aria-label=\"Probar Rayuela gratis\">\n                Probar Gratis\n              </Link>\n            </Button>\n          </div>\n        </nav>\n      </header>\n      <main>{children}</main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;;;;;;AAEO,MAAM,WAAW,CAAA,GAAA,iHAAA,CAAA,mBAAmB,AAAD,EAAE;IAC1C,OAAO;IACP,aACE;IACF,MAAM;IACN,UAAU;QACR;QACA;QACA;QACA;QACA;KACD;AACH;AAEe,SAAS,aAAa,EAAE,QAAQ,EAA0C;IACvF,qBACE,8OAAC;;0BACC,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,uIAAA,CAAA,UAAW;4BAAC,MAAK;4BAAK,MAAK;4BAAI,UAAU;4BAAM,SAAS;4BAAI,UAAU;4BAAG,eAAc;;;;;;sCAExF,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAyE;;;;;;sDAGzG,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;sDAAyE;;;;;;sDAGtG,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAyE;;;;;;;;;;;;8CAMzG,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,MAAK;oCAAK,WAAU;8CAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAiC,cAAW;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvF,8OAAC;0BAAM;;;;;;;;;;;;AAGb", "debugId": null}}]}