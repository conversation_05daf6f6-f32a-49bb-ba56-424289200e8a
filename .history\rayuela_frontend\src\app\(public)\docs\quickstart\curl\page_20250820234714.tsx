import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowLeft, Terminal, CheckCircle } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'Inicio Rápido - cURL',
  description: 'Guía completa para probar la API de Rayuela usando cURL. Ejemplos prácticos para autenticación, ingesta de datos y obtención de recomendaciones.',
  path: '/docs/quickstart/curl',
  keywords: ['curl', 'terminal', 'inicio rápido', 'API', 'recomendaciones', 'testing'],
});

export default function CurlQuickstartPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="mb-8">
          <Button variant="ghost" asChild className="mb-4">
            <Link href="/docs">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver a Documentación
            </Link>
          </Button>
          
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            Inicio Rápido - cURL
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl">
            Prueba la API de Rayuela directamente desde tu terminal usando cURL. Perfecto para testing rápido y prototipado.
          </p>
        </div>

        {/* Prerequisites */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Terminal className="w-5 h-5" />
              Requisitos Previos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-muted-foreground">
              <li>• cURL instalado (viene preinstalado en la mayoría de sistemas)</li>
              <li>• Una cuenta en Rayuela (regístrate en <Link href="/register" className="text-primary hover:underline">rayuela.ai</Link>)</li>
              <li>• Tu API Key (disponible en el dashboard después del registro)</li>
            </ul>
          </CardContent>
        </Card>

        {/* Step 1: Test Auth */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Paso 1: Verificar Autenticación</CardTitle>
            <CardDescription>
              Prueba que tu API Key funciona correctamente.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
              <pre className="text-sm font-mono overflow-x-auto">
{`curl -X GET "https://api.rayuela.ai/api/v1/health/auth" \\
     -H "X-API-Key: sk_prod_tu_api_key_aqui"`}
              </pre>
            </div>
            <div className="mt-4 p-4 bg-success/10 border border-success/20 rounded-lg">
              <p className="text-sm text-success-foreground">
                <strong>Respuesta esperada:</strong> HTTP 200 con información de tu cuenta
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Step 2: Create Product */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Paso 2: Crear un Producto</CardTitle>
            <CardDescription>
              Agrega un producto a tu catálogo para poder generar recomendaciones.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
              <pre className="text-sm font-mono overflow-x-auto">
{`curl -X POST "https://api.rayuela.ai/api/v1/products" \\
     -H "X-API-Key: sk_prod_tu_api_key_aqui" \\
     -H "Content-Type: application/json" \\
     -d '{
       "external_id": "prod-001",
       "name": "Smartphone XYZ",
       "description": "Smartphone de última generación",
       "price": 599.99,
       "category": "electronics",
       "attributes": {
         "color": "black",
         "brand": "XYZ",
         "memory": "128GB"
       }
     }'`}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Step 3: Create User */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Paso 3: Crear un Usuario</CardTitle>
            <CardDescription>
              Registra un usuario para poder generar recomendaciones personalizadas.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
              <pre className="text-sm font-mono overflow-x-auto">
{`curl -X POST "https://api.rayuela.ai/api/v1/end-users" \\
     -H "X-API-Key: sk_prod_tu_api_key_aqui" \\
     -H "Content-Type: application/json" \\
     -d '{
       "external_id": "user-001",
       "preferred_categories": ["electronics"],
       "price_range_min": 100,
       "price_range_max": 1000,
       "attributes": {
         "age": 28,
         "location": "Madrid"
       }
     }'`}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Step 4: Record Interaction */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Paso 4: Registrar una Interacción</CardTitle>
            <CardDescription>
              Registra que el usuario interactuó con un producto (vista, compra, etc.).
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
              <pre className="text-sm font-mono overflow-x-auto">
{`curl -X POST "https://api.rayuela.ai/api/v1/interactions" \\
     -H "X-API-Key: sk_prod_tu_api_key_aqui" \\
     -H "Content-Type: application/json" \\
     -d '{
       "user_external_id": "user-001",
       "product_external_id": "prod-001",
       "interaction_type": "view",
       "value": 1.0
     }'`}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Step 5: Get Recommendations */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Paso 5: Obtener Recomendaciones</CardTitle>
            <CardDescription>
              Solicita recomendaciones personalizadas para un usuario.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
              <pre className="text-sm font-mono overflow-x-auto">
{`curl -X POST "https://api.rayuela.ai/api/v1/recommendations/personalized/query" \\
     -H "X-API-Key: sk_prod_tu_api_key_aqui" \\
     -H "Content-Type: application/json" \\
     -d '{
       "user_external_id": "user-001",
       "limit": 5,
       "filters": {
         "category": "electronics",
         "price_max": 1000
       }
     }'`}
              </pre>
            </div>
            <div className="mt-4 p-4 bg-info/10 border border-info/20 rounded-lg">
              <p className="text-sm text-info-foreground">
                <strong>💡 Tip:</strong> Si no tienes suficientes datos, el sistema usará recomendaciones populares como fallback.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-success" />
              ¡Listo! Siguientes Pasos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                Ya probaste los endpoints básicos con cURL. Ahora puedes:
              </p>
              <div className="grid gap-4 md:grid-cols-2">
                <Button asChild variant="outline">
                  <Link href="/docs/api/recommendations">
                    Ver API Completa
                  </Link>
                </Button>
                <Button asChild variant="outline">
                  <Link href="/docs/quickstart/python">
                    Integrar con Python
                  </Link>
                </Button>
                <Button asChild variant="outline">
                  <Link href="/docs/quickstart/nodejs">
                    Integrar con Node.js
                  </Link>
                </Button>
                <Button asChild variant="outline">
                  <Link href="/docs/guides/data-ingestion">
                    Guía de Datos
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
