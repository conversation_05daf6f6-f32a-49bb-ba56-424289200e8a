import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Clock } from 'lucide-react';
import { getRayuela } from '@/lib/generated/rayuelaAPI';

type ApiStatusType = 'healthy' | 'unhealthy' | 'checking';

interface ApiStatusProps {
  readonly className?: string;
}

export function ApiStatus({ className }: ApiStatusProps) {
  const [status, setStatus] = useState<ApiStatusType>('checking');
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const checkApiHealth = async () => {
    setStatus('checking');
    
    try {
      // Use generated API client instead of direct fetch
      const api = getRayuela();
      const response = await api.healthCheckHealthGet();
      
      // Check if response indicates healthy status
      if (response.data && typeof response.data === 'object' && 'status' in response.data) {
        const healthData = response.data as { status: string };
        setStatus(healthData.status === 'healthy' ? 'healthy' : 'unhealthy');
      } else {
        // If we get a successful response, assume healthy
        setStatus('healthy');
      }
      
      setLastChecked(new Date());
    } catch (error) {
      console.error('Health check failed:', error);
      setStatus('unhealthy');
      setLastChecked(new Date());
    }
  };

  useEffect(() => {
    checkApiHealth();
    
    // Check every 30 seconds
    const interval = setInterval(checkApiHealth, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusConfig = () => {
    switch (status) {
      case 'healthy':
        return {
          variant: 'default' as const,
          icon: CheckCircle,
          text: 'API Saludable',
          className: 'bg-green-100 text-green-800 border-green-200'
        };
      case 'unhealthy':
        return {
          variant: 'destructive' as const,
          icon: XCircle,
          text: 'API No Disponible',
          className: 'bg-red-100 text-red-800 border-red-200'
        };
      case 'checking':
        return {
          variant: 'secondary' as const,
          icon: Clock,
          text: 'Verificando...',
          className: 'bg-yellow-100 text-yellow-800 border-yellow-200'
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <div className={`flex items-center gap-2 ${className || ''}`}>
      <Badge variant={config.variant} className={config.className}>
        <Icon className="h-3 w-3 mr-1" />
        {config.text}
      </Badge>
      {lastChecked && (
        <span className="text-xs text-muted-foreground">
          Última verificación: {lastChecked.toLocaleTimeString()}
        </span>
      )}
    </div>
  );
}
