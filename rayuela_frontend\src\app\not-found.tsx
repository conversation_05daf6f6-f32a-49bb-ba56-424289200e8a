import Link from 'next/link';
import { Button } from '@/components/ui/button';

export default function NotFoundPage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-white via-sky-50 to-sky-100 dark:from-gray-950 dark:to-gray-900 p-4 text-center">
      <h1 className="text-6xl font-extrabold text-foreground mb-4">404</h1>
      <p className="text-xl text-foreground mb-8">
        Ups, no encontramos esto <span role="img" aria-label="sad">😔</span>
      </p>
      <p className="text-lg text-muted-foreground mb-6 max-w-lg">
        Regresa al inicio o abre la documentación para seguir explorando Rayuela.
      </p>
      <div className="flex flex-col sm:flex-row gap-4">
        <Button asChild size="lg" className="px-8 py-3">
          <Link href="/">Volver a la home</Link>
        </Button>
        <Button variant="outline" asChild size="lg" className="px-8 py-3">
          <Link href="/docs">Ver documentación</Link>
        </Button>
      </div>
    </div>
  );
} 