"use client";

import React, { useState } from 'react';
import { AlertCircle, Mail } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/lib/auth';
import { toast } from 'sonner';

interface EmailVerificationBannerProps {
  onClose?: () => void;
}

/**
 * Banner persistente que recuerda al usuario verificar su email para activar la API Key.
 * Visible sólo para cuentas FREE con email no verificado (lógica externa).
 */
export default function EmailVerificationBanner({ onClose }: EmailVerificationBannerProps) {
  const { requestNewVerificationEmail } = useAuth();
  const [sending, setSending] = useState(false);
  const [sent, setSent] = useState(false);

  const handleResend = async () => {
    setSending(true);
    try {
      const ok = await requestNewVerificationEmail();
      if (ok) {
        setSent(true);
      }
    } catch {
      toast.error('No se pudo reenviar el email de verificación. Intenta de nuevo más tarde.');
    } finally {
      setSending(false);
    }
  };

  return (
    <div className="bg-blue-100 border border-blue-300 text-blue-800 px-4 py-3 rounded mb-4 flex items-start gap-4">
      <AlertCircle className="w-5 h-5 mt-1" />
      <div className="flex-1 text-sm">
        <p className="font-medium">Tu email aún no ha sido verificado.</p>
        <p className="mt-1">Verifica tu correo electrónico para activar tu API Key y comenzar a usar la API.</p>
        {sent && <p className="mt-1 text-green-700">¡Email de verificación reenviado! Revisa tu bandeja de entrada.</p>}
      </div>
      <div className="flex flex-col gap-2 items-end">
        <Button size="sm" variant="outline" onClick={handleResend} disabled={sending || sent}>
          <Mail className="w-4 h-4 mr-1" /> {sent ? 'Enviado' : sending ? 'Enviando…' : 'Reenviar email'}
        </Button>
        {onClose && (
          <Button size="icon" variant="ghost" onClick={onClose} aria-label="Cerrar aviso">
            ×
          </Button>
        )}
      </div>
    </div>
  );
}
