import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { SemanticIcon } from "@/components/ui/icon";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import {
  PhoneIcon,
  MailIcon,
  CheckIcon,
  ArrowLeftIcon,
  BuildingIcon,
  UsersIcon,
  ShieldCheckIcon,
  ClockIcon
} from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'Contactar Ventas - Plan Enterprise',
  description: 'Contacta con nuestro equipo de ventas para obtener una solución Enterprise personalizada con límites ilimitados, soporte dedicado 24/7 y SLA garantizado.',
  path: '/contact-sales',
  keywords: ['enterprise', 'ventas', 'contacto', 'soporte dedicado', 'SLA', 'personalizado'],
});

export default function ContactSalesPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end">
      <div className="container mx-auto px-4 py-16">
        {/* Back to Pricing */}
        <div className="mb-8">
          <Button variant="ghost" asChild>
            <Link href="/pricing" className="flex items-center">
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Volver a Precios
            </Link>
          </Button>
        </div>

        {/* Hero Section */}
        <div className="text-center mb-16">
          <Badge variant="secondary" className="mb-4">
            Plan Enterprise
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Arquitectura Enterprise Pensada para Escalar
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Diseñamos cada implementación de Rayuela sobre Google Cloud para ofrecer rendimiento, seguridad y flexibilidad a nivel enterprise. Configuramos límites, soporte y SLA según tus objetivos de crecimiento.
          </p>
        </div>

        {/* Tech Highlights */}
        <div className="mt-24 mb-16 max-w-5xl mx-auto">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white text-center mb-10">
            Tecnología que impulsa resultados
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[{
              icon: <SemanticIcon icon={ShieldCheckIcon} size="xl" context="primary" />,
              title: 'Infraestructura Google Cloud',
              text: 'Escalamos automáticamente y cumplimos con estándares de seguridad de nivel empresarial.'
            }, {
              icon: <SemanticIcon icon={ClockIcon} size="xl" context="primary" />,
              title: 'Entrenamiento Continuo',
              text: 'Modelos actualizados cada 6 horas para mantener la relevancia del catálogo.'
            }, {
              icon: <SemanticIcon icon={UsersIcon} size="xl" context="success" />,
              title: 'SDKs para tu Stack',
              text: 'Librerías oficiales para Node, Python y PHP aceleran la integración.'
            }].map((item, idx) => (
              <div key={idx} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow flex flex-col items-center text-center">
                {item.icon}
                <h3 className="font-semibold text-lg mt-4 mb-2">{item.title}</h3>
                <p className="text-muted-foreground text-sm">{item.text}</p>
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {/* Contact Information */}
          <Card className="h-fit">
            <CardHeader>
              <CardTitle accent className="flex items-center">
                <PhoneIcon className="h-5 w-5 mr-2 text-blue-600" />
                Contacta con Ventas
              </CardTitle>
              <CardDescription>
                Nuestro equipo de ventas está listo para ayudarte a encontrar la solución perfecta
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Contact Methods */}
              <div className="space-y-4">
                <div className="flex items-center space-x-3 p-4 bg-info-light rounded-lg">
                  <MailIcon className="h-5 w-5 text-blue-600" />
                  <div>
                    <div className="font-medium">Email</div>
                    <div className="text-sm text-muted-foreground"><EMAIL></div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <PhoneIcon className="h-5 w-5 text-green-600" />
                  <div>
                    <div className="font-medium">Teléfono</div>
                    <div className="text-sm text-muted-foreground">+54 11 1234-5678</div>
                  </div>
                </div>
              </div>

              {/* Response Time */}
              <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <ClockIcon className="h-4 w-4 text-gray-600" />
                  <span className="font-medium">Tiempo de Respuesta</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Nuestro equipo responde en menos de 24 horas durante días hábiles.
                </p>
              </div>

              {/* CTA Buttons */}
              <div className="space-y-3">
                <Button className="w-full" size="lg" asChild>
                  <a href="mailto:<EMAIL>?subject=Consulta Plan Enterprise">
                    <MailIcon className="mr-2 h-4 w-4" />
                    Enviar Email
                  </a>
                </Button>
                
                <Button variant="outline" className="w-full" size="lg" asChild>
                  <a href="tel:+541112345678">
                    <PhoneIcon className="mr-2 h-4 w-4" />
                    Llamar Ahora
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Enterprise Features */}
          <Card>
            <CardHeader>
              <CardTitle accent className="flex items-center">
                <BuildingIcon className="h-5 w-5 mr-2 text-purple-600" />
                ¿Qué Incluye Enterprise?
              </CardTitle>
              <CardDescription>
                Características exclusivas para grandes organizaciones
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Features List */}
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium">Límites Personalizados</div>
                    <div className="text-sm text-muted-foreground">
                      API calls, almacenamiento y usuarios según tus necesidades
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium">Soporte Dedicado 24/7</div>
                    <div className="text-sm text-muted-foreground">
                      Equipo de soporte exclusivo disponible las 24 horas
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium">SLA Garantizado</div>
                    <div className="text-sm text-muted-foreground">
                      99.9% de uptime garantizado con compensaciones
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium">Implementación Personalizada</div>
                    <div className="text-sm text-muted-foreground">
                      Asistencia completa en la integración y configuración
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium">Modelos Personalizados</div>
                    <div className="text-sm text-muted-foreground">
                      Desarrollo de modelos específicos para tu industria
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium">Entrenamiento Continuo</div>
                    <div className="text-sm text-muted-foreground">
                      Reentrenamiento automático cada 6 horas
                    </div>
                  </div>
                </div>
              </div>

              {/* Enterprise Stats */}
              <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                <div className="text-center p-3 bg-info-light rounded-lg">
                  <UsersIcon className="h-6 w-6 text-blue-600 mx-auto mb-1" />
                  <div className="text-sm font-medium">Usuarios</div>
                  <div className="text-xs text-muted-foreground">Ilimitados</div>
                </div>
                
                <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <ShieldCheckIcon className="h-6 w-6 text-purple-600 mx-auto mb-1" />
                  <div className="text-sm font-medium">Uptime</div>
                  <div className="text-xs text-muted-foreground">99.9% SLA</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Additional Information */}
        <div className="mt-16 text-center">
          <div className="max-w-2xl mx-auto">
            <h3 className="text-xl font-semibold mb-4">¿Tienes Preguntas?</h3>
            <p className="text-muted-foreground mb-6">
              Nuestro equipo de ventas puede ayudarte a entender cómo Rayuela puede 
              adaptarse a las necesidades específicas de tu organización.
            </p>
            <Button variant="outline" asChild>
              <Link href="/pricing">
                Ver Otros Planes
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
