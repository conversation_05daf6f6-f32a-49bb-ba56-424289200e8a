from fastapi import APIRouter, Depends, Query, Path, Request, Body, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any, List, Union
import json
from src.db import schemas
from src.db.schemas.public_recommendation import (
    PublicRecommendationQueryRequest,
    PublicRecommendationQueryExternalRequest,
    convert_public_to_internal_request,
    convert_public_external_to_internal_request
)
from src.db.session import get_db
from src.core.deps import get_current_account, get_limit_service
from src.services import LimitService
from src.utils.pagination import get_pagination_params
from src.db.models.product import Product
from src.ml_pipeline.serving_engine import ServingEngine
from src.ml_pipeline.post_processing_service import PostProcessingService
from src.ml_pipeline.explanation_generator import ExplanationGenerator
from src.ml_pipeline.fallback_handler import Fallback<PERSON><PERSON><PERSON>
from src.ml_pipeline.catalog_insights_service import CatalogInsightsService
from src.ml_pipeline.model_artifact_manager import ModelArtifactManager
from src.utils.base_logger import logger, log_error, log_info
from src.utils.security import verify_resource_ownership
from src.db.models.end_user import EndUser
from src.core.cache import RecommendationCache
from src.utils.filter_utils import apply_structured_filters, parse_simple_filter_string
from src.core.exceptions import (
    RecommendationException,
    ResourceNotFoundError,
    ModelNotTrainedError,
)
from sqlalchemy import select
from src.services.recommendation_service import RecommendationService
from src.db.schemas.analytics import RecommendationPerformanceResponse
from src.api.v1.endpoints.analytics import get_recommendation_performance as _get_recommendation_performance


router = APIRouter()
# Inicializar los servicios de ML
artifact_manager = ModelArtifactManager()
serving_engine = ServingEngine(artifact_manager)
post_processing = PostProcessingService()
explanation_generator = ExplanationGenerator()
fallback_handler = FallbackHandler()
catalog_insights = CatalogInsightsService()
recommendation_cache = RecommendationCache()

# NUEVO: Servicio de recomendaciones centralizado con auto-fallback
recommendation_service = RecommendationService(
    serving_engine=serving_engine,
    post_processing=post_processing,
    fallback_handler=fallback_handler,
    cache=recommendation_cache,
)

# Función de dependencia para permitir inyección y facilitar pruebas

def get_recommendation_service() -> RecommendationService:  # pragma: no cover
    return recommendation_service


# El endpoint GET /recommendations/personalized/{end_user_id} ha sido eliminado# Por favor, use el endpoint POST /recommendations/personalized/query en su lugar


# Endpoint: Most Searched Products
@router.get(
    "/most-searched/", response_model=schemas.PaginatedResponse[schemas.Product]
)
async def get_most_searched(
    timeframe: str = Query("week", enum=["day", "week", "month"]),
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    pagination: tuple = Depends(get_pagination_params),
):
    skip, limit = pagination
    result = await catalog_insights.get_most_searched(
        db, account.account_id, skip, limit, timeframe
    )
    return result


# Endpoint: Trending Searches
@router.get(
    "/trending-searches/", response_model=schemas.PaginatedResponse[schemas.Search]
)
async def get_trending_searches(
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    pagination: tuple = Depends(get_pagination_params),
):
    skip, limit = pagination
    result = await catalog_insights.get_trending_searches(
        db, account.account_id, skip, limit
    )
    return result


# Endpoint: Popular Trends
@router.get(
    "/popular-trends/", response_model=schemas.PaginatedResponse[schemas.Product]
)
async def get_popular_trends(
    model_type: str = Query(
        "standard", description="Model type to use for recommendations"
    ),
    include_explanation: bool = Query(
        False, description="Incluir explicación de la recomendación"
    ),
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    pagination: tuple = Depends(get_pagination_params),
):
    skip, limit = pagination
    # Usar el método del servicio con manejo de caché y paginación
    result = await recommendation_service.get_personalized_recommendations_with_cache(
        db=db,
        account_id=account.account_id,
        user_id=0,  # Para tendencias populares usamos user_id=0 como valor especial
        skip=skip,
        limit=limit,
        handle_cold_start=True,  # Activar manejo de cold start
        model_type=model_type,  # Usar el modelo especificado
    )

    # Si no se solicita explicación, eliminarla de los resultados
    if not include_explanation:
        for item in result.get("items", []):
            if hasattr(item, "explanation"):
                item.explanation = None

    return result


# Endpoint: Related Searches for a Product
@router.get(
    "/related-searches/{product_id}",
    response_model=schemas.PaginatedResponse[schemas.Search],
)
async def get_related_searches(
    product_id: int,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    pagination: tuple = Depends(get_pagination_params),
):
    # Verificar que el producto pertenezca a la cuenta
    await verify_resource_ownership(
        db=db,
        model=Product,
        resource_id=product_id,
        account_id=account.account_id,
        error_class=ResourceNotFoundError,
    )

    skip, limit = pagination
    result = await recommendation_service.get_related_searches(
        db, account.account_id, product_id, skip, limit
    )
    return result


# Endpoint: Most Sold Products
@router.get("/most-sold/", response_model=schemas.PaginatedResponse[schemas.Product])
async def get_most_sold(
    timeframe: str = Query("week", enum=["day", "week", "month"]),
    category: Optional[str] = None,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    pagination: tuple = Depends(get_pagination_params),
):
    skip, limit = pagination
    # Pasar los parámetros de paginación al servicio
    result = await recommendation_service.get_most_sold(
        db, account.account_id, timeframe, category
    )
    # Formatear la respuesta con la paginación
    if isinstance(result, list):
        return {
            "items": result,
            "total": len(result),
            "page": skip // limit + 1 if limit > 0 else 1,
            "size": limit,
        }
    return result


# Endpoint: Top Rated Products
@router.get("/top-rated/", response_model=schemas.PaginatedResponse[schemas.Product])
async def get_top_rated(
    min_ratings: int = Query(10, ge=1),
    category: Optional[str] = None,
    model_type: str = Query(
        "standard", description="Model type to use for recommendations"
    ),
    include_explanation: bool = Query(
        False, description="Incluir explicación de la recomendación"
    ),
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    pagination: tuple = Depends(get_pagination_params),
):
    skip, limit = pagination
    # Usar el método del servicio con manejo de caché y paginación
    result = await recommendation_service.get_personalized_recommendations_with_cache(
        db=db,
        account_id=account.account_id,
        user_id=0,  # Para top rated usamos user_id=0 como valor especial
        skip=skip,
        limit=limit,
        category=category,  # Usar la categoría si se proporciona
        min_rating=min_ratings,  # Usar el rating mínimo proporcionado
        handle_cold_start=True,  # Activar manejo de cold start
        model_type=model_type,  # Usar el modelo especificado
    )

    # Si no se solicita explicación, eliminarla de los resultados
    if not include_explanation:
        for item in result.get("items", []):
            if hasattr(item, "explanation"):
                item.explanation = None

    return result


# Endpoint: Products by Category
@router.get(
    "/category/{category}", response_model=schemas.PaginatedResponse[schemas.Product]
)
async def get_category_products(
    category: str,
    sort_by: str = Query(
        "popularity", enum=["popularity", "rating", "price_asc", "price_desc"]
    ),
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    pagination: tuple = Depends(get_pagination_params),
):
    skip, limit = pagination
    result = await recommendation_service.get_category_products(
        db, account.account_id, category, skip, limit, sort_by
    )
    return result


# Endpoint: Related Categories
@router.get(
    "/related-categories/{category}",
    response_model=schemas.PaginatedResponse[schemas.Category],
)
async def get_related_categories(
    category: str,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    pagination: tuple = Depends(get_pagination_params),
):
    skip, limit = pagination
    result = await recommendation_service.get_related_categories(
        db, account.account_id, category, skip, limit
    )
    return result


# Endpoint: Products Also Bought Together
@router.get(
    "/also-bought/{product_id}",
    response_model=schemas.PaginatedResponse[schemas.Product],
)
async def get_also_bought(
    product_id: int,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    pagination: tuple = Depends(get_pagination_params),
):
    # Verificar que el producto pertenezca a la cuenta
    await verify_resource_ownership(
        db=db,
        model=Product,
        resource_id=product_id,
        account_id=account.account_id,
        error_class=ResourceNotFoundError,
    )

    skip, limit = pagination
    result = await recommendation_service.get_also_bought(
        db, account.account_id, product_id, skip, limit
    )
    return result


# Endpoint: Similar Products based on content
@router.get(
    "/products/{product_id}/similar",
    response_model=schemas.PaginatedResponse[schemas.Product],
)
async def get_similar_products(
    product_id: int,
    limit: int = Query(
        10, ge=1, le=50, description="Número máximo de productos similares a devolver"
    ),
    include_explanation: bool = Query(
        False, description="Incluir explicación de la similitud"
    ),
    explanation_level: schemas.ExplanationLevel = Query(
        schemas.ExplanationLevel.SIMPLE,
        description="Nivel de detalle de la explicación",
    ),
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    pagination: tuple = Depends(get_pagination_params),
):
    """
    Obtiene productos similares al producto especificado basados en contenido.

    Utiliza vectores de características de productos para calcular similitud coseno
    y encontrar productos con características similares.

    Parámetros:
    - product_id: ID del producto para el que se buscan similares
    - limit: Número máximo de productos similares a devolver
    - include_explanation: Si es True, incluye explicaciones de por qué los productos son similares
    - explanation_level: Nivel de detalle de la explicación (simple o detailed)

    Retorna:
    - Lista paginada de productos similares ordenados por similitud
    - Cada producto incluye un score de similitud
    - Si se solicita, incluye explicaciones de la similitud
    """
    # Verificar que el producto pertenezca a la cuenta
    await verify_resource_ownership(
        db=db,
        model=Product,
        resource_id=product_id,
        account_id=account.account_id,
        error_class=ResourceNotFoundError,
    )

    skip, limit = pagination
    result = await recommendation_service.get_similar_products(
        db=db,
        account_id=account.account_id,
        product_id=product_id,
        skip=skip,
        limit=limit,
        include_explanation=include_explanation,
        explanation_level=explanation_level,
    )
    return result


# Endpoint: Invalidate User Recommendations Cache
@router.post("/invalidate-cache/{user_id}")
async def invalidate_user_cache(
    user_id: int = Path(..., gt=0),
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Invalida la caché de recomendaciones para un usuario específico.

    Args:
        user_id: ID del usuario
        account: Cuenta actual
        db: Sesión de base de datos

    Returns:
        Mensaje de confirmación
    """
    # Verificar que el usuario pertenezca a la cuenta
    await verify_resource_ownership(
        db=db,
        model=EndUser,
        resource_id=user_id,
        account_id=account.account_id,
        error_class=ResourceNotFoundError,
    )

    # Invalidar caché específica del usuario
    await recommendation_cache.invalidate_user_cache(
        account_id=account.account_id, user_id=user_id
    )

    return {"message": f"Cache invalidated for user {user_id}"}


# Endpoint: Invalidate User Recommendations Cache using External ID
@router.post("/invalidate-cache/external/{external_user_id}")
async def invalidate_user_cache_external(
    external_user_id: str = Path(..., description="External user ID"),
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Invalida la caché de recomendaciones para un usuario específico usando external_user_id.

    Args:
        external_user_id: ID externo del usuario proporcionado por el cliente
        account: Cuenta actual
        db: Sesión de base de datos

    Returns:
        Mensaje de confirmación
    """
    from src.db.repositories.user import EndUserRepository

    # Resolver el external_user_id al user_id interno
    user_repo = EndUserRepository(db, account.account_id)
    user = await user_repo.get_by_external_id(external_user_id)
    if not user:
        raise HTTPException(
            status_code=404,
            detail=f"User with external_id '{external_user_id}' not found"
        )

    # Invalidar caché específica del usuario usando el user_id interno
    await recommendation_cache.invalidate_user_cache(
        account_id=account.account_id, user_id=user.user_id
    )

    return {"message": f"Cache invalidated for user with external_id '{external_user_id}'"}


# Endpoint: Invalidate Account Recommendations Cache
@router.post("/invalidate-cache")
async def invalidate_account_cache(
    account: schemas.AccountResponse = Depends(get_current_account),
):
    """Invalida toda la caché de recomendaciones para una cuenta."""
    try:
        await recommendation_cache.invalidate_cache(account_id=account.account_id)
        return {"message": f"Cache invalidated for account {account.account_id}"}
    except Exception as e:
        log_error(f"Error invalidating cache: {str(e)}")
        raise RecommendationException(f"Error invalidating cache: {str(e)}")


# Endpoint: Personalized Recommendations with Complex Filters (POST)
@router.post(
    "/personalized/query",
    response_model=schemas.PaginatedResponse[schemas.Product],
    summary="Get personalized recommendations",
    description="Main endpoint for obtaining personalized recommendations with complex filters and structured context.",
)
async def query_personalized_recommendations(
    query: Union[PublicRecommendationQueryRequest, PublicRecommendationQueryExternalRequest],
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    limit_service: LimitService = Depends(get_limit_service),
):
    """
    Get personalized recommendations for a user with complex filtering options.

    This is the main and recommended endpoint for obtaining personalized recommendations.
    It allows you to specify complex structured filters and context in the request body,
    making it easy to build advanced queries that wouldn't be possible with query parameters.

    ## Features

    * **Complex Filters**: Combine multiple conditions with logical operators (AND/OR)
    * **Structured Context**: Provide context information to improve recommendation relevance
    * **Configurable Strategies**: Choose from different recommendation strategies
    * **Pagination and Sorting**: Control the number and order of results
    * **Detailed Explanations**: Understand why items are recommended

    ## Available Recommendation Goals

    * `user_engagement`: Optimize for user engagement and satisfaction
    * `conversion_optimization`: Focus on conversion and purchase likelihood
    * `discovery_mode`: Maximize diversity and help users discover new items
    * `catalog_promotion`: Promote specific catalog items and new arrivals

    ## Model Variants

    * `standard`: Basic recommendation model (included in all plans)
    * `premium`: Advanced model with enhanced personalization
    * `enterprise`: Full-featured model with maximum customization

    ## Request Body Parameters

    * `user_id` (integer, required): ID of the user to get recommendations for
    * `filters` (object, optional): Complex filter structure with nested conditions
    * `context` (object, optional): Contextual information to improve recommendations
    * `recommendation_goal` (string, optional): Business objective for recommendations
    * `model_variant` (string, default: "standard"): Model variant based on your plan
    * `include_explanation` (boolean, default: false): Whether to include explanations
    * `skip` (integer, default: 0): Number of items to skip (for pagination)
    * `limit` (integer, default: 10, max: 100): Maximum number of items to return

    ## Example Request

    ```json
    {
      "user_id": 123,
      "recommendation_goal": "user_engagement",
      "model_variant": "standard",
      "filters": {
        "logic": "and",
        "filters": [
          {
            "field": "price",
            "op": "lt",
            "value": 50
          },
          {
            "logic": "or",
            "filters": [
              {
                "field": "category",
                "op": "eq",
                "value": "electronics"
              },
              {
                "field": "brand",
                "op": "in",
                "value": ["Samsung", "Apple"]
              }
            ]
          }
        ]
      },
      "context": {
        "page_type": "product_detail",
        "device": "mobile",
        "source_item_id": 456
      },
      "include_explanation": true,
      "skip": 0,
      "limit": 10
    }
    ```

    ## Example Response

    ```json
    {
      "items": [
        {
          "product_id": "p123",
          "name": "Smartphone XYZ",
          "category": "electronics",
          "price": 49.99,
          "score": 0.95,
          "explanation": {
            "primary_reason": "similar_items",
            "text_explanation": "Recommended because you viewed similar smartphones"
          }
        },
        ...
      ],
      "total": 42,
      "page": 1,
      "size": 10
    }
    ```
    """
    try:
        # Validar límites de API
        await limit_service.validate_api_call_limit("recommendations")

        # Convert public schema to internal format
        if isinstance(query, PublicRecommendationQueryExternalRequest):
            # Convert public external request to internal format
            internal_data = convert_public_external_to_internal_request(query)
            ext_user_id = internal_data["external_user_id"]

            # Buscar usuario interno
            end_user_row = await db.execute(
                select(EndUser).where(
                    EndUser.external_id == ext_user_id,
                    EndUser.account_id == account.account_id,
                )
            )
            end_user = end_user_row.scalars().first()
            if not end_user:
                raise ResourceNotFoundError("EndUser", ext_user_id)

            # Construir objeto equivalente RecommendationQueryRequest con datos internos
            query_internal = schemas.RecommendationQueryRequest(
                user_id=end_user.user_id,
                filters=internal_data.get("filters"),
                context=internal_data.get("context"),
                strategy=internal_data.get("strategy"),
                model_type=internal_data.get("model_type"),
                include_explanation=internal_data.get("include_explanation"),
                explanation_level=internal_data.get("explanation_level"),
                skip=internal_data.get("skip"),
                limit=internal_data.get("limit"),
            )
        elif isinstance(query, PublicRecommendationQueryRequest):
            # Convert public request to internal format
            internal_data = convert_public_to_internal_request(query)
            query_internal = schemas.RecommendationQueryRequest(
                user_id=internal_data["user_id"],
                filters=internal_data.get("filters"),
                context=internal_data.get("context"),
                strategy=internal_data.get("strategy"),
                model_type=internal_data.get("model_type"),
                include_explanation=internal_data.get("include_explanation"),
                explanation_level=internal_data.get("explanation_level"),
                skip=internal_data.get("skip"),
                limit=internal_data.get("limit"),
            )
        else:
            # Fallback for legacy internal requests
            query_internal = query

        # Verificar que el usuario pertenezca a la cuenta
        await verify_resource_ownership(
            db=db,
            model=EndUser,
            resource_id=query_internal.user_id,
            account_id=account.account_id,
            error_class=ResourceNotFoundError,
        )

        # Usar el método del servicio que maneja la caché y la paginación
        result = await recommendation_service.get_personalized_recommendations_with_cache(
            db=db,
            account_id=account.account_id,
            user_id=query_internal.user_id,
            skip=query_internal.skip,
            limit=query_internal.limit * 2,  # Solicitar más candidatos para filtrar después
            recommendation_type="hybrid",  # Valor por defecto para el nuevo enfoque de estrategias
            handle_cold_start=True,
            model_type=query_internal.model_type,
            strategy=query_internal.strategy,
            context=query_internal.context,
            include_explanation=query_internal.include_explanation,
        )

        # Aplicar filtros estructurados a los resultados
        if query_internal.filters and result.get("items"):
            items = result.get("items", [])
            filtered_items = apply_structured_filters(items, query_internal.filters)
            log_info(
                f"Filtrado estructurado: {len(items)} -> {len(filtered_items)} items"
            )

            # Advertir si no quedan elementos después del filtrado
            if len(filtered_items) == 0:
                log_error(
                    f"No quedan elementos después de aplicar los filtros estructurados"
                )

            # Actualizar los resultados con los items filtrados
            result["items"] = filtered_items[: query_internal.limit]
            result["total"] = len(filtered_items)

        # Aplicar ordenamiento si se especifica
        if query_internal.sort_by and result.get("items"):
            items = result.get("items", [])
            field = query_internal.sort_by.field
            direction = query_internal.sort_by.direction

            # Ordenar los items
            sorted_items = sorted(
                items,
                key=lambda x: x.get(field, ""),
                reverse=(direction == schemas.SortDirection.DESCENDING),
            )
            result["items"] = sorted_items

        # Si no se solicita explicación, eliminarla de los resultados
        if not query_internal.include_explanation:
            for item in result.get("items", []):
                if hasattr(item, "explanation"):
                    item.explanation = None

        return result

    except ModelNotTrainedError as e:
        log_error(f"Model not trained for account {account.account_id}: {str(e)}")
        raise RecommendationException(str(e))
    except Exception as e:
        log_error(f"Error in query_personalized_recommendations: {str(e)}")
        raise RecommendationException(f"Error generating recommendations: {str(e)}")


# Endpoint: Explicación detallada de recomendación
@router.get(
    "/explain/{user_id}/{item_id}",
    response_model=schemas.DetailedExplanation,
)
async def get_recommendation_explanation(
    user_id: int = Path(..., gt=0),
    item_id: int = Path(..., gt=0),
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    limit_service: LimitService = Depends(get_limit_service),
):
    """
    Obtiene una explicación detallada de por qué un producto específico se recomienda a un usuario.

    Esta explicación incluye:
    - Razón principal de la recomendación
    - Razones secundarias
    - Nivel de confianza
    - Evidencia que respalda la recomendación (productos similares, categorías afines, etc.)
    - Explicación en texto plano

    Args:
        user_id: ID del usuario
        item_id: ID del producto
        account: Información de la cuenta autenticada
        db: Sesión de base de datos
        limit_service: Servicio de límites

    Returns:
        Explicación detallada de la recomendación
    """
    try:
        # Validar límites de API
        await limit_service.validate_api_call_limit("recommendations")

        # Verificar que el usuario pertenezca a la cuenta
        await verify_resource_ownership(
            db=db,
            model=EndUser,
            resource_id=user_id,
            account_id=account.account_id,
            error_class=ResourceNotFoundError,
        )

        # Verificar que el producto pertenezca a la cuenta
        await verify_resource_ownership(
            db=db,
            model=Product,
            resource_id=item_id,
            account_id=account.account_id,
            error_class=ResourceNotFoundError,
        )

        # Obtener explicación detallada
        explanation = await recommendation_service.get_item_explanation(
            db=db, account_id=account.account_id, user_id=user_id, item_id=item_id
        )

        if not explanation:
            raise HTTPException(
                status_code=404,
                detail=f"No se encontró explicación para el item {item_id} y usuario {user_id}",
            )

        return explanation

    except ResourceNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        log_error(f"Error obteniendo explicación: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error obteniendo explicación: {str(e)}"
        )


# New endpoint: Explain using external IDs
@router.get(
    "/explain/external/{external_user_id}/{external_item_id}",
    response_model=schemas.DetailedExplanation,
    summary="Get explanation using external IDs",
)
async def get_recommendation_explanation_external(
    external_user_id: str = Path(..., description="External user ID"),
    external_item_id: str = Path(..., description="External product ID"),
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    limit_service: LimitService = Depends(get_limit_service),
):
    """Get explanation using external IDs by resolving to internal IDs."""
    from src.db.repositories.user import EndUserRepository
    from src.db.repositories.product import ProductRepository
    # Resolve internal IDs
    user_repo = EndUserRepository(db, account.account_id)
    user = await user_repo.get_by_external_id(external_user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    product_repo = ProductRepository(db, account.account_id)
    product = await product_repo.get_by_external_id(external_item_id)
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    return await recommendation_service.get_item_explanation(
        db=db,
        account_id=account.account_id,
        user_id=user.user_id,
        item_id=product.product_id,
    )

# ============================================================================
# E-COMMERCE SPECIFIC ENDPOINTS (High-Level Industry Templates)
# ============================================================================

@router.post(
    "/ecommerce/homepage",
    response_model=schemas.PaginatedResponse[schemas.Product],
    summary="E-commerce homepage recommendations",
    description="Optimized recommendations for e-commerce homepage with balanced strategy"
)
async def get_ecommerce_homepage_recommendations(
    request: schemas.RecommendationQueryExternalRequest,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    limit_service: LimitService = Depends(get_limit_service),
):
    """
    E-commerce homepage recommendations with optimized defaults:
    - Strategy: balanced (relevance + diversity)
    - Filters: in_stock=true, active products only
    - Limit: 12 (optimal for homepage grid)
    - Explanations: disabled (performance optimization)
    """
    # Override with e-commerce homepage optimized settings
    optimized_request = request.model_copy(update={
        "strategy": "balanced",
        "limit": min(request.limit or 12, 20),  # Cap at 20 for performance
        "include_explanation": False,  # Homepage doesn't need explanations
        "filters": {
            "logic": "and",
            "filters": [
                {"field": "is_active", "op": "eq", "value": True},
                {"field": "inventory_count", "op": "gt", "value": 0},
                *((request.filters.filters if request.filters else []))
            ]
        }
    })

    return await query_personalized_recommendations_external(
        optimized_request, account, db, limit_service
    )


@router.post(
    "/ecommerce/product-page",
    response_model=schemas.PaginatedResponse[schemas.Product],
    summary="E-commerce product page recommendations",
    description="Cross-sell and related product recommendations for product detail pages"
)
async def get_ecommerce_product_page_recommendations(
    request: schemas.RecommendationQueryExternalRequest,
    current_product_id: str = Query(..., description="External ID of the current product being viewed"),
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    limit_service: LimitService = Depends(get_limit_service),
):
    """
    Product page recommendations optimized for cross-sell:
    - Strategy: maximize_engagement (focus on conversion)
    - Excludes current product
    - Includes explanations for trust
    - Limit: 8 (optimal for product page sidebar)
    """
    # Add current product exclusion and optimize for product page
    optimized_request = request.model_copy(update={
        "strategy": "maximize_engagement",
        "limit": min(request.limit or 8, 12),
        "include_explanation": True,  # Product page benefits from explanations
        "context": {
            **(request.context or {}),
            "page_type": "product_detail",
            "source_external_product_id": current_product_id
        },
        "filters": {
            "logic": "and",
            "filters": [
                {"field": "is_active", "op": "eq", "value": True},
                {"field": "inventory_count", "op": "gt", "value": 0},
                {"field": "external_id", "op": "ne", "value": current_product_id},
                *((request.filters.filters if request.filters else []))
            ]
        }
    })

    return await query_personalized_recommendations_external(
        optimized_request, account, db, limit_service
    )


@router.post(
    "/ecommerce/cart",
    response_model=schemas.PaginatedResponse[schemas.Product],
    summary="E-commerce cart recommendations",
    description="Complementary product recommendations for shopping cart page"
)
async def get_ecommerce_cart_recommendations(
    request: schemas.RecommendationQueryExternalRequest,
    cart_items: List[str] = Query(..., description="External IDs of products currently in cart"),
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    limit_service: LimitService = Depends(get_limit_service),
):
    """
    Cart recommendations for complementary items:
    - Strategy: maximize_engagement (focus on add-to-cart)
    - Excludes cart items
    - Focuses on complementary products
    - Limit: 6 (optimal for cart sidebar)
    """
    optimized_request = request.model_copy(update={
        "strategy": "maximize_engagement",
        "limit": min(request.limit or 6, 10),
        "include_explanation": False,  # Cart page focuses on quick decisions
        "context": {
            **(request.context or {}),
            "page_type": "cart",
            "cart_items": cart_items
        },
        "filters": {
            "logic": "and",
            "filters": [
                {"field": "is_active", "op": "eq", "value": True},
                {"field": "inventory_count", "op": "gt", "value": 0},
                {"field": "external_id", "op": "not_in", "value": cart_items},
                *((request.filters.filters if request.filters else []))
            ]
        }
    })

    return await query_personalized_recommendations_external(
        optimized_request, account, db, limit_service
    )


@router.post(
    "/ecommerce/checkout",
    response_model=schemas.PaginatedResponse[schemas.Product],
    summary="E-commerce checkout recommendations",
    description="Last-chance upsell recommendations for checkout page"
)
async def get_ecommerce_checkout_recommendations(
    request: schemas.RecommendationQueryExternalRequest,
    order_value: float = Query(..., description="Current order total value"),
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    limit_service: LimitService = Depends(get_limit_service),
):
    """
    Checkout upsell recommendations:
    - Strategy: balanced (relevance + quick delivery)
    - Price cap: 30% of order value
    - Focus on quick-add items
    - Limit: 4 (minimal distraction)
    """
    max_price = order_value * 0.3  # Max 30% of order value

    optimized_request = request.model_copy(update={
        "strategy": "balanced",
        "limit": min(request.limit or 4, 6),
        "include_explanation": False,  # Checkout should be fast
        "context": {
            **(request.context or {}),
            "page_type": "checkout",
            "order_value": order_value
        },
        "filters": {
            "logic": "and",
            "filters": [
                {"field": "is_active", "op": "eq", "value": True},
                {"field": "inventory_count", "op": "gt", "value": 0},
                {"field": "price", "op": "lte", "value": max_price},
                *((request.filters.filters if request.filters else []))
            ]
        }
    })

    return await query_personalized_recommendations_external(
        optimized_request, account, db, limit_service
    )


# New endpoint: Similar products using external ID
@router.get(
    "/products/external/{external_product_id}/similar",
    response_model=schemas.PaginatedResponse[schemas.Product],
    summary="Get similar products using external ID",
)
async def get_similar_products_external(
    external_product_id: str,
    limit: int = Query(10, ge=1, le=50),
    include_explanation: bool = Query(False),
    explanation_level: schemas.ExplanationLevel = Query(schemas.ExplanationLevel.SIMPLE),
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    pagination: tuple = Depends(get_pagination_params),
):
    from src.db.repositories.product import ProductRepository
    product_repo = ProductRepository(db, account.account_id)
    product = await product_repo.get_by_external_id(external_product_id)
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    skip, _limit = pagination
    result = await recommendation_service.get_similar_products(
        db=db,
        account_id=account.account_id,
        product_id=product.product_id,
        limit=limit,
        include_explanation=include_explanation,
        explanation_level=explanation_level,
    )
    return result

# New endpoint: Also bought using external product ID
@router.get(
    "/also-bought/external/{external_product_id}",
    response_model=schemas.PaginatedResponse[schemas.Product],
    summary="Get also-bought products using external ID",
)
async def get_also_bought_external(
    external_product_id: str,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    pagination: tuple = Depends(get_pagination_params),
):
    from src.db.repositories.product import ProductRepository
    product_repo = ProductRepository(db, account.account_id)
    product = await product_repo.get_by_external_id(external_product_id)
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    skip, limit = pagination
    result = await recommendation_service.get_also_bought(
        db=db,
        account_id=account.account_id,
        product_id=product.product_id,
        skip=skip,
        limit=limit,
    )
    return result


# Endpoint: Confidence Metrics
@router.get("/confidence-metrics", response_model=Dict[str, Any])
async def get_confidence_metrics(
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    limit_service: LimitService = Depends(get_limit_service),
):
    """
    Obtiene métricas de confianza para las recomendaciones.

    Estas métricas incluyen:
    - Distribución de scores de confianza por tipo de modelo (colaborativo, contenido, híbrido)
    - Confianza promedio por categoría de producto
    - Factores que influyen en la confianza
    - Tendencias de confianza a lo largo del tiempo

    Returns:
        Diccionario con métricas de confianza
    """
    try:
        # Validar límites de API
        await limit_service.validate_api_call_limit("analytics")

        # Obtener métricas de confianza
        metrics = await recommendation_service.get_confidence_metrics(
            db=db, account_id=account.account_id
        )

        return metrics
    except Exception as e:
        log_error(f"Error obteniendo métricas de confianza: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error obteniendo métricas de confianza: {str(e)}"
        )


# Endpoint: Rollback Model Version
@router.post("/rollback/{artifact_version}")
async def rollback_model(
    artifact_version: str,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    try:
        # Usar el método del servicio para hacer rollback
        success = await recommendation_service.rollback_model_version(
            db=db, account_id=account.account_id, artifact_version=artifact_version
        )

        if success:
            return {
                "message": f"Rolled back to version {artifact_version} and cache invalidated"
            }
        else:
            raise RecommendationException("Failed to rollback model")
    except ResourceNotFoundError:
        # Re-lanzar para mantener el mismo comportamiento
        raise ResourceNotFoundError("Model", artifact_version)
    except Exception as e:
        logger.error(f"Error rolling back model: {str(e)}")
        raise RecommendationException(f"Error rolling back model: {str(e)}")

# ============================================================================
# A/B TESTING ENDPOINTS (Automatic Baseline Comparison)
# ============================================================================

@router.post("/ab-test/recommendations")
async def get_ab_test_recommendations(
    request: schemas.RecommendationQueryExternalRequest,
    experiment_id: str = Query(None, description="Experiment ID for A/B testing"),
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Get recommendations with automatic A/B testing.

    If experiment_id is provided, users are automatically assigned to control or treatment groups:
    - Control: Popular/baseline recommendations
    - Treatment: Rayuela personalized recommendations

    If no experiment_id, creates an automatic experiment and returns treatment recommendations.

    This enables automatic comparison of Rayuela vs baseline with zero configuration.
    """
    from src.services.experiment_service import ExperimentService
    from datetime import datetime

    experiment_service = ExperimentService(db)

    # If no experiment specified, create automatic baseline experiment
    if not experiment_id:
        auto_experiment = await experiment_service.create_baseline_experiment(
            account_id=account.account_id,
            name=f"Auto A/B Test - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            description="Automatic baseline comparison experiment",
            traffic_allocation=0.5,
            primary_metric="ctr"
        )
        experiment_id = auto_experiment["experiment_id"]

        # Start the experiment immediately
        await experiment_service.start_experiment(account.account_id, experiment_id)

    # Get recommendations based on experiment assignment
    recommendations, variant = await experiment_service.get_recommendations_for_experiment(
        account_id=account.account_id,
        experiment_id=experiment_id,
        user_external_id=request.external_user_id,
        limit=request.limit or 10,
        context=request.context
    )

    # Format response similar to standard recommendations
    response_items = []
    for rec in recommendations:
        response_items.append(schemas.Product(
            product_id=rec["id"],
            external_id=rec["external_id"],
            name=rec["name"],
            category=rec["category"],
            price=rec["price"],
            score=rec["score"],
            explanation=rec.get("explanation"),
            source=rec["source"]
        ))

    return schemas.PaginatedResponse[schemas.Product](
        items=response_items,
        total=len(response_items),
        page=1,
        per_page=len(response_items),
        pages=1,
        meta={
            "experiment_id": experiment_id,
            "variant": variant,
            "ab_testing": True,
            "source": f"ab_test_{variant}"
        }
    )


@router.post("/ab-test/track")
async def track_ab_test_interaction(
    experiment_id: str,
    user_external_id: str,
    event_type: str,
    product_external_id: str = None,
    event_value: float = None,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Track user interaction in A/B test experiment.

    Event types:
    - click: User clicked on a recommendation
    - conversion: User purchased/converted
    - view: User viewed product details
    """
    from src.services.experiment_service import ExperimentService
    from datetime import datetime

    experiment_service = ExperimentService(db)

    # Get user's variant assignment
    variant = await experiment_service.assign_user_to_experiment(
        account_id=account.account_id,
        experiment_id=experiment_id,
        user_external_id=user_external_id
    )

    # Track the event
    await experiment_service.track_experiment_event(
        account_id=account.account_id,
        experiment_id=experiment_id,
        user_external_id=user_external_id,
        variant=variant,
        event_type=event_type,
        product_external_id=product_external_id,
        event_value=event_value
    )

    return {
        "success": True,
        "experiment_id": experiment_id,
        "variant": variant,
        "event_type": event_type,
        "tracked_at": datetime.now().isoformat()
    }


@router.get("/ab-test/{experiment_id}/results")
async def get_ab_test_results(
    experiment_id: str,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Get A/B test results showing performance comparison.

    Returns:
    - Control group metrics (baseline)
    - Treatment group metrics (Rayuela)
    - Lift calculations (CTR, CVR improvements)
    - Statistical significance
    """
    from src.services.experiment_service import ExperimentService

    experiment_service = ExperimentService(db)

    results = await experiment_service.get_experiment_results(
        account_id=account.account_id,
        experiment_id=experiment_id
    )

    return results


router.add_api_route(
    path="/performance",
    endpoint=_get_recommendation_performance,
    methods=["GET"],
    response_model=RecommendationPerformanceResponse,
    tags=["recommendations"],
)
