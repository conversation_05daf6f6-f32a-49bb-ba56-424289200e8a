{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/app/%28public%29/home/<USER>"], "sourcesContent": ["\"use client\";\n\nimport { useCallback, useMemo, useState } from \"react\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\nimport { motion } from \"framer-motion\";\n\n/**\n * Landing final:\n * - Hero con copy para CTO/PO + CTA (Quickstart/Pricing) e imagen /dashboard-hero.png\n * - Diferenciadores (XAI, LTR, Cold-start, RLS, LATAM-ready) con gradiente\n * - Cómo funciona (3 pasos) con círculos numerados centrados\n * - Snippets dinámicos (Python | Node | cURL) + botón Copiar\n * - Resultados que puedes esperar (KPIs claros, sin duplicación con el hero)\n * - Casos de éxito (3 cards con resultado + descripción breve)\n * - FAQ (objeciones típicas)\n * - CTA final\n */\n\ntype Lang = \"python\" | \"node\" | \"curl\";\n\nexport default function HomePage() {\n  const [language, setLanguage] = useState<Lang>(\"python\");\n  const [copied, setCopied] = useState(false);\n\n  const snippets: Record<Lang, string> = useMemo(\n    () => ({\n      python: `# pip install rayuela-sdk\nfrom rayuela import Rayuela\n\nclient = Rayuela(api_key=\"YOUR_API_KEY\")\nrecs = client.recommend(\"user-123\", limit=10)\nprint(f\"Got {len(recs.items)} recommendations!\")`,\n      node: `// npm i rayuela-sdk\nimport Rayuela from \"rayuela-sdk\";\n\nconst client = new Rayuela({ apiKey: \"YOUR_API_KEY\" });\nconst recs = await client.recommend(\"user-123\", { limit: 10 });\nconsole.log(\\`Got \\${recs.items.length} recommendations!\\`);`,\n      curl: `curl -X POST \"https://api.rayuela.ai/api/v1/recommendations/ecommerce/homepage\" \\\\\n  -H \"X-API-Key: YOUR_API_KEY\" \\\\\n  -H \"Content-Type: application/json\" \\\\\n  -d '{\"external_user_id\":\"user-123\",\"limit\":10}'`,\n    }),\n    []\n  );\n\n  const onCopy = useCallback(async () => {\n    try {\n      await navigator.clipboard.writeText(snippets[language]);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 1600);\n    } catch {\n      // noop\n    }\n  }, [language, snippets]);\n\n  // Animations\n  const fadeUp = {\n    hidden: { opacity: 0, y: 24 },\n    show: { opacity: 1, y: 0 },\n  };\n\n  const springCard = {\n    hidden: { opacity: 0, y: 16 },\n    show: { opacity: 1, y: 0 },\n  };\n\n  return (\n    <main className=\"bg-white\">\n      {/* ===== HERO ===== */}\n      <section className=\"py-14 md:py-16\">\n        <div className=\"max-w-7xl mx-auto px-6 grid md:grid-cols-2 gap-6 md:gap-8 items-center\">\n          {/* Columna de texto */}\n          <motion.div\n            variants={fadeUp}\n            initial=\"hidden\"\n            animate=\"show\"\n            className=\"order-2 md:order-1 mb-8 md:mb-0\"\n          >\n            <h1 className=\"text-4xl md:text-5xl font-bold leading-tight mb-4\">\n              Personalización con IA para E-commerce y Marketplaces\n            </h1>\n            <p className=\"text-lg text-muted-foreground mb-6\">\n              Rayuela es una API-first de recomendaciones: integración en <strong>horas</strong>, impacto en{\" \"}\n              <strong>conversión y AOV</strong>, sin MLOps ni complejidad. Diseñada para CTOs, Product Leaders y devs.\n            </p>\n            <div className=\"flex flex-wrap gap-4\">\n              <Link href=\"/onboarding\" className=\"btn-primary\" aria-label=\"Setup en 10 minutos\">\n                Setup en 10 min\n              </Link>\n              <Link href=\"/api-explorer\" className=\"btn-secondary\" aria-label=\"Probar API Explorer\">\n                API Explorer\n              </Link>\n              <Link href=\"/roi-calculator\" className=\"btn-secondary\" aria-label=\"Calcular ROI\">\n                ROI Calculator\n              </Link>\n            </div>\n            <div className=\"mt-4 flex flex-wrap gap-4\">\n              <Link href=\"/register\" className=\"text-blue-600 hover:text-blue-800 font-medium\" aria-label=\"Crear cuenta gratis\">\n                Crear cuenta gratis →\n              </Link>\n              <Link href=\"/community\" className=\"text-blue-600 hover:text-blue-800 font-medium\" aria-label=\"Ver comunidad\">\n                Comunidad (2,847+ devs) →\n              </Link>\n            </div>\n            <div className=\"mt-2 text-xs text-foreground bg-muted px-4 py-3 rounded-lg inline-flex flex-wrap gap-x-4 gap-y-2\">\n              <span>🚀 Setup en 10 minutos</span>\n              <span>🧪 A/B testing automático</span>\n              <span>📊 +25% CTR promedio</span>\n            </div>\n          </motion.div>\n\n          {/* Columna de imagen */}\n          <motion.div\n            className=\"order-1 md:order-2 flex justify-center\"\n            initial={{ opacity: 0, y: 32 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n          >\n            <Image\n              src=\"/dashboard-hero.png\"\n              alt=\"Rayuela dashboard: métricas y recomendaciones\"\n              width={430}\n              height={420}\n              priority\n              className=\"hero-img\"\n            />\n          </motion.div>\n        </div>\n      </section>\n\n      {/* ===== DIFERENCIADORES ===== */}\n      <section className=\"section-gradient-strong\">\n        <div className=\"max-w-7xl mx-auto px-6\">\n          <motion.h2\n            variants={fadeUp}\n            initial=\"hidden\"\n            whileInView=\"show\"\n            viewport={{ once: true, amount: 0.3 }}\n            className=\"text-3xl font-bold text-center mb-10\"\n          >\n            Diferenciadores clave\n          </motion.h2>\n\n          <div className=\"grid md:grid-cols-3 gap-6\">\n            {[\n              {\n                t: \"IA explicable (XAI)\",\n                d: \"Entiende por qué se recomienda cada ítem. Transparencia para PMs, confianza para tu negocio.\",\n              },\n              {\n                t: \"Optimizado con LTR\",\n                d: \"Learning-to-Rank entrenado para maximizar métricas de negocio (conversión, AOV), no solo precisión.\",\n              },\n              {\n                t: \"Cold-start sin fricción\",\n                d: \"Fallback inteligente y señales iniciales para mostrar recomendaciones relevantes desde el día uno.\",\n              },\n              {\n                t: \"Multi-tenant seguro (RLS)\",\n                d: \"Aislamiento de datos por cuenta a nivel base de datos. Listo para producción.\",\n              },\n              {\n                t: \"LATAM-ready\",\n                d: \"Integración nativa con Mercado Pago y focus en latencia/regulación regional.\",\n              },\n              {\n                t: \"DX impecable\",\n                d: \"OpenAPI/SDKs, ejemplos copy-paste y Quickstart para reducir time-to-value.\",\n              },\n            ].map((f, i) => (\n              <motion.div\n                key={f.t}\n                className=\"card\"\n                variants={springCard}\n                initial=\"hidden\"\n                whileInView=\"show\"\n                viewport={{ once: true, amount: 0.2 }}\n                custom={i}\n                whileHover={{ scale: 1.02 }}\n                transition={{ type: \"spring\", stiffness: 120, damping: 16 }}\n              >\n                <h3 className=\"text-lg font-semibold mb-2\">{f.t}</h3>\n                <p className=\"text-muted-foreground\">{f.d}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* ===== NUEVAS FUNCIONALIDADES ===== */}\n      <section className=\"py-14 md:py-16 bg-gradient-to-br from-blue-50 to-purple-50\">\n        <div className=\"max-w-7xl mx-auto px-6\">\n          <motion.div\n            variants={fadeUp}\n            initial=\"hidden\"\n            whileInView=\"show\"\n            viewport={{ once: true, amount: 0.3 }}\n            className=\"text-center mb-12\"\n          >\n            <h2 className=\"text-3xl font-bold mb-4\">\n              Nuevas funcionalidades para acelerar tu adopción\n            </h2>\n            <p className=\"text-xl text-muted-foreground\">\n              Herramientas diseñadas para reducir el time-to-value de semanas a minutos\n            </p>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {[\n              {\n                icon: \"🚀\",\n                title: \"Onboarding de 10 minutos\",\n                description: \"Setup guiado desde signup hasta primera recomendación\",\n                link: \"/onboarding\",\n                badge: \"Nuevo\",\n                color: \"blue\"\n              },\n              {\n                icon: \"🔧\",\n                title: \"API Explorer Interactivo\",\n                description: \"Prueba endpoints en tiempo real y genera código automáticamente\",\n                link: \"/api-explorer\",\n                badge: null,\n                color: \"purple\"\n              },\n              {\n                icon: \"💰\",\n                title: \"ROI Calculator\",\n                description: \"Calcula impacto de negocio y genera business cases automáticos\",\n                link: \"/roi-calculator\",\n                badge: \"Popular\",\n                color: \"green\"\n              },\n              {\n                icon: \"👥\",\n                title: \"Comunidad de Desarrolladores\",\n                description: \"2,847+ devs compartiendo código, casos de uso y mejores prácticas\",\n                link: \"/community\",\n                badge: null,\n                color: \"orange\"\n              }\n            ].map((feature, i) => (\n              <motion.div\n                key={feature.title}\n                variants={springCard}\n                initial=\"hidden\"\n                whileInView=\"show\"\n                viewport={{ once: true, amount: 0.2 }}\n                custom={i}\n                whileHover={{ scale: 1.02 }}\n                transition={{ type: \"spring\", stiffness: 120, damping: 16 }}\n              >\n                <Link href={feature.link} className=\"block\">\n                  <div className=\"card h-full hover:shadow-lg transition-shadow\">\n                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center mb-4 ${\n                      {\n                        blue: 'bg-blue-100',\n                        purple: 'bg-purple-100',\n                        green: 'bg-green-100',\n                        orange: 'bg-orange-100'\n                      }[feature.color]\n                    }`}>\n                      <span className=\"text-2xl\">{feature.icon}</span>\n                    </div>\n                    <div className=\"flex items-center gap-2 mb-2\">\n                      <h3 className=\"text-lg font-semibold\">{feature.title}</h3>\n                      {feature.badge && (\n                        <span className={`px-2 py-1 text-xs rounded-full ${\n                          feature.badge === 'Nuevo' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'\n                        }`}>\n                          {feature.badge}\n                        </span>\n                      )}\n                    </div>\n                    <p className=\"text-muted-foreground mb-4\">{feature.description}</p>\n                    <div className=\"text-blue-600 font-medium text-sm\">\n                      Explorar →\n                    </div>\n                  </div>\n                </Link>\n              </motion.div>\n            ))}\n          </div>\n\n          <motion.div\n            variants={fadeUp}\n            initial=\"hidden\"\n            whileInView=\"show\"\n            viewport={{ once: true, amount: 0.3 }}\n            className=\"text-center mt-12\"\n          >\n            <div className=\"bg-white rounded-lg p-6 shadow-lg max-w-4xl mx-auto\">\n              <h3 className=\"text-xl font-bold mb-4\">Resultados comprobados</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold text-blue-600 mb-1\">+25%</div>\n                  <div className=\"text-sm text-muted-foreground\">CTR promedio vs baseline</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold text-green-600 mb-1\">10min</div>\n                  <div className=\"text-sm text-muted-foreground\">Time-to-first-value</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold text-purple-600 mb-1\">2,847+</div>\n                  <div className=\"text-sm text-muted-foreground\">Desarrolladores activos</div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* ===== CÓMO FUNCIONA ===== */}\n      <section className=\"section-gradient-strong\">\n        <div className=\"max-w-7xl mx-auto px-6\">\n          <motion.h2\n            variants={fadeUp}\n            initial=\"hidden\"\n            whileInView=\"show\"\n            viewport={{ once: true, amount: 0.3 }}\n            className=\"text-3xl font-bold text-center mb-10\"\n          >\n            Cómo funciona\n          </motion.h2>\n\n          <div className=\"grid md:grid-cols-3 gap-6\">\n            {[\n              {\n                n: \"1\",\n                t: \"Ingesta tus datos\",\n                d: \"Envía productos, usuarios e interacciones por API o lotes. Soporte de IDs externos.\",\n              },\n              {\n                n: \"2\",\n                t: \"Entrena el modelo\",\n                d: \"Entrenamiento automático (híbrido) optimizado para tus KPIs. Sin MLOps.\",\n              },\n              {\n                n: \"3\",\n                t: \"Sirve recomendaciones\",\n                d: \"Obtén recomendaciones personalizadas con latencia baja y explicación opcional (XAI).\",\n              },\n            ].map((s, i) => (\n              <motion.div\n                key={s.t}\n                className=\"card text-center\"\n                variants={springCard}\n                initial=\"hidden\"\n                whileInView=\"show\"\n                viewport={{ once: true, amount: 0.2 }}\n                custom={i}\n                whileHover={{ scale: 1.02 }}\n              >\n                <div className=\"step-circle\">{s.n}</div>\n                <h3 className=\"text-lg font-semibold mb-2\">{s.t}</h3>\n                <p className=\"text-muted-foreground\">{s.d}</p>\n              </motion.div>\n            ))}\n          </div>\n\n          <div className=\"flex justify-center mt-8\">\n            <Link href=\"/docs/quickstart/python\" className=\"btn-primary\" aria-label=\"Ir al Quickstart de Python\">\n              Integrar en 3 pasos\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* ===== CODE SNIPPETS ===== */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-4xl mx-auto px-6\">\n          <motion.h2\n            variants={fadeUp}\n            initial=\"hidden\"\n            whileInView=\"show\"\n            viewport={{ once: true, amount: 0.3 }}\n            className=\"text-3xl font-bold text-center mb-6\"\n          >\n            Tu primera recomendación en minutos\n          </motion.h2>\n\n          <motion.div\n            variants={fadeUp}\n            initial=\"hidden\"\n            whileInView=\"show\"\n            viewport={{ once: true, amount: 0.2 }}\n            className=\"flex flex-wrap items-center justify-center gap-3 mb-5\"\n          >\n            {([\"python\", \"node\", \"curl\"] as Lang[]).map((lang) => (\n              <button\n                key={lang}\n                type=\"button\"\n                onClick={() => setLanguage(lang)}\n                className={`px-4 py-2 rounded transition ${\n                  language === lang ? \"bg-purple-600 text-white\" : \"bg-gray-200 text-foreground\"\n                }`}\n                aria-pressed={language === lang ? \"true\" : \"false\"}\n                aria-label={`Mostrar snippet ${lang}`}\n              >\n                {lang}\n              </button>\n            ))}\n          </motion.div>\n          <div className=\"relative bg-gray-900 text-green-300 rounded-lg p-4 font-mono text-sm overflow-auto\">\n            <button\n              type=\"button\"\n              onClick={onCopy}\n              className=\"absolute top-3 right-3 bg-gray-700 text-white text-xs px-3 py-1 rounded-md hover:bg-gray-600 transition\"\n              aria-label=\"Copiar snippet al portapapeles\"\n            >\n              {copied ? \"Copiado ✓\" : \"Copiar\"}\n            </button>\n\n            <motion.pre\n              key={language}\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.25 }}\n              className=\"bg-gray-900 text-green-400 rounded-lg p-6 text-left overflow-x-auto\"\n            >\n            <code>{snippets[language]}</code>\n            </motion.pre>\n          </div>\n\n          <div className=\"text-center mt-6\">\n            <Link href=\"/docs\" className=\"btn-secondary\" aria-label=\"Ir a la documentación completa\">\n              Ver documentación completa\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* ===== RESULTADOS QUE PUEDES ESPERAR ===== */}\n      <section className=\"section-gradient-strong\">\n        <div className=\"max-w-7xl mx-auto px-6\">\n          <motion.h2\n            variants={fadeUp}\n            initial=\"hidden\"\n            whileInView=\"show\"\n            viewport={{ once: true, amount: 0.3 }}\n            className=\"text-3xl font-bold text-center mb-10\"\n          >\n            Resultados que puedes esperar\n          </motion.h2>\n\n          <div className=\"grid md:grid-cols-3 gap-6\">\n            {[\n              { k: \"+15%\", t: \"Conversión\", d: \"Incremento promedio tras 30-45 días.\" },\n              { k: \"+12%\", t: \"AOV\", d: \"Ticket promedio impulsado por recomendaciones.\" },\n              { k: \"-25%\", t: \"Tiempo de integración\", d: \"vs. construir un RecSys in-house.\" },\n            ].map((m, i) => (\n              <motion.div\n                key={m.t}\n                className=\"card text-center\"\n                variants={springCard}\n                initial=\"hidden\"\n                whileInView=\"show\"\n                viewport={{ once: true, amount: 0.2 }}\n                custom={i}\n              >\n                <div className=\"text-3xl font-extrabold mb-2\">{m.k}</div>\n                <div className=\"font-semibold mb-1\">{m.t}</div>\n                <p className=\"text-muted-foreground\">{m.d}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* ===== OPORTUNIDADES DE NEGOCIO ===== */}\n      <section className=\"section-gradient-strong\">\n        <div className=\"max-w-7xl mx-auto px-6\">\n          <motion.div\n            variants={fadeUp}\n            initial=\"hidden\"\n            whileInView=\"show\"\n            viewport={{ once: true, amount: 0.3 }}\n            className=\"text-center mb-10\"\n          >\n            <h2 className=\"text-3xl font-bold mb-4\">\n              Oportunidades de negocio por industria\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-3xl mx-auto\">\n              Descubre el potencial de las recomendaciones inteligentes en tu sector.{\" \"}\n              <strong className=\"text-purple-600\">Haz clic para explorar cada oportunidad.</strong>\n            </p>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {[\n              {\n                title: \"Marketplaces\",\n                industry: \"Plataformas de Matching\",\n                opportunity: \"Hasta +40% GMV\",\n                desc: \"Conecta oferta y demanda de forma inteligente. Reduce tiempo de búsqueda y aumenta matches exitosos.\",\n                href: \"/use-cases/marketplaces\",\n                potential: \"Matching semántico, discovery automático\"\n              },\n              {\n                title: \"E-commerce\",\n                industry: \"Tiendas Online\",\n                opportunity: \"Hasta +30% AOV\",\n                desc: \"Personaliza la experiencia de compra. Aumenta ticket promedio y repeat purchases con cross-selling inteligente.\",\n                href: \"/use-cases/ecommerce\",\n                potential: \"Recomendaciones contextuales, upselling\"\n              },\n              {\n                title: \"SaaS B2B\",\n                industry: \"Software Empresarial\",\n                opportunity: \"MVP con IA en 1 semana\",\n                desc: \"Agrega inteligencia artificial sin equipo de ML. Diferénciate de la competencia con features avanzadas.\",\n                href: \"/use-cases/saas-b2b\",\n                potential: \"Ahorra $280k vs equipo interno\"\n              },\n              {\n                title: \"Contenido Digital\",\n                industry: \"Media & Entretenimiento\",\n                opportunity: \"Hasta +50% engagement\",\n                desc: \"Retén usuarios con feeds ultra-personalizados. Compite con TikTok y YouTube en engagement.\",\n                href: \"/use-cases/content-digital\",\n                potential: \"Algoritmos de retención avanzados\"\n              },\n            ].map((c, i) => (\n              <Link key={c.title} href={c.href} className=\"block group\">\n                <motion.div\n                  className=\"card cursor-pointer transition-all duration-300 hover:shadow-xl hover:border-purple-200 group-hover:bg-gradient-to-br group-hover:from-white group-hover:to-purple-50\"\n                  variants={springCard}\n                  initial=\"hidden\"\n                  whileInView=\"show\"\n                  viewport={{ once: true, amount: 0.2 }}\n                  custom={i}\n                  whileHover={{ scale: 1.02 }}\n                >\n                  <div className=\"flex items-center gap-3 mb-3\">\n                    <div>\n                      <div className=\"text-sm font-medium text-purple-600 group-hover:text-purple-700\">\n                        {c.industry}\n                      </div>\n                      <div className=\"text-lg font-bold text-foreground group-hover:text-purple-900\">\n                        {c.title}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-2xl font-bold text-green-600 mb-2 group-hover:text-green-700\">\n                    {c.opportunity}\n                  </div>\n                  <p className=\"text-muted-foreground mb-3 group-hover:text-foreground leading-relaxed\">\n                    {c.desc}\n                  </p>\n                  <div className=\"text-xs text-muted-foreground mb-3 group-hover:text-muted-foreground\">\n                    {c.potential}\n                  </div>\n                  <div className=\"flex items-center text-purple-600 group-hover:text-purple-700 text-sm font-medium\">\n                    Explorar oportunidad\n                    <svg className=\"w-4 h-4 ml-1 transition-transform group-hover:translate-x-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  </div>\n                </motion.div>\n              </Link>\n            ))}\n          </div>\n\n          <div className=\"flex justify-center mt-8\">\n            <Link href=\"/register?utm_campaign=success-stories\" className=\"btn-primary\" aria-label=\"Crear cuenta\">\n              Únete como early adopter\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* ===== FAQ ===== */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-5xl mx-auto px-6\">\n          <motion.h2\n            variants={fadeUp}\n            initial=\"hidden\"\n            whileInView=\"show\"\n            viewport={{ once: true, amount: 0.3 }}\n            className=\"text-3xl font-bold text-center mb-10\"\n          >\n            Preguntas frecuentes\n          </motion.h2>\n\n          <div className=\"grid md:grid-cols-2 gap-6\">\n            {[\n              {\n                q: \"¿Necesito un equipo de ML para usar Rayuela?\",\n                a: \"No. Rayuela abstrae el MLOps y ofrece defaults sólidos (híbrido + LTR). Si tienes Data Scientists/ML Engineers, pueden ajustar estrategias y explicar salidas con XAI.\",\n              },\n              {\n                q: \"¿Cómo se maneja el cold-start?\",\n                a: \"Usamos fallback inteligente (best-sellers, similitudes y señales iniciales). Aseguramos relevancia desde el primer día.\",\n              },\n              {\n                q: \"¿Qué tan rápido puedo integrar?\",\n                a: \"Con el Quickstart, puedes servir recomendaciones en horas. Nuestra DX (SDKs, OpenAPI y ejemplos) reduce drásticamente el time-to-value.\",\n              },\n              {\n                q: \"¿Es seguro para datos sensibles?\",\n                a: \"Sí. Arquitectura multi-tenant con RLS y aislamiento por cuenta. Logs y auditoría a nivel de API.\",\n              },\n            ].map((f, i) => (\n              <motion.div\n                key={f.q}\n                className=\"card\"\n                variants={springCard}\n                initial=\"hidden\"\n                whileInView=\"show\"\n                viewport={{ once: true, amount: 0.2 }}\n                custom={i}\n              >\n                <h3 className=\"font-semibold mb-2\">{f.q}</h3>\n                <p className=\"text-muted-foreground\">{f.a}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* ===== CTA FINAL ===== */}\n      <section className=\"section-gradient-strong\">\n        <div className=\"max-w-4xl mx-auto px-6 text-center\">\n          <motion.h2\n            variants={fadeUp}\n            initial=\"hidden\"\n            whileInView=\"show\"\n            viewport={{ once: true, amount: 0.3 }}\n            className=\"text-3xl font-bold mb-4\"\n          >\n            Empieza gratis y llega a tu primera recomendación hoy\n          </motion.h2>\n          <p className=\"text-muted-foreground mb-6\">\n            Crea tu cuenta, genera tu API Key y sigue el Quickstart. Si necesitas ayuda, te acompañamos en la integración.\n          </p>\n          <div className=\"flex justify-center gap-4\">\n            <Link href=\"/register\" className=\"btn-primary\" aria-label=\"Crear cuenta\">\n              Crear cuenta\n            </Link>\n            <Link href=\"/docs/quickstart/python\" className=\"btn-secondary\" aria-label=\"Ir al Quickstart\">\n              Ver Quickstart\n            </Link>\n          </div>\n        </div>\n      </section>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAqBe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAQ;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,WAAiC,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sCAC3C,IAAM,CAAC;gBACL,QAAQ,CAAC;;;;;gDAKiC,CAAC;gBAC3C,MAAM,CAAC;;;;;4DAK+C,CAAC;gBACvD,MAAM,CAAC;;;iDAGoC,CAAC;YAC9C,CAAC;qCACD,EAAE;IAGJ,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wCAAE;YACzB,IAAI;gBACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS;gBACtD,UAAU;gBACV;oDAAW,IAAM,UAAU;mDAAQ;YACrC,EAAE,OAAM;YACN,OAAO;YACT;QACF;uCAAG;QAAC;QAAU;KAAS;IAEvB,aAAa;IACb,MAAM,SAAS;QACb,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG;QAAE;IAC3B;IAEA,MAAM,aAAa;QACjB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG;QAAE;IAC3B;IAEA,qBACE,6LAAC;QAAK,WAAU;;0BAEd,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAE,WAAU;;wCAAqC;sDACY,6LAAC;sDAAO;;;;;;wCAAc;wCAAa;sDAC/F,6LAAC;sDAAO;;;;;;wCAAyB;;;;;;;8CAEnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAc,WAAU;4CAAc,cAAW;sDAAsB;;;;;;sDAGlF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAgB,WAAU;4CAAgB,cAAW;sDAAsB;;;;;;sDAGtF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAkB,WAAU;4CAAgB,cAAW;sDAAe;;;;;;;;;;;;8CAInF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAY,WAAU;4CAAgD,cAAW;sDAAsB;;;;;;sDAGlH,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;4CAAgD,cAAW;sDAAgB;;;;;;;;;;;;8CAI/G,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;sDAAK;;;;;;sDACN,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,QAAQ;gCACR,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAOlB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,WAAU;sCACX;;;;;;sCAID,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,GAAG;oCACH,GAAG;gCACL;gCACA;oCACE,GAAG;oCACH,GAAG;gCACL;gCACA;oCACE,GAAG;oCACH,GAAG;gCACL;gCACA;oCACE,GAAG;oCACH,GAAG;gCACL;gCACA;oCACE,GAAG;oCACH,GAAG;gCACL;gCACA;oCACE,GAAG;oCACH,GAAG;gCACL;6BACD,CAAC,GAAG,CAAC,CAAC,GAAG,kBACR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,UAAU;oCACV,SAAQ;oCACR,aAAY;oCACZ,UAAU;wCAAE,MAAM;wCAAM,QAAQ;oCAAI;oCACpC,QAAQ;oCACR,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,YAAY;wCAAE,MAAM;wCAAU,WAAW;wCAAK,SAAS;oCAAG;;sDAE1D,6LAAC;4CAAG,WAAU;sDAA8B,EAAE,CAAC;;;;;;sDAC/C,6LAAC;4CAAE,WAAU;sDAAyB,EAAE,CAAC;;;;;;;mCAXpC,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;0BAmBlB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAA0B;;;;;;8CAGxC,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAK/C,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,MAAM;oCACN,OAAO;oCACP,aAAa;oCACb,MAAM;oCACN,OAAO;oCACP,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,OAAO;oCACP,aAAa;oCACb,MAAM;oCACN,OAAO;oCACP,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,OAAO;oCACP,aAAa;oCACb,MAAM;oCACN,OAAO;oCACP,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,OAAO;oCACP,aAAa;oCACb,MAAM;oCACN,OAAO;oCACP,OAAO;gCACT;6BACD,CAAC,GAAG,CAAC,CAAC,SAAS,kBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,SAAQ;oCACR,aAAY;oCACZ,UAAU;wCAAE,MAAM;wCAAM,QAAQ;oCAAI;oCACpC,QAAQ;oCACR,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,YAAY;wCAAE,MAAM;wCAAU,WAAW;wCAAK,SAAS;oCAAG;8CAE1D,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,QAAQ,IAAI;wCAAE,WAAU;kDAClC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAC,2DAA2D,EAC1E;wDACE,MAAM;wDACN,QAAQ;wDACR,OAAO;wDACP,QAAQ;oDACV,CAAC,CAAC,QAAQ,KAAK,CAAC,EAChB;8DACA,cAAA,6LAAC;wDAAK,WAAU;kEAAY,QAAQ,IAAI;;;;;;;;;;;8DAE1C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAyB,QAAQ,KAAK;;;;;;wDACnD,QAAQ,KAAK,kBACZ,6LAAC;4DAAK,WAAW,CAAC,+BAA+B,EAC/C,QAAQ,KAAK,KAAK,UAAU,8BAA8B,+BAC1D;sEACC,QAAQ,KAAK;;;;;;;;;;;;8DAIpB,6LAAC;oDAAE,WAAU;8DAA8B,QAAQ,WAAW;;;;;;8DAC9D,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;;;;;;;;;;;;mCAhClD,QAAQ,KAAK;;;;;;;;;;sCAyCxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyB;;;;;;kDACvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAwC;;;;;;kEACvD,6LAAC;wDAAI,WAAU;kEAAgC;;;;;;;;;;;;0DAEjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAyC;;;;;;kEACxD,6LAAC;wDAAI,WAAU;kEAAgC;;;;;;;;;;;;0DAEjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAA0C;;;;;;kEACzD,6LAAC;wDAAI,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3D,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,WAAU;sCACX;;;;;;sCAID,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,GAAG;oCACH,GAAG;oCACH,GAAG;gCACL;gCACA;oCACE,GAAG;oCACH,GAAG;oCACH,GAAG;gCACL;gCACA;oCACE,GAAG;oCACH,GAAG;oCACH,GAAG;gCACL;6BACD,CAAC,GAAG,CAAC,CAAC,GAAG,kBACR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,UAAU;oCACV,SAAQ;oCACR,aAAY;oCACZ,UAAU;wCAAE,MAAM;wCAAM,QAAQ;oCAAI;oCACpC,QAAQ;oCACR,YAAY;wCAAE,OAAO;oCAAK;;sDAE1B,6LAAC;4CAAI,WAAU;sDAAe,EAAE,CAAC;;;;;;sDACjC,6LAAC;4CAAG,WAAU;sDAA8B,EAAE,CAAC;;;;;;sDAC/C,6LAAC;4CAAE,WAAU;sDAAyB,EAAE,CAAC;;;;;;;mCAXpC,EAAE,CAAC;;;;;;;;;;sCAgBd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAA0B,WAAU;gCAAc,cAAW;0CAA6B;;;;;;;;;;;;;;;;;;;;;;0BAQ3G,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,WAAU;sCACX;;;;;;sCAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,WAAU;sCAET,AAAC;gCAAC;gCAAU;gCAAQ;6BAAO,CAAY,GAAG,CAAC,CAAC,qBAC3C,6LAAC;oCAEC,MAAK;oCACL,SAAS,IAAM,YAAY;oCAC3B,WAAW,CAAC,6BAA6B,EACvC,aAAa,OAAO,6BAA6B,+BACjD;oCACF,gBAAc,aAAa,OAAO,SAAS;oCAC3C,cAAY,CAAC,gBAAgB,EAAE,MAAM;8CAEpC;mCATI;;;;;;;;;;sCAaX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEV,SAAS,cAAc;;;;;;8CAG1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,UAAU;oCAAK;oCAC7B,WAAU;8CAEZ,cAAA,6LAAC;kDAAM,QAAQ,CAAC,SAAS;;;;;;mCANlB;;;;;;;;;;;sCAUT,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAQ,WAAU;gCAAgB,cAAW;0CAAiC;;;;;;;;;;;;;;;;;;;;;;0BAQ/F,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,WAAU;sCACX;;;;;;sCAID,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,GAAG;oCAAQ,GAAG;oCAAc,GAAG;gCAAuC;gCACxE;oCAAE,GAAG;oCAAQ,GAAG;oCAAO,GAAG;gCAAiD;gCAC3E;oCAAE,GAAG;oCAAQ,GAAG;oCAAyB,GAAG;gCAAoC;6BACjF,CAAC,GAAG,CAAC,CAAC,GAAG,kBACR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,UAAU;oCACV,SAAQ;oCACR,aAAY;oCACZ,UAAU;wCAAE,MAAM;wCAAM,QAAQ;oCAAI;oCACpC,QAAQ;;sDAER,6LAAC;4CAAI,WAAU;sDAAgC,EAAE,CAAC;;;;;;sDAClD,6LAAC;4CAAI,WAAU;sDAAsB,EAAE,CAAC;;;;;;sDACxC,6LAAC;4CAAE,WAAU;sDAAyB,EAAE,CAAC;;;;;;;mCAVpC,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;0BAkBlB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAA0B;;;;;;8CAGxC,6LAAC;oCAAE,WAAU;;wCAAkD;wCACW;sDACxE,6LAAC;4CAAO,WAAU;sDAAkB;;;;;;;;;;;;;;;;;;sCAIxC,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,OAAO;oCACP,UAAU;oCACV,aAAa;oCACb,MAAM;oCACN,MAAM;oCACN,WAAW;gCACb;gCACA;oCACE,OAAO;oCACP,UAAU;oCACV,aAAa;oCACb,MAAM;oCACN,MAAM;oCACN,WAAW;gCACb;gCACA;oCACE,OAAO;oCACP,UAAU;oCACV,aAAa;oCACb,MAAM;oCACN,MAAM;oCACN,WAAW;gCACb;gCACA;oCACE,OAAO;oCACP,UAAU;oCACV,aAAa;oCACb,MAAM;oCACN,MAAM;oCACN,WAAW;gCACb;6BACD,CAAC,GAAG,CAAC,CAAC,GAAG,kBACR,6LAAC,+JAAA,CAAA,UAAI;oCAAe,MAAM,EAAE,IAAI;oCAAE,WAAU;8CAC1C,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,UAAU;wCACV,SAAQ;wCACR,aAAY;wCACZ,UAAU;4CAAE,MAAM;4CAAM,QAAQ;wCAAI;wCACpC,QAAQ;wCACR,YAAY;4CAAE,OAAO;wCAAK;;0DAE1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEACZ,EAAE,QAAQ;;;;;;sEAEb,6LAAC;4DAAI,WAAU;sEACZ,EAAE,KAAK;;;;;;;;;;;;;;;;;0DAId,6LAAC;gDAAI,WAAU;0DACZ,EAAE,WAAW;;;;;;0DAEhB,6LAAC;gDAAE,WAAU;0DACV,EAAE,IAAI;;;;;;0DAET,6LAAC;gDAAI,WAAU;0DACZ,EAAE,SAAS;;;;;;0DAEd,6LAAC;gDAAI,WAAU;;oDAAoF;kEAEjG,6LAAC;wDAAI,WAAU;wDAA8D,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACrH,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;mCAhClE,EAAE,KAAK;;;;;;;;;;sCAwCtB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAyC,WAAU;gCAAc,cAAW;0CAAe;;;;;;;;;;;;;;;;;;;;;;0BAQ5G,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,WAAU;sCACX;;;;;;sCAID,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,GAAG;oCACH,GAAG;gCACL;gCACA;oCACE,GAAG;oCACH,GAAG;gCACL;gCACA;oCACE,GAAG;oCACH,GAAG;gCACL;gCACA;oCACE,GAAG;oCACH,GAAG;gCACL;6BACD,CAAC,GAAG,CAAC,CAAC,GAAG,kBACR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,UAAU;oCACV,SAAQ;oCACR,aAAY;oCACZ,UAAU;wCAAE,MAAM;wCAAM,QAAQ;oCAAI;oCACpC,QAAQ;;sDAER,6LAAC;4CAAG,WAAU;sDAAsB,EAAE,CAAC;;;;;;sDACvC,6LAAC;4CAAE,WAAU;sDAAyB,EAAE,CAAC;;;;;;;mCATpC,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;0BAiBlB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;oCAAc,cAAW;8CAAe;;;;;;8CAGzE,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAA0B,WAAU;oCAAgB,cAAW;8CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzG;GApnBwB;KAAA", "debugId": null}}]}