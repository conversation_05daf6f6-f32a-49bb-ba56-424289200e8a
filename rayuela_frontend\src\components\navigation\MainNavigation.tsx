"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Rocket, 
  Calculator, 
  Users, 
  Code, 
  BarChart3,
  Zap,
  BookOpen,
  Target,
  Menu,
  X
} from "lucide-react";
import { useState } from "react";

const navigationItems = [
  {
    name: "Get Started",
    href: "/onboarding",
    icon: Rocket,
    description: "10-minute setup",
    badge: "New"
  },
  {
    name: "API Explorer",
    href: "/api-explorer",
    icon: Code,
    description: "Test in real-time",
    badge: null
  },
  {
    name: "ROI Calculator",
    href: "/roi-calculator",
    icon: Calculator,
    description: "Business case generator",
    badge: "Popular"
  },
  {
    name: "Community",
    href: "/community",
    icon: Users,
    description: "2,847 developers",
    badge: null
  },
  {
    name: "Analytics",
    href: "/analytics",
    icon: BarChart3,
    description: "Performance metrics",
    badge: null
  }
];

export default function MainNavigation() {
  const pathname = usePathname();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden lg:block bg-white border-b border-border sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <Link href="/" className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Zap className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-foreground">Rayuela</span>
            </Link>

            {/* Navigation Items */}
            <div className="flex items-center space-x-1">
              {navigationItems.map((item) => {
                const isActive = pathname === item.href;
                const Icon = item.icon;
                
                return (
                  <Link key={item.name} href={item.href}>
                    <div className={`
                      flex items-center gap-2 px-4 py-2 rounded-lg transition-colors
                      ${isActive 
                        ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                        : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                      }
                    `}>
                      <Icon className="h-4 w-4" />
                      <span className="font-medium">{item.name}</span>
                      {item.badge && (
                        <Badge 
                          variant={item.badge === 'New' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {item.badge}
                        </Badge>
                      )}
                    </div>
                  </Link>
                );
              })}
            </div>

            {/* CTA Buttons */}
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm">
                <BookOpen className="h-4 w-4 mr-2" />
                Docs
              </Button>
              <Button size="sm">
                <Target className="h-4 w-4 mr-2" />
                Get API Key
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation */}
      <nav className="lg:hidden bg-white border-b border-border sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <Link href="/" className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Zap className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-foreground">Rayuela</span>
            </Link>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>

          {/* Mobile Menu */}
          {mobileMenuOpen && (
            <div className="border-t border-border py-4">
              <div className="space-y-2">
                {navigationItems.map((item) => {
                  const isActive = pathname === item.href;
                  const Icon = item.icon;
                  
                  return (
                    <Link 
                      key={item.name} 
                      href={item.href}
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <div className={`
                        flex items-center gap-3 px-4 py-3 rounded-lg transition-colors
                        ${isActive 
                          ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                          : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                        }
                      `}>
                        <Icon className="h-5 w-5" />
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{item.name}</span>
                            {item.badge && (
                              <Badge 
                                variant={item.badge === 'New' ? 'default' : 'secondary'}
                                className="text-xs"
                              >
                                {item.badge}
                              </Badge>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">{item.description}</div>
                        </div>
                      </div>
                    </Link>
                  );
                })}
              </div>
              
              <div className="mt-4 pt-4 border-t border-border space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <BookOpen className="h-4 w-4 mr-2" />
                  Documentation
                </Button>
                <Button className="w-full justify-start">
                  <Target className="h-4 w-4 mr-2" />
                  Get API Key
                </Button>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Quick Access Floating Menu */}
      <div className="fixed bottom-6 right-6 z-40">
        <div className="flex flex-col gap-3">
          {/* Quick Start Button */}
          <Link href="/onboarding">
            <Button 
              size="lg" 
              className="rounded-full shadow-lg hover:shadow-xl transition-shadow"
            >
              <Rocket className="h-5 w-5 mr-2" />
              Quick Start
            </Button>
          </Link>
          
          {/* ROI Calculator Quick Access */}
          <Link href="/roi-calculator">
            <Button 
              variant="outline" 
              size="sm" 
              className="rounded-full shadow-md hover:shadow-lg transition-shadow bg-white"
            >
              <Calculator className="h-4 w-4 mr-2" />
              ROI Calculator
            </Button>
          </Link>
        </div>
      </div>

      {/* Progress Indicator for Onboarding */}
      {pathname === '/onboarding' && (
        <div className="fixed top-16 left-0 right-0 h-1 bg-gray-200 z-30">
          <div className="h-full bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-300" style={{ width: '60%' }} />
        </div>
      )}
    </>
  );
}
