{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, elevation = \"soft\", ...props }: React.ComponentProps<\"div\"> & { elevation?: \"none\" | \"sm\" | \"soft\" | \"medium\" | \"glow\" }) {\n  const shadowMap: Record<string, string> = {\n    none: \"shadow-none\",\n    sm: \"shadow-sm\",\n    soft: \"shadow-soft\",\n    medium: \"shadow-medium\",\n    glow: \"shadow-glow\",\n  }\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-lg border\",\n        shadowMap[elevation] ?? \"shadow-soft\",\n        \"rayuela-card-gradient rayuela-card-hover\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, accent = false, ...props }: React.ComponentProps<\"div\"> & { accent?: boolean }) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"text-subheading\", accent ? \"rayuela-accent\" : \"text-foreground\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-caption\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,YAAY,MAAM,EAAE,GAAG,OAAiG;IACjJ,MAAM,YAAoC;QACxC,MAAM;QACN,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM;IACR;IACA,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA,SAAS,CAAC,UAAU,IAAI,eACxB,4CACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,SAAS,KAAK,EAAE,GAAG,OAA2D;IAC5G,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB,SAAS,mBAAmB,mBAAmB;QAC/E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC7B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/form.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n    Controller,\n    ControllerProps,\n    FieldPath,\n    FieldValues,\n    FormProvider,\n    useFormContext,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n    TFieldValues extends FieldValues = FieldValues,\n    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n> = {\n    name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n    {} as FormFieldContextValue\n)\n\nconst FormField = <\n    TFieldValues extends FieldValues = FieldValues,\n    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n>({\n    ...props\n}: ControllerProps<TFieldValues, TName>) => {\n    return (\n        <FormFieldContext.Provider value={{ name: props.name }}>\n            <Controller {...props} />\n        </FormFieldContext.Provider>\n    )\n}\n\nconst useFormField = () => {\n    const fieldContext = React.useContext(FormFieldContext)\n    const itemContext = React.useContext(FormItemContext)\n    const { getFieldState, formState } = useFormContext()\n\n    const fieldState = getFieldState(fieldContext.name, formState)\n\n    if (!fieldContext) {\n        throw new Error(\"useFormField should be used within <FormField>\")\n    }\n\n    const { id } = itemContext\n\n    return {\n        id,\n        name: fieldContext.name,\n        formItemId: `${id}-form-item`,\n        formDescriptionId: `${id}-form-item-description`,\n        formMessageId: `${id}-form-item-message`,\n        ...fieldState,\n    }\n}\n\ntype FormItemContextValue = {\n    id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n    {} as FormItemContextValue\n)\n\nconst FormItem = React.forwardRef<\n    HTMLDivElement,\n    React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => {\n    const id = React.useId()\n\n    return (\n        <FormItemContext.Provider value={{ id }}>\n            <div ref={ref} className={cn(\"space-y-2\", className)} {...props} />\n        </FormItemContext.Provider>\n    )\n})\nFormItem.displayName = \"FormItem\"\n\nconst FormLabel = React.forwardRef<\n    React.ElementRef<typeof LabelPrimitive.Root>,\n    React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\n>(({ className, ...props }, ref) => {\n    const { error, formItemId } = useFormField()\n\n    return (\n        <Label\n            ref={ref}\n            className={cn(error && \"text-destructive\", className)}\n            htmlFor={formItemId}\n            {...props}\n        />\n    )\n})\nFormLabel.displayName = \"FormLabel\"\n\nconst FormControl = React.forwardRef<\n    React.ElementRef<typeof Slot>,\n    React.ComponentPropsWithoutRef<typeof Slot>\n>(({ ...props }, ref) => {\n    const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n    return (\n        <Slot\n            ref={ref}\n            id={formItemId}\n            aria-describedby={\n                !error\n                    ? `${formDescriptionId}`\n                    : `${formDescriptionId} ${formMessageId}`\n            }\n            aria-invalid={!!error}\n            {...props}\n        />\n    )\n})\nFormControl.displayName = \"FormControl\"\n\nconst FormDescription = React.forwardRef<\n    HTMLParagraphElement,\n    React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => {\n    const { formDescriptionId } = useFormField()\n\n    return (\n        <p\n            ref={ref}\n            id={formDescriptionId}\n            className={cn(\"text-sm text-muted-foreground\", className)}\n            {...props}\n        />\n    )\n})\nFormDescription.displayName = \"FormDescription\"\n\nconst FormMessage = React.forwardRef<\n    HTMLParagraphElement,\n    React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => {\n    const { error, formMessageId } = useFormField()\n    const body = error ? String(error?.message) : children\n\n    if (!body) {\n        return null\n    }\n\n    return (\n        <p\n            ref={ref}\n            id={formMessageId}\n            className={cn(\"text-sm font-medium text-destructive\", className)}\n            {...props}\n        >\n            {body}\n        </p>\n    )\n})\nFormMessage.displayName = \"FormMessage\"\n\nexport {\n    useFormField,\n    Form,\n    FormItem,\n    FormLabel,\n    FormControl,\n    FormDescription,\n    FormMessage,\n    FormField,\n} \n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;AACA;AASA;AACA;;;;;;;AAEA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACvC,CAAC;AAGL,MAAM,YAAY,CAGhB,EACE,GAAG,OACgC;IACnC,qBACI,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACjD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAGjC;AAEA,MAAM,eAAe;IACjB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACf,MAAM,IAAI,MAAM;IACpB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACH;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACjB;AACJ;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACtC,CAAC;AAGL,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACI,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBAClC,cAAA,8OAAC;YAAI,KAAK;YAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAa,GAAG,KAAK;;;;;;;;;;;AAG3E;AACA,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACI,8OAAC,iIAAA,CAAA,QAAK;QACF,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,SAAS,oBAAoB;QAC3C,SAAS;QACR,GAAG,KAAK;;;;;;AAGrB;AACA,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,GAAG,OAAO,EAAE;IACb,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACI,8OAAC,gKAAA,CAAA,OAAI;QACD,KAAK;QACL,IAAI;QACJ,oBACI,CAAC,QACK,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAEjD,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGrB;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACI,8OAAC;QACG,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGrB;AACA,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW;IAE9C,IAAI,CAAC,MAAM;QACP,OAAO;IACX;IAEA,qBACI,8OAAC;QACG,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;kBAER;;;;;;AAGb;AACA,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-lg border bg-transparent px-3 py-1 text-base shadow-soft rayuela-interactive rayuela-focus-ring outline-none hover:border-ring/50 file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,meACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 transition-all hover:shadow-sm [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground border-border\",\n        destructive:\n          \"border-destructive/50 text-destructive bg-destructive/5 hover:bg-destructive/10 [&>svg]:text-destructive\",\n        success:\n          \"border-success/50 text-success bg-success-light hover:bg-success/10 [&>svg]:text-success\",\n        warning:\n          \"border-warning/50 text-warning bg-warning-light hover:bg-warning/10 [&>svg]:text-warning\",\n        info:\n          \"border-info/50 text-info bg-info-light hover:bg-info/10 [&>svg]:text-info\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,4LACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,SACE;YACF,MACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 384, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/auth/LoginForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport Link from \"next/link\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport * as z from \"zod\";\nimport { toast } from \"sonner\";\n\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\nimport { useAuth } from \"@/lib/auth\";\nimport { MailIcon, AlertCircleIcon } from 'lucide-react';\n\n// Esquema de validación\nconst loginSchema = z.object({\n  email: z.string().email({ message: \"Email inválido\" }),\n  password: z.string().min(8, { message: \"Contraseña debe tener al menos 8 caracteres\" }),\n});\n\ntype LoginFormValues = z.infer<typeof loginSchema>;\n\ninterface LoginFormProps {\n  showHeader?: boolean;\n  onSuccess?: () => void;\n  initialEmail?: string;\n  initialPassword?: string;\n}\n\nexport default function LoginForm({ \n  showHeader = true, \n  onSuccess,\n  initialEmail = \"\",\n  initialPassword = \"\"\n}: LoginFormProps) {\n  const { login, emailVerificationError, requestNewVerificationEmail } = useAuth();\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [isResendingEmail, setIsResendingEmail] = useState(false);\n\n  // Inicializar el formulario\n  const form = useForm<LoginFormValues>({\n    resolver: zodResolver(loginSchema),\n    defaultValues: {\n      email: initialEmail,\n      password: initialPassword,\n    },\n  });\n\n  // Actualizar los valores del formulario si cambian las props iniciales o emailVerificationError\n  useEffect(() => {\n    if (emailVerificationError) {\n      form.setValue('email', emailVerificationError.email);\n      form.setValue('password', emailVerificationError.password);\n    } else if (initialEmail || initialPassword) {\n      form.setValue('email', initialEmail);\n      form.setValue('password', initialPassword);\n    }\n  }, [emailVerificationError, initialEmail, initialPassword, form]);\n\n  // Función para manejar el reenvío del email de verificación\n  const handleResendVerificationEmail = async () => {\n    setIsResendingEmail(true);\n    try {\n      const success = await requestNewVerificationEmail();\n      if (success) {\n        toast.success(\"Email de verificación enviado. Por favor, revisa tu bandeja de entrada.\");\n      }\n    } catch (error: unknown) {\n      console.error(\"Error al reenviar email de verificación:\", error);\n      toast.error((error as Error).message || \"Error al reenviar email de verificación.\");\n    } finally {\n      setIsResendingEmail(false);\n    }\n  };\n\n  // Manejar envío del formulario\n  const onSubmit = async (values: LoginFormValues) => {\n    try {\n      setIsLoading(true);\n      const success = await login(values.email, values.password);\n\n      if (success) {\n        if (onSuccess) {\n          onSuccess();\n        } else {\n          // Redirigir al dashboard\n          router.push(\"/dashboard\");\n          toast.success(\"¡Inicio de sesión exitoso!\");\n        }\n      }\n    } catch (error: unknown) {\n      console.error(\"Login failed:\", error);\n      toast.error((error as Error).message || \"Error al iniciar sesión. Verifica tus credenciales.\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {showHeader && (\n        <div className=\"space-y-2 text-center\">\n          <h1 className=\"text-3xl font-bold\">Iniciar Sesión</h1>\n          <p className=\"text-muted-foreground\">\n            Ingresa tus credenciales para acceder a tu cuenta\n          </p>\n        </div>\n      )}\n\n      {/* Alerta de verificación de email */}\n      {emailVerificationError && (\n        <Alert variant=\"warning\" className=\"mb-6\">\n          <AlertCircleIcon className=\"h-4 w-4\" />\n          <AlertTitle>Verificación de email requerida</AlertTitle>\n          <AlertDescription>\n            {emailVerificationError.message}\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              className=\"mt-2 border-amber-300 text-amber-700 hover:bg-amber-100 hover:text-amber-800\"\n              onClick={handleResendVerificationEmail}\n              disabled={isResendingEmail}\n            >\n              <MailIcon className=\"mr-2 h-4 w-4\" />\n              {isResendingEmail ? 'Enviando...' : 'Reenviar email de verificación'}\n            </Button>\n          </AlertDescription>\n        </Alert>\n      )}\n\n      <Form {...form}>\n        <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\n          <FormField\n            control={form.control}\n            name=\"email\"\n            render={({ field }) => (\n              <FormItem>\n                <FormLabel>Email</FormLabel>\n                <FormControl>\n                  <Input\n                    placeholder=\"<EMAIL>\"\n                    type=\"email\"\n                    autoComplete=\"email\"\n                    disabled={isLoading}\n                    {...field}\n                  />\n                </FormControl>\n                <FormMessage />\n              </FormItem>\n            )}\n          />\n          <FormField\n            control={form.control}\n            name=\"password\"\n            render={({ field }) => (\n              <FormItem>\n                <FormLabel>Contraseña</FormLabel>\n                <FormControl>\n                  <Input\n                    placeholder=\"••••••••\"\n                    type=\"password\"\n                    autoComplete=\"current-password\"\n                    disabled={isLoading}\n                    {...field}\n                  />\n                </FormControl>\n                <FormMessage />\n              </FormItem>\n            )}\n          />\n\n          <Button type=\"submit\" className=\"w-full\" disabled={isLoading || isResendingEmail}>\n            {isLoading ? \"Iniciando sesión...\" : \"Iniciar Sesión\"}\n          </Button>\n        </form>\n      </Form>\n\n      {showHeader && (\n        <div className=\"text-center text-sm\">\n          ¿No tienes una cuenta?{\" \"}\n          <Link\n            href=\"/register\"\n            className=\"underline underline-offset-4 hover:text-primary\"\n          >\n            Registrarse\n          </Link>\n        </div>\n      )}\n    </div>\n  );\n} \n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAQA;AACA;AACA;AACA;AAAA;AAtBA;;;;;;;;;;;;;;;AAwBA,wBAAwB;AACxB,MAAM,cAAc,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IAC3B,OAAO,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,CAAC;QAAE,SAAS;IAAiB;IACpD,UAAU,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAAE,SAAS;IAA8C;AACvF;AAWe,SAAS,UAAU,EAChC,aAAa,IAAI,EACjB,SAAS,EACT,eAAe,EAAE,EACjB,kBAAkB,EAAE,EACL;IACf,MAAM,EAAE,KAAK,EAAE,sBAAsB,EAAE,2BAA2B,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,UAAO,AAAD;IAC7E,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,4BAA4B;IAC5B,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAmB;QACpC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO;YACP,UAAU;QACZ;IACF;IAEA,gGAAgG;IAChG,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,wBAAwB;YAC1B,KAAK,QAAQ,CAAC,SAAS,uBAAuB,KAAK;YACnD,KAAK,QAAQ,CAAC,YAAY,uBAAuB,QAAQ;QAC3D,OAAO,IAAI,gBAAgB,iBAAiB;YAC1C,KAAK,QAAQ,CAAC,SAAS;YACvB,KAAK,QAAQ,CAAC,YAAY;QAC5B;IACF,GAAG;QAAC;QAAwB;QAAc;QAAiB;KAAK;IAEhE,4DAA4D;IAC5D,MAAM,gCAAgC;QACpC,oBAAoB;QACpB,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,IAAI,SAAS;gBACX,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,AAAC,MAAgB,OAAO,IAAI;QAC1C,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,+BAA+B;IAC/B,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,aAAa;YACb,MAAM,UAAU,MAAM,MAAM,OAAO,KAAK,EAAE,OAAO,QAAQ;YAEzD,IAAI,SAAS;gBACX,IAAI,WAAW;oBACb;gBACF,OAAO;oBACL,yBAAyB;oBACzB,OAAO,IAAI,CAAC;oBACZ,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF;QACF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,AAAC,MAAgB,OAAO,IAAI;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,4BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;YAOxC,wCACC,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;;kCACjC,8OAAC,wNAAA,CAAA,kBAAe;wBAAC,WAAU;;;;;;kCAC3B,8OAAC,iIAAA,CAAA,aAAU;kCAAC;;;;;;kCACZ,8OAAC,iIAAA,CAAA,mBAAgB;;4BACd,uBAAuB,OAAO;0CAC/B,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,UAAU;;kDAEV,8OAAC,sMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACnB,mBAAmB,gBAAgB;;;;;;;;;;;;;;;;;;;0BAM5C,8OAAC,gIAAA,CAAA,OAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBAAK,UAAU,KAAK,YAAY,CAAC;oBAAW,WAAU;;sCACrD,8OAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sDACP,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,MAAK;gDACL,cAAa;gDACb,UAAU;gDACT,GAAG,KAAK;;;;;;;;;;;sDAGb,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sCAIlB,8OAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sDACP,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,MAAK;gDACL,cAAa;gDACb,UAAU;gDACT,GAAG,KAAK;;;;;;;;;;;sDAGb,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sCAKlB,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,WAAU;4BAAS,UAAU,aAAa;sCAC7D,YAAY,wBAAwB;;;;;;;;;;;;;;;;;YAK1C,4BACC,8OAAC;gBAAI,WAAU;;oBAAsB;oBACZ;kCACvB,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 722, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/app/%28public%29/login/page.tsx"], "sourcesContent": ["// src/app/(public)/login/page.tsx\n\"use client\";\n\nimport React from 'react';\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport LoginForm from '@/components/auth/LoginForm';\n\nexport default function LoginPage() {\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end flex items-center justify-center p-4\">\n      <div className=\"w-full max-w-md\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-heading-lg font-bold text-foreground mb-2\">\n            Iniciar Se<PERSON>\n          </h1>\n          <p className=\"text-muted-foreground\">\n            Accede a tu panel de control de Rayuela.ai\n          </p>\n        </div>\n\n        <Card className=\"shadow-soft border-border/50 backdrop-blur-sm bg-card/95\">\n          <CardContent className=\"p-6 md:p-8\">\n            <LoginForm showHeader={false} />\n          </CardContent>\n        </Card>\n\n        {/* Additional info */}\n        <div className=\"text-center mt-6\">\n          <p className=\"text-sm text-muted-foreground\">\n            ¿No tienes cuenta?{' '}\n            <a href=\"/register\" className=\"text-primary hover:text-primary/80 font-medium underline underline-offset-2\">\n              Regístrate gratis\n            </a>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AAIlC;AACA;AAJA;;;;AAMe,SAAS;IAEtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC,uIAAA,CAAA,UAAS;4BAAC,YAAY;;;;;;;;;;;;;;;;8BAK3B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAgC;4BACxB;0CACnB,8OAAC;gCAAE,MAAK;gCAAY,WAAU;0CAA8E;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxH", "debugId": null}}]}