/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Rayuela
 * OpenAPI spec version: v1
 */
import axios from "axios";
import type { AxiosRequestConfig, AxiosResponse } from "axios";
export type GetUsageHistoryApiV1UsageGetParams = {
  /**
   * Start date for historical data
   */
  start_date?: string | null;
  /**
   * End date for historical data
   */
  end_date?: string | null;
};

export type ListBatchJobsApiV1IngestionBatchGetParams = {
  /**
   * Maximum number of jobs to return
   */
  limit?: number;
  /**
   * Filter by job status
   */
  status?: string | null;
};

export type RefreshStorageUsageApiV1StorageRefreshPost200 = {
  [key: string]: unknown;
};

export type GetStorageUsageApiV1StorageUsageGet200 = { [key: string]: unknown };

export type GetSubscriptionUsageApiV1SubscriptionUsageGet200 = {
  [key: string]: unknown;
};

export type MonitorTablesApiV1MaintenanceMaintenanceMonitorTablesPost200 = {
  [key: string]: unknown;
};

export type MonitorTablesApiV1MaintenanceMaintenanceMonitorTablesPostParams = {
  run_async?: boolean;
};

export type GetSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGet200 =
  { [key: string]: unknown };

export type GetSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGetParams =
  {
    account_id?: number | null;
    run_async?: boolean;
  };

export type CleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPost200 =
  { [key: string]: unknown };

export type CleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPostParams =
  {
    retention_days?: number;
    account_id?: number | null;
    dry_run?: boolean;
    run_async?: boolean;
  };

export type ListArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGet200 =
  { [key: string]: unknown };

export type ListArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGetParams =
  {
    start_date?: string | null;
    end_date?: string | null;
    account_id?: number | null;
  };

export type ArchiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPost200 =
  { [key: string]: unknown };

export type ArchiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPostParams =
  {
    days_to_keep?: number;
    account_id?: number | null;
    batch_size?: number;
    run_async?: boolean;
  };

export type ArchiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPost200 =
  { [key: string]: unknown };

export type ArchiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPostParams =
  {
    days_to_keep?: number;
    account_id?: number | null;
    batch_size?: number;
    run_async?: boolean;
  };

export type GetTaskStatusApiV1MaintenanceMaintenanceTaskTaskIdGet200 = {
  [key: string]: unknown;
};

export type CleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPost200 =
  { [key: string]: unknown };

export type CleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPostParams =
  {
    days_to_keep?: number;
    account_id?: number | null;
    batch_size?: number;
    run_async?: boolean;
  };

export type CleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPost200 =
  { [key: string]: unknown };

export type CleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPostParams =
  {
    days_to_keep?: number;
    account_id?: number | null;
    run_async?: boolean;
  };

export type GetMetricsHistoryApiV1AnalyticsAnalyticsMetricsHistoryGetParams = {
  /**
   * Name of the metric to get history for
   */
  metric_name: string;
  /**
   * Maximum number of historical data points to return
   */
  limit?: number;
  account_id?: number | null;
};

export type CompareModelVersionsApiV1AnalyticsAnalyticsModelsCompareGetParams =
  {
    /**
     * List of model IDs to compare
     */
    model_ids?: number[];
    /**
     * Maximum number of models to compare if model_ids not provided
     */
    limit?: number;
    account_id?: number | null;
  };

export type GetRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGetParams =
  {
    /**
     * Filter by specific model ID
     */
    model_id?: number | null;
    /**
     * Filter by metric type (ndcg, map, catalog_coverage, etc.)
     */
    metric_type?: string | null;
    account_id?: number | null;
  };

export type GetEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGet200Item = {
  [key: string]: unknown;
};

export type GetEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGetParams = {
  /**
   * Filter by specific endpoint
   */
  endpoint?: string | null;
  /**
   * Filter by HTTP method
   */
  method?: string | null;
  account_id?: number | null;
};

export type GetAccountAnalyticsApiV1AnalyticsAnalyticsAccountGet200 = {
  [key: string]: unknown;
};

export type GetAccountAnalyticsApiV1AnalyticsAnalyticsAccountGetParams = {
  account_id?: number | null;
};

export type ListTrainingJobsApiV1PipelineJobsGetParams = {
  /**
   * Maximum number of jobs to return
   */
  limit?: number;
  /**
   * Filter by job status
   */
  status?: string | null;
};

export type TrainingCallbackApiV1PipelineCallbackJobIdPost200 = {
  [key: string]: unknown;
};

export type TrainingCallbackApiV1PipelineCallbackJobIdPostBody = {
  [key: string]: unknown;
};

export type ProcessTrainingJobApiV1PipelineProcessPost200 = {
  [key: string]: unknown;
};

export type ProcessTrainingJobApiV1PipelineProcessPostBody = {
  [key: string]: unknown;
};

export type TrainArtifactForAccountApiV1PipelineTrainAccountIdPost202 = {
  [key: string]: unknown;
};

export type InvalidateCacheApiV1PipelineInvalidateCachePostParams = {
  model_type?: string | null;
  metric_type?: string | null;
};

export type ListModelsApiV1PipelineModelsGetParams = {
  limit?: number;
  offset?: number;
};

export type ReadInteractionsApiV1InteractionsGetParams = {
  skip?: number;
  limit?: number;
};

export type GetRecommendationPerformanceApiV1RecommendationsPerformanceGetParams =
  {
    /**
     * Filter by specific model ID
     */
    model_id?: number | null;
    /**
     * Filter by metric type (ndcg, map, catalog_coverage, etc.)
     */
    metric_type?: string | null;
    account_id?: number | null;
  };

export type GetConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet200 = {
  [key: string]: unknown;
};

export type GetAlsoBoughtExternalApiV1RecommendationsAlsoBoughtExternalExternalProductIdGetParams =
  {
    skip?: number;
    limit?: number;
  };

export type GetSimilarProductsExternalApiV1RecommendationsProductsExternalExternalProductIdSimilarGetParams =
  {
    limit?: number;
    include_explanation?: boolean;
    explanation_level?: ExplanationLevel;
    skip?: number;
  };

export type QueryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPostBody =
  RecommendationQueryRequest | RecommendationQueryExternalRequest;

export type GetSimilarProductsApiV1RecommendationsProductsProductIdSimilarGetParams =
  {
    limit?: number;
    /**
     * Incluir explicación de la similitud
     */
    include_explanation?: boolean;
    /**
     * Nivel de detalle de la explicación
     */
    explanation_level?: ExplanationLevel;
    skip?: number;
  };

export type GetAlsoBoughtApiV1RecommendationsAlsoBoughtProductIdGetParams = {
  skip?: number;
  limit?: number;
};

export type GetRelatedCategoriesApiV1RecommendationsRelatedCategoriesCategoryGetParams =
  {
    skip?: number;
    limit?: number;
  };

export type GetCategoryProductsApiV1RecommendationsCategoryCategoryGetSortBy =
  (typeof GetCategoryProductsApiV1RecommendationsCategoryCategoryGetSortBy)[keyof typeof GetCategoryProductsApiV1RecommendationsCategoryCategoryGetSortBy];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetCategoryProductsApiV1RecommendationsCategoryCategoryGetSortBy =
  {
    popularity: "popularity",
    rating: "rating",
    price_asc: "price_asc",
    price_desc: "price_desc",
  } as const;

export type GetCategoryProductsApiV1RecommendationsCategoryCategoryGetParams = {
  sort_by?: GetCategoryProductsApiV1RecommendationsCategoryCategoryGetSortBy;
  skip?: number;
  limit?: number;
};

export type GetTopRatedApiV1RecommendationsTopRatedGetParams = {
  min_ratings?: number;
  category?: string | null;
  /**
   * Model type to use for recommendations
   */
  model_type?: string;
  /**
   * Incluir explicación de la recomendación
   */
  include_explanation?: boolean;
  skip?: number;
  limit?: number;
};

export type GetMostSoldApiV1RecommendationsMostSoldGetTimeframe =
  (typeof GetMostSoldApiV1RecommendationsMostSoldGetTimeframe)[keyof typeof GetMostSoldApiV1RecommendationsMostSoldGetTimeframe];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetMostSoldApiV1RecommendationsMostSoldGetTimeframe = {
  day: "day",
  week: "week",
  month: "month",
} as const;

export type GetMostSoldApiV1RecommendationsMostSoldGetParams = {
  timeframe?: GetMostSoldApiV1RecommendationsMostSoldGetTimeframe;
  category?: string | null;
  skip?: number;
  limit?: number;
};

export type GetRelatedSearchesApiV1RecommendationsRelatedSearchesProductIdGetParams =
  {
    skip?: number;
    limit?: number;
  };

export type GetPopularTrendsApiV1RecommendationsPopularTrendsGetParams = {
  /**
   * Model type to use for recommendations
   */
  model_type?: string;
  /**
   * Incluir explicación de la recomendación
   */
  include_explanation?: boolean;
  skip?: number;
  limit?: number;
};

export type GetTrendingSearchesApiV1RecommendationsTrendingSearchesGetParams = {
  skip?: number;
  limit?: number;
};

export type GetMostSearchedApiV1RecommendationsMostSearchedGetTimeframe =
  (typeof GetMostSearchedApiV1RecommendationsMostSearchedGetTimeframe)[keyof typeof GetMostSearchedApiV1RecommendationsMostSearchedGetTimeframe];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetMostSearchedApiV1RecommendationsMostSearchedGetTimeframe = {
  day: "day",
  week: "week",
  month: "month",
} as const;

export type GetMostSearchedApiV1RecommendationsMostSearchedGetParams = {
  timeframe?: GetMostSearchedApiV1RecommendationsMostSearchedGetTimeframe;
  skip?: number;
  limit?: number;
};

export type ReadProductsApiV1ProductsGetParams = {
  category?: string;
  skip?: number;
  limit?: number;
};

export type ReadEndUsersApiV1EndUsersGetParams = {
  skip?: number;
  limit?: number;
};

export type GetAvailablePlansApiV1PlansGet200 = {
  [key: string]: SrcApiV1EndpointsPlansPlanInfo;
};

export type GetAuditLogsApiV1AccountsAccountIdAuditLogsGetParams = {
  action?: string | null;
  artifact_name?: string | null;
  start_date?: string | null;
  end_date?: string | null;
};

export type VerifyEmailApiV1AuthVerifyEmailGetParams = {
  token: string;
};

export type AuthHealthCheckApiV1HealthAuthGet200 = { [key: string]: unknown };

export type DbHealthCheckApiV1HealthDbGet200 = { [key: string]: unknown };

export type HealthCheckApiV1HealthGet200 = { [key: string]: unknown };

/**
 * Plan information
 */
export interface SrcDbSchemasUsageSummaryPlanInfo {
  /** Plan features */
  features: PlanFeatures;
  /** Plan ID */
  id: string;
  /** Plan limits */
  limits: PlanLimits;
}

export type SrcApiV1EndpointsPlansPlanInfoMercadopagoPriceId = string | null;

export type SrcApiV1EndpointsPlansPlanInfoLimits = { [key: string]: unknown };

/**
 * Plan information model.
 */
export interface SrcApiV1EndpointsPlansPlanInfo {
  contact_required?: boolean;
  description: string;
  features: string[];
  id: string;
  limits: SrcApiV1EndpointsPlansPlanInfoLimits;
  mercadopago_price_id?: SrcApiV1EndpointsPlansPlanInfoMercadopagoPriceId;
  name: string;
  price: string;
  recommended?: boolean;
}

export type ValidationErrorLocItem = string | number;

export interface ValidationError {
  loc: ValidationErrorLocItem[];
  msg: string;
  type: string;
}

/**
 * Billing portal URL
 */
export type UsageSummaryResponseBillingPortalUrl = string | null;

/**
 * All available plans
 */
export type UsageSummaryResponseAllPlans = {
  [key: string]: SrcDbSchemasUsageSummaryPlanInfo;
};

/**
 * Complete usage summary response
 */
export interface UsageSummaryResponse {
  /** All available plans */
  allPlans?: UsageSummaryResponseAllPlans;
  /** API calls usage */
  apiCalls: ApiCallsUsage;
  /** Billing portal URL */
  billingPortalUrl?: UsageSummaryResponseBillingPortalUrl;
  /** Current plan limits */
  planLimits: PlanLimits;
  /** Storage usage */
  storage: StorageUsage;
  subscription: SubscriptionInfo;
  /** Training usage */
  training: TrainingUsage;
}

export type UsageStatsUpdatedAt = string | null;

export type UsageStatsLastReset = string | null;

/**
 * Estadísticas de uso de la API
 */
export interface UsageStats {
  api_calls_count?: number;
  last_reset?: UsageStatsLastReset;
  storage_used?: number;
  updated_at?: UsageStatsUpdatedAt;
}

/**
 * Usage history item response schema
 */
export interface UsageHistoryItemResponse {
  /** Number of API calls made on this date */
  apiCalls: number;
  /** Date of the usage record */
  date: string;
  /** Storage used in bytes on this date */
  storage: number;
}

/**
 * Next available training time
 */
export type TrainingUsageNextAvailable = string | null;

/**
 * Date of last training
 */
export type TrainingUsageLastTrainingDate = string | null;

/**
 * Training usage information
 */
export interface TrainingUsage {
  /** Whether training is available now */
  canTrainNow?: boolean;
  /** Training frequency limit */
  frequencyLimit?: string;
  /** Date of last training */
  lastTrainingDate?: TrainingUsageLastTrainingDate;
  /** Next available training time */
  nextAvailable?: TrainingUsageNextAvailable;
}

export type TrainingStatusTrainingTime = number | null;

export type TrainingStatusRecall = number | null;

export type TrainingStatusPrecision = number | null;

export type TrainingStatusNdcg = number | null;

export type TrainingStatusMap = number | null;

export type TrainingStatusDiversity = number | null;

export type TrainingStatusDataPoints = number | null;

export type TrainingStatusCustomMetricsAnyOf = { [key: string]: unknown };

export type TrainingStatusCustomMetrics =
  TrainingStatusCustomMetricsAnyOf | null;

export type TrainingStatusCoverage = number | null;

/**
 * Estado del entrenamiento y métricas
 */
export interface TrainingStatus {
  coverage?: TrainingStatusCoverage;
  customMetrics?: TrainingStatusCustomMetrics;
  dataPoints?: TrainingStatusDataPoints;
  diversity?: TrainingStatusDiversity;
  map?: TrainingStatusMap;
  ndcg?: TrainingStatusNdcg;
  precision?: TrainingStatusPrecision;
  recall?: TrainingStatusRecall;
  trainingTime?: TrainingStatusTrainingTime;
}

export type TrainingResponseTaskId = string | null;

export type TrainingResponseJobId = number | null;

export type TrainingResponseAccountId = number | null;

/**
 * Respuesta para el inicio de entrenamiento
 */
export interface TrainingResponse {
  accountId?: TrainingResponseAccountId;
  jobId?: TrainingResponseJobId;
  message: string;
  taskId?: TrainingResponseTaskId;
}

export type TrainingJobStatusTaskId = string | null;

export type TrainingJobStatusStartedAt = string | null;

/**
 * Estado detallado de un trabajo de entrenamiento
 */
export interface TrainingJobStatus {
  completedAt?: TrainingJobStatusCompletedAt;
  createdAt: string;
  errorMessage?: TrainingJobStatusErrorMessage;
  jobId: number;
  metrics?: TrainingJobStatusMetrics;
  model?: TrainingJobStatusModel;
  parameters?: TrainingJobStatusParameters;
  startedAt?: TrainingJobStatusStartedAt;
  status: string;
  taskId?: TrainingJobStatusTaskId;
}

export type TrainingJobStatusParametersAnyOf = { [key: string]: unknown };

export type TrainingJobStatusParameters =
  TrainingJobStatusParametersAnyOf | null;

export type TrainingJobStatusModel = ModelInfo | null;

export type TrainingJobStatusMetricsAnyOf = { [key: string]: unknown };

export type TrainingJobStatusMetrics = TrainingJobStatusMetricsAnyOf | null;

export type TrainingJobStatusErrorMessage = string | null;

export type TrainingJobStatusCompletedAt = string | null;

export type SystemUserUpdatePassword = string | null;

export interface SystemUserUpdate {
  email: string;
  password?: SystemUserUpdatePassword;
}

export type SystemUserResponseDeletedAt = string | null;

/**
 * Esquema de respuesta para usuarios del sistema que excluye campos sensibles
 */
export interface SystemUserResponse {
  account_id: number;
  created_at: string;
  deleted_at?: SystemUserResponseDeletedAt;
  email: string;
  id: number;
  is_active: boolean;
  is_admin: boolean;
  roles?: Role[];
  updated_at: string;
}

export interface SystemUserCreate {
  email: string;
  password: string;
}

/**
 * Expiration date
 */
export type SubscriptionInfoExpiresAt = string | null;

/**
 * Subscription information
 */
export interface SubscriptionInfo {
  /** Expiration date */
  expiresAt?: SubscriptionInfoExpiresAt;
  /** Whether subscription is active */
  isActive?: boolean;
  /** Plan type */
  plan: string;
}

export type SubscriptionBasicInfoExpiresAt = string | null;

/**
 * Información básica de suscripción para incluir en las respuestas de cuenta
 */
export interface SubscriptionBasicInfo {
  expiresAt?: SubscriptionBasicInfoExpiresAt;
  isActive: boolean;
  plan: string;
}

/**
 * Source of measurement (redis_cache, database_calculation)
 */
export type StorageUsageSource = string | null;

/**
 * Last measurement timestamp
 */
export type StorageUsageLastMeasured = string | null;

/**
 * Storage usage information
 */
export interface StorageUsage {
  /** Last measurement timestamp */
  lastMeasured?: StorageUsageLastMeasured;
  /** Storage limit in bytes */
  limitBytes?: number;
  /** Percentage of limit used */
  percentage?: number;
  /** Source of measurement (redis_cache, database_calculation) */
  source?: StorageUsageSource;
  /** Storage used in bytes */
  usedBytes?: number;
}

/**
 * Dirección de ordenamiento
 */
export type SortDirection = (typeof SortDirection)[keyof typeof SortDirection];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SortDirection = {
  asc: "asc",
  desc: "desc",
} as const;

/**
 * Configuración de ordenamiento
 */
export interface SortConfig {
  /** Dirección del ordenamiento */
  direction?: SortDirection;
  /** Campo por el cual ordenar */
  field: string;
}

export interface Search {
  account_id: number;
  id: number;
  query: string;
  timestamp: string;
  user_id: number;
}

export type RoleType = (typeof RoleType)[keyof typeof RoleType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const RoleType = {
  ADMIN: "ADMIN",
  EDITOR: "EDITOR",
  VIEWER: "VIEWER",
} as const;

/**
 * Descripción del rol
 */
export type RoleCreateDescription = string | null;

export interface RoleCreate {
  /** Descripción del rol */
  description?: RoleCreateDescription;
  name: RoleType;
}

/**
 * Descripción del rol
 */
export type RoleDescription = string | null;

export interface Role {
  account_id: number;
  created_at: string;
  /** Descripción del rol */
  description?: RoleDescription;
  id: number;
  name: RoleType;
  updated_at: string;
}

/**
 * Registration response schema
 */
export interface RegisterResponse {
  /** JWT access token */
  accessToken: string;
  /** Account ID */
  accountId: number;
  /** Initial API key for the user */
  apiKey: string;
  /** Whether user is admin */
  isAdmin: boolean;
  /** Success message */
  message: string;
  /** Token type (bearer) */
  tokenType: string;
  /** User ID */
  userId: number;
}

/**
 * Schema para el registro completo de una cuenta con usuario administrador.
 */
export interface RegisterRequest {
  /**
   * Nombre de la cuenta
   * @minLength 1
   */
  accountName: string;
  /** Email del usuario administrador */
  email: string;
  /**
   * Contraseña del usuario administrador
   * @minLength 8
   */
  password: string;
}

/**
 * Estrategia de recomendación a utilizar
 */
export type RecommendationQueryRequestStrategy = string | null;

/**
 * Configuración de ordenamiento
 */
export type RecommendationQueryRequestSortBy = SortConfig | null;

/**
 * Filtros estructurados para aplicar a las recomendaciones
 */
export type RecommendationQueryRequestFilters = FilterGroup | null;

/**
 * Contexto explícito para filtrar o re-rankear recomendaciones
 */
export type RecommendationQueryRequestContext = RecommendationContext | null;

/**
 * Esquema para solicitudes de recomendaciones con filtros complejos.
Permite especificar filtros estructurados y otros parámetros en el cuerpo de la solicitud.
 */
export interface RecommendationQueryRequest {
  /** Contexto explícito para filtrar o re-rankear recomendaciones */
  context?: RecommendationQueryRequestContext;
  /** Filtros estructurados para aplicar a las recomendaciones */
  filters?: RecommendationQueryRequestFilters;
  /** Incluir explicación de la recomendación */
  include_explanation?: boolean;
  /**
   * Número máximo de elementos a devolver
   * @maximum 100
   */
  limit?: number;
  /** Tipo de modelo a utilizar */
  model_type?: string;
  /**
   * Número de elementos a saltar
   * @minimum 0
   */
  skip?: number;
  /** Configuración de ordenamiento */
  sort_by?: RecommendationQueryRequestSortBy;
  /** Estrategia de recomendación a utilizar */
  strategy?: RecommendationQueryRequestStrategy;
  /**
   * ID del usuario para el que se generan recomendaciones
   */
  user_id: number;
}

/**
 * Estrategia de recomendación a utilizar
 */
export type RecommendationQueryExternalRequestStrategy = string | null;

/**
 * Configuración de ordenamiento
 */
export type RecommendationQueryExternalRequestSortBy = SortConfig | null;

/**
 * Filtros estructurados para aplicar a las recomendaciones
 */
export type RecommendationQueryExternalRequestFilters = FilterGroup | null;

/**
 * Contexto explícito para filtrar o re-rankear recomendaciones
 */
export type RecommendationQueryExternalRequestContext =
  RecommendationContext | null;

/**
 * Esquema para solicitudes de recomendaciones usando external_user_id.
Permite a los clientes usar sus propios identificadores de usuario.
 */
export interface RecommendationQueryExternalRequest {
  /** Contexto explícito para filtrar o re-rankear recomendaciones */
  context?: RecommendationQueryExternalRequestContext;
  /** ID externo del usuario proporcionado por el cliente */
  external_user_id: string;
  /** Filtros estructurados para aplicar a las recomendaciones */
  filters?: RecommendationQueryExternalRequestFilters;
  /** Incluir explicación de la recomendación */
  include_explanation?: boolean;
  /**
   * Número máximo de elementos a devolver
   * @maximum 100
   */
  limit?: number;
  /** Tipo de modelo a utilizar */
  model_type?: string;
  /**
   * Número de elementos a saltar
   * @minimum 0
   */
  skip?: number;
  /** Configuración de ordenamiento */
  sort_by?: RecommendationQueryExternalRequestSortBy;
  /** Estrategia de recomendación a utilizar */
  strategy?: RecommendationQueryExternalRequestStrategy;
}

/**
 * Specific model ID if filtered
 */
export type RecommendationPerformanceResponseModelId = number | null;

/**
 * Complete recommendation performance response
 */
export interface RecommendationPerformanceResponse {
  /** Specific model ID if filtered */
  modelId?: RecommendationPerformanceResponseModelId;
  /** Offline metrics */
  offlineMetrics: OfflineMetrics;
  /** Online metrics */
  onlineMetrics: OnlineMetrics;
}

/**
 * Momento del día (mañana, tarde, noche)
 */
export type RecommendationContextTimeOfDay = string | null;

/**
 * ID del producto de origen (ej: en página de detalle)
 */
export type RecommendationContextSourceItemId = number | null;

/**
 * Consulta de búsqueda actual
 */
export type RecommendationContextSearchQuery = string | null;

/**
 * IDs de productos vistos recientemente
 */
export type RecommendationContextRecentlyViewedIds = number[] | null;

/**
 * Tipo de página donde se muestran las recomendaciones
 */
export type RecommendationContextPageType = PageType | null;

/**
 * Ubicación geográfica del usuario
 */
export type RecommendationContextLocation = string | null;

/**
 * Tipo de dispositivo desde donde se solicitan las recomendaciones
 */
export type RecommendationContextDevice = DeviceType | null;

export type RecommendationContextCustomAttributesAnyOf = {
  [key: string]: unknown;
};

/**
 * Atributos personalizados adicionales
 */
export type RecommendationContextCustomAttributes =
  RecommendationContextCustomAttributesAnyOf | null;

/**
 * ID de la categoría actual
 */
export type RecommendationContextCategoryId = number | null;

/**
 * IDs de productos en el carrito
 */
export type RecommendationContextCartItemIds = number[] | null;

/**
 * Contexto explícito para recomendaciones personalizadas
 */
export interface RecommendationContext {
  /** IDs de productos en el carrito */
  cart_item_ids?: RecommendationContextCartItemIds;
  /** ID de la categoría actual */
  category_id?: RecommendationContextCategoryId;
  /** Atributos personalizados adicionales */
  custom_attributes?: RecommendationContextCustomAttributes;
  /** Tipo de dispositivo desde donde se solicitan las recomendaciones */
  device?: RecommendationContextDevice;
  /** Ubicación geográfica del usuario */
  location?: RecommendationContextLocation;
  /** Tipo de página donde se muestran las recomendaciones */
  page_type?: RecommendationContextPageType;
  /** IDs de productos vistos recientemente */
  recently_viewed_ids?: RecommendationContextRecentlyViewedIds;
  /** Consulta de búsqueda actual */
  search_query?: RecommendationContextSearchQuery;
  /** ID del producto de origen (ej: en página de detalle) */
  source_item_id?: RecommendationContextSourceItemId;
  /** Momento del día (mañana, tarde, noche) */
  time_of_day?: RecommendationContextTimeOfDay;
}

export type ProductUpdatePrice = number | string | null;

export type ProductUpdateNumRatings = number | null;

export type ProductUpdateName = string | null;

export type ProductUpdateInventoryCount = number | null;

/**
 * External identifier provided by the client (e.g., SKU, product code)
 */
export type ProductUpdateExternalId = string | null;

export type ProductUpdateDescription = string | null;

export type ProductUpdateCategory = string | null;

export type ProductUpdateAverageRating = number | null;

export interface ProductUpdate {
  average_rating?: ProductUpdateAverageRating;
  category?: ProductUpdateCategory;
  description?: ProductUpdateDescription;
  /** External identifier provided by the client (e.g., SKU, product code) */
  external_id?: ProductUpdateExternalId;
  inventory_count?: ProductUpdateInventoryCount;
  name?: ProductUpdateName;
  num_ratings?: ProductUpdateNumRatings;
  price?: ProductUpdatePrice;
}

export type ProductCreatePrice = number | string;

export type ProductCreateDescription = string | null;

export interface ProductCreate {
  /**
   * @minimum 0
   * @maximum 5
   */
  averageRating?: number;
  /**
   * @minLength 1
   * @maxLength 100
   */
  category: string;
  description?: ProductCreateDescription;
  /**
   * External identifier provided by the client (e.g., SKU, product code)
   * @minLength 1
   * @maxLength 255
   */
  externalId: string;
  /** @minimum 0 */
  inventoryCount?: number;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /** @minimum 0 */
  numRatings?: number;
  price: ProductCreatePrice;
}

export type ProductSource = string | null;

export type ProductScore = number | null;

export type ProductExplanation = string | null;

export type ProductDescription = string | null;

export type ProductDeletedAt = string | null;

export interface Product {
  accountId: number;
  /**
   * @minimum 0
   * @maximum 5
   */
  averageRating?: number;
  /**
   * @minLength 1
   * @maxLength 100
   */
  category: string;
  createdAt: string;
  deletedAt?: ProductDeletedAt;
  description?: ProductDescription;
  explanation?: ProductExplanation;
  /**
   * External identifier provided by the client (e.g., SKU, product code)
   * @minLength 1
   * @maxLength 255
   */
  externalId: string;
  /** @minimum 0 */
  inventoryCount?: number;
  isActive?: boolean;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /** @minimum 0 */
  numRatings?: number;
  price: string;
  productId: number;
  score?: ProductScore;
  source?: ProductSource;
  updatedAt: string;
}

/**
 * Plan limits information
 */
export interface PlanLimits {
  /** API calls limit */
  apiCalls?: number;
  /** Concurrent requests limit */
  maxConcurrentRequests?: number;
  /** Maximum items */
  maxItems?: number;
  /** Formatted max items */
  maxItemsFormatted?: string;
  /** Rate limit per minute */
  maxRequestsPerMinute?: number;
  /** Maximum users */
  maxUsers?: number;
  /** Formatted max users */
  maxUsersFormatted?: string;
  /** Cache TTL in seconds */
  recommendationCacheTtl?: number;
  /** Storage limit in bytes */
  storageBytes?: number;
  /** Storage limit in GB */
  storageGb?: number;
  /** Storage limit in MB */
  storageMb?: number;
  /** Training data limit */
  trainingDataLimit?: number;
  /** Formatted training data limit */
  trainingDataLimitFormatted?: string;
  /** Training frequency */
  trainingFrequency?: string;
}

/**
 * Plan features information
 */
export interface PlanFeatures {
  /** Available models */
  availableModels?: string[];
}

export type PermissionType =
  (typeof PermissionType)[keyof typeof PermissionType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PermissionType = {
  READ: "READ",
  WRITE: "WRITE",
  DELETE: "DELETE",
  ADMIN: "ADMIN",
  PRODUCT_READ: "PRODUCT_READ",
  PRODUCT_CREATE: "PRODUCT_CREATE",
  PRODUCT_UPDATE: "PRODUCT_UPDATE",
  PRODUCT_DELETE: "PRODUCT_DELETE",
  USER_READ: "USER_READ",
  USER_CREATE: "USER_CREATE",
  USER_UPDATE: "USER_UPDATE",
  USER_DELETE: "USER_DELETE",
  SYSTEM_USER_READ: "SYSTEM_USER_READ",
  SYSTEM_USER_CREATE: "SYSTEM_USER_CREATE",
  SYSTEM_USER_UPDATE: "SYSTEM_USER_UPDATE",
  SYSTEM_USER_DELETE: "SYSTEM_USER_DELETE",
  ROLE_READ: "ROLE_READ",
  ROLE_CREATE: "ROLE_CREATE",
  ROLE_UPDATE: "ROLE_UPDATE",
  ROLE_DELETE: "ROLE_DELETE",
  PERMISSION_ASSIGN: "PERMISSION_ASSIGN",
  ANALYTICS_READ: "ANALYTICS_READ",
  MODEL_READ: "MODEL_READ",
  MODEL_CREATE: "MODEL_CREATE",
  MODEL_UPDATE: "MODEL_UPDATE",
  MODEL_DELETE: "MODEL_DELETE",
  TRAINING_JOB_READ: "TRAINING_JOB_READ",
  TRAINING_JOB_CREATE: "TRAINING_JOB_CREATE",
  TRAINING_JOB_UPDATE: "TRAINING_JOB_UPDATE",
  TRAINING_JOB_CANCEL: "TRAINING_JOB_CANCEL",
} as const;

export interface PermissionCreate {
  name: PermissionType;
}

export interface Permission {
  account_id: number;
  id: number;
  name: PermissionType;
}

export interface PaginatedResponseSearch {
  items: Search[];
  limit: number;
  skip: number;
  total: number;
}

export interface PaginatedResponseProduct {
  items: Product[];
  limit: number;
  skip: number;
  total: number;
}

export interface PaginatedResponseInteraction {
  items: Interaction[];
  limit: number;
  skip: number;
  total: number;
}

export interface PaginatedResponseEndUser {
  items: EndUser[];
  limit: number;
  skip: number;
  total: number;
}

export interface PaginatedResponseCategory {
  items: Category[];
  limit: number;
  skip: number;
  total: number;
}

/**
 * Tipos de páginas donde se muestran recomendaciones
 */
export type PageType = (typeof PageType)[keyof typeof PageType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PageType = {
  home: "home",
  product_detail: "product_detail",
  category: "category",
  search_results: "search_results",
  cart: "cart",
  checkout: "checkout",
  user_profile: "user_profile",
} as const;

/**
 * Online metrics data
 */
export interface OnlineMetrics {
  /** Bounce rate */
  bounceRate?: number;
  /** Click-through rate */
  ctr?: number;
  /** Conversion rate */
  cvr?: number;
  /** Average session duration */
  sessionDuration?: number;
  /** User engagement score */
  userEngagement?: number;
}

/**
 * Offline metrics data
 */
export interface OfflineMetrics {
  /** Overall catalog coverage */
  catalogCoverage?: number;
  /** Overall diversity */
  diversity?: number;
  /** Overall MAP */
  map?: number;
  /** Individual model metrics */
  models?: ModelMetrics[];
  /** Overall NDCG */
  ndcg?: number;
  /** Overall novelty */
  novelty?: number;
  /** Overall precision */
  precision?: number;
  /** Overall recall */
  recall?: number;
  /** Overall serendipity */
  serendipity?: number;
}

/**
 * Nombre descriptivo de la API Key
 */
export type NewApiKeyResponseName = string | null;

/**
 * Schema for a newly created API Key (includes the full key)
 */
export interface NewApiKeyResponse {
  /** API Key completa (solo se muestra una vez) */
  api_key: string;
  /** Fecha de creación */
  created_at: string;
  /** ID único de la API Key */
  id: number;
  /** Mensaje informativo */
  message: string;
  /** Nombre descriptivo de la API Key */
  name?: NewApiKeyResponseName;
  /** Prefijo de la API Key */
  prefix: string;
}

/**
 * Individual model metrics
 */
export interface ModelMetrics {
  /** Catalog coverage */
  catalogCoverage?: number;
  /** Diversity metric */
  diversity?: number;
  /** MAP score */
  map?: number;
  /** Model ID */
  modelId: number;
  /** Model type (collaborative, content, hybrid) */
  modelType: string;
  /** NDCG metric */
  ndcg?: number;
  /** Novelty metric */
  novelty?: number;
  /** Precision metric */
  precision?: number;
  /** Recall metric */
  recall?: number;
  /** Serendipity metric */
  serendipity?: number;
  /** Model version */
  version?: string;
}

export type ModelMetadataResponsePerformanceMetrics = {
  [key: string]: unknown;
};

export type ModelMetadataResponseParameters = { [key: string]: unknown };

export type ModelMetadataResponseDescription = string | null;

export interface ModelMetadataResponse {
  artifact_name: string;
  artifact_version: string;
  description?: ModelMetadataResponseDescription;
  id: number;
  parameters: ModelMetadataResponseParameters;
  performance_metrics: ModelMetadataResponsePerformanceMetrics;
  training_date: string;
}

export type ModelInfoDescription = string | null;

/**
 * Información básica de un modelo
 */
export interface ModelInfo {
  artifactName: string;
  artifactVersion: string;
  description?: ModelInfoDescription;
  id: number;
  trainingDate: string;
}

/**
 * Improvement suggestions
 */
export type ModelComparisonResponseImprovements = { [key: string]: string };

/**
 * Comparison matrix between models
 */
export type ModelComparisonResponseComparisonMatrix = {
  [key: string]: { [key: string]: number };
};

/**
 * Model comparison response
 */
export interface ModelComparisonResponse {
  /** Best performing model */
  bestModel?: ModelComparisonResponseBestModel;
  /** Comparison matrix between models */
  comparisonMatrix?: ModelComparisonResponseComparisonMatrix;
  /** Improvement suggestions */
  improvements?: ModelComparisonResponseImprovements;
  /** Models being compared */
  models?: ModelComparisonMetrics[];
}

/**
 * All metrics
 */
export type ModelComparisonMetricsMetrics = { [key: string]: number };

/**
 * Metrics for model comparison
 */
export interface ModelComparisonMetrics {
  /** Creation date */
  createdAt: string;
  /** All metrics */
  metrics?: ModelComparisonMetricsMetrics;
  /** Model ID */
  modelId: number;
  /** Model name */
  modelName: string;
  /** Overall performance score */
  performanceScore?: number;
}

/**
 * Best performing model
 */
export type ModelComparisonResponseBestModel = ModelComparisonMetrics | null;

/**
 * Associated model ID
 */
export type MetricsHistoryPointModelId = number | null;

/**
 * Single point in metrics history
 */
export interface MetricsHistoryPoint {
  /** Associated model ID */
  modelId?: MetricsHistoryPointModelId;
  /** Timestamp */
  timestamp: string;
  /** Metric value */
  value: number;
}

/**
 * Metrics history response
 */
export interface MetricsHistoryResponse {
  /** Average value over the period */
  averageValue?: number;
  /** Historical data points */
  dataPoints?: MetricsHistoryPoint[];
  /** Name of the metric */
  metricName: string;
  /** Trend direction (improving, declining, stable) */
  trend?: string;
}

/**
 * Login response schema
 */
export interface LoginResponse {
  /** JWT access token */
  accessToken: string;
  /** Account ID */
  accountId: number;
  /** Whether user is admin */
  isAdmin: boolean;
  /** Token type (bearer) */
  tokenType: string;
}

/**
 * Schema para el login de usuarios con JSON payload.
 */
export interface LoginRequest {
  /** Email del usuario */
  email: string;
  /**
   * Contraseña del usuario
   * @minLength 1
   */
  password: string;
}

/**
 * Operadores lógicos para combinar filtros
 */
export type LogicalOperator =
  (typeof LogicalOperator)[keyof typeof LogicalOperator];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const LogicalOperator = {
  and: "and",
  or: "or",
} as const;

export interface InventoryUpdate {
  /**
   * Cantidad de inventario disponible
   * @minimum 0
   */
  inventory_count: number;
}

export type InteractionType =
  (typeof InteractionType)[keyof typeof InteractionType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const InteractionType = {
  VIEW: "VIEW",
  LIKE: "LIKE",
  PURCHASE: "PURCHASE",
  CART: "CART",
  RATING: "RATING",
  WISHLIST: "WISHLIST",
  CLICK: "CLICK",
  SEARCH: "SEARCH",
  FAVORITE: "FAVORITE",
} as const;

export type InteractionExternalCreateValue = number | string;

/**
 * Schema for creating interactions using external IDs.
 */
export interface InteractionExternalCreate {
  /** External product identifier provided by the client */
  external_product_id: string;
  /** External user identifier provided by the client */
  external_user_id: string;
  interaction_type: InteractionType;
  recommendation_metadata?: InteractionExternalCreateRecommendationMetadata;
  value: InteractionExternalCreateValue;
}

export type InteractionExternalCreateRecommendationMetadataAnyOf = {
  [key: string]: unknown;
};

export type InteractionExternalCreateRecommendationMetadata =
  InteractionExternalCreateRecommendationMetadataAnyOf | null;

export type InteractionCreateValue = number | string;

export interface InteractionCreate {
  interactionType: InteractionType;
  productId: number;
  recommendationMetadata?: InteractionCreateRecommendationMetadata;
  userId: number;
  value: InteractionCreateValue;
}

export type InteractionCreateRecommendationMetadataAnyOf = {
  [key: string]: unknown;
};

export type InteractionCreateRecommendationMetadata =
  InteractionCreateRecommendationMetadataAnyOf | null;

export type InteractionRecommendationMetadataAnyOf = { [key: string]: unknown };

export type InteractionRecommendationMetadata =
  InteractionRecommendationMetadataAnyOf | null;

export interface Interaction {
  accountId: number;
  id: number;
  interactionType: InteractionType;
  productId: number;
  recommendationMetadata?: InteractionRecommendationMetadata;
  timestamp: string;
  userId: number;
  value: string;
}

export interface HTTPValidationError {
  detail?: ValidationError[];
}

/**
 * Operadores para filtros estructurados
 */
export type FilterOperator =
  (typeof FilterOperator)[keyof typeof FilterOperator];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const FilterOperator = {
  eq: "eq",
  ne: "ne",
  gt: "gt",
  gte: "gte",
  lt: "lt",
  lte: "lte",
  in: "in",
  nin: "nin",
  contains: "contains",
  startswith: "startswith",
  endswith: "endswith",
} as const;

/**
 * Filtro para recomendaciones
 */
export interface Filter {
  /** Campo a filtrar */
  field: string;
  /** Operador de filtrado */
  operator: FilterOperator;
  /** Valor a comparar */
  value: unknown;
}

export type FilterGroupFiltersItem = Filter | FilterGroup;

/**
 * Grupo de filtros con lógica AND/OR
 */
export interface FilterGroup {
  /** Lista de filtros o grupos de filtros */
  filters: FilterGroupFiltersItem[];
  /** Lógica de combinación (and/or) */
  logic?: LogicalOperator;
}

/**
 * Razones para recomendar un producto
 */
export type ExplanationReason =
  (typeof ExplanationReason)[keyof typeof ExplanationReason];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const ExplanationReason = {
  similar_users: "similar_users",
  similar_items: "similar_items",
  category_affinity: "category_affinity",
  popular_item: "popular_item",
  trending_item: "trending_item",
  attribute_match: "attribute_match",
  complementary_item: "complementary_item",
  recent_interaction: "recent_interaction",
  personalized_ranking: "personalized_ranking",
  new_item: "new_item",
  diversity: "diversity",
  seasonal: "seasonal",
  promotional: "promotional",
} as const;

/**
 * Niveles de detalle para explicaciones de recomendaciones
 */
export type ExplanationLevel =
  (typeof ExplanationLevel)[keyof typeof ExplanationLevel];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const ExplanationLevel = {
  simple: "simple",
  detailed: "detailed",
} as const;

/**
 * ID del elemento (si aplica)
 */
export type ExplanationEvidenceId = number | null;

/**
 * Evidencia que respalda una explicación
 */
export interface ExplanationEvidence {
  /** ID del elemento (si aplica) */
  id?: ExplanationEvidenceId;
  /** Nombre o descripción del elemento */
  name: string;
  /**
   * Relevancia de esta evidencia (0-1)
   * @minimum 0
   * @maximum 1
   */
  relevance: number;
  /** Tipo de evidencia (producto, categoría, atributo, etc.) */
  type: string;
}

/**
 * Minimum price preference for recommendations
 */
export type EndUserCreatePriceRangeMin = number | string | null;

/**
 * Maximum price preference for recommendations
 */
export type EndUserCreatePriceRangeMax = number | string | null;

/**
 * List of preferred product categories for initial recommendations
 */
export type EndUserCreatePreferredCategories = string[] | null;

/**
 * List of preferred brands for initial recommendations
 */
export type EndUserCreatePreferredBrands = string[] | null;

export type EndUserCreateOnboardingPreferencesAnyOf = {
  [key: string]: unknown;
};

/**
 * Additional preferences collected during onboarding
 */
export type EndUserCreateOnboardingPreferences =
  EndUserCreateOnboardingPreferencesAnyOf | null;

/**
 * List of categories to avoid in recommendations
 */
export type EndUserCreateDislikedCategories = string[] | null;

/**
 * List of brands to avoid in recommendations
 */
export type EndUserCreateDislikedBrands = string[] | null;

export interface EndUserCreate {
  /** Demographic information (age_group, gender, location, etc.) */
  demographicInfo?: EndUserCreateDemographicInfo;
  /** List of brands to avoid in recommendations */
  dislikedBrands?: EndUserCreateDislikedBrands;
  /** List of categories to avoid in recommendations */
  dislikedCategories?: EndUserCreateDislikedCategories;
  externalId: string;
  /** Additional preferences collected during onboarding */
  onboardingPreferences?: EndUserCreateOnboardingPreferences;
  /** List of preferred brands for initial recommendations */
  preferredBrands?: EndUserCreatePreferredBrands;
  /** List of preferred product categories for initial recommendations */
  preferredCategories?: EndUserCreatePreferredCategories;
  /** Maximum price preference for recommendations */
  priceRangeMax?: EndUserCreatePriceRangeMax;
  /** Minimum price preference for recommendations */
  priceRangeMin?: EndUserCreatePriceRangeMin;
}

export type EndUserCreateDemographicInfoAnyOf = { [key: string]: unknown };

/**
 * Demographic information (age_group, gender, location, etc.)
 */
export type EndUserCreateDemographicInfo =
  EndUserCreateDemographicInfoAnyOf | null;

export type EndUserPriceRangeMin = string | null;

export type EndUserPriceRangeMax = string | null;

export type EndUserPreferredCategories = string[] | null;

export type EndUserPreferredBrands = string[] | null;

export interface EndUser {
  accountId: number;
  createdAt: string;
  deletedAt?: EndUserDeletedAt;
  demographicInfo?: EndUserDemographicInfo;
  dislikedBrands?: EndUserDislikedBrands;
  dislikedCategories?: EndUserDislikedCategories;
  externalId: string;
  isActive: boolean;
  onboardingPreferences?: EndUserOnboardingPreferences;
  preferredBrands?: EndUserPreferredBrands;
  preferredCategories?: EndUserPreferredCategories;
  priceRangeMax?: EndUserPriceRangeMax;
  priceRangeMin?: EndUserPriceRangeMin;
  updatedAt: string;
  userId: number;
}

export type EndUserOnboardingPreferencesAnyOf = { [key: string]: unknown };

export type EndUserOnboardingPreferences =
  EndUserOnboardingPreferencesAnyOf | null;

export type EndUserDislikedCategories = string[] | null;

export type EndUserDislikedBrands = string[] | null;

export type EndUserDemographicInfoAnyOf = { [key: string]: unknown };

export type EndUserDemographicInfo = EndUserDemographicInfoAnyOf | null;

export type EndUserDeletedAt = string | null;

/**
 * Tipos de dispositivos desde donde se solicitan recomendaciones
 */
export type DeviceType = (typeof DeviceType)[keyof typeof DeviceType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeviceType = {
  desktop: "desktop",
  mobile: "mobile",
  tablet: "tablet",
  tv: "tv",
  other: "other",
} as const;

/**
 * Explicación detallada de una recomendación
 */
export interface DetailedExplanation {
  /**
   * Confianza en la recomendación (0-1)
   * @minimum 0
   * @maximum 1
   */
  confidence: number;
  /** Evidencia que respalda la explicación */
  evidence?: ExplanationEvidence[];
  /** Razón principal de la recomendación */
  primary_reason: ExplanationReason;
  /** Razones secundarias */
  secondary_reasons?: ExplanationReason[];
  /** Fuente de la recomendación (collaborative, content, hybrid) */
  source: string;
  /** Explicación en texto plano */
  text_explanation: string;
}

/**
 * Schema para la respuesta de creación de sesión del portal de facturación.
 */
export interface CreatePortalSessionResponse {
  /** URL de la sesión del portal de facturación */
  url: string;
}

/**
 * URL de retorno después de usar el portal
 */
export type CreatePortalSessionRequestReturnUrl = string | null;

/**
 * Pasarela de pago a utilizar (mercadopago)
 */
export type CreatePortalSessionRequestPaymentGateway = string | null;

/**
 * Schema para crear una sesión del portal de facturación.
 */
export interface CreatePortalSessionRequest {
  /** Pasarela de pago a utilizar (mercadopago) */
  payment_gateway?: CreatePortalSessionRequestPaymentGateway;
  /** URL de retorno después de usar el portal */
  return_url?: CreatePortalSessionRequestReturnUrl;
}

/**
 * ID de la sesión de checkout (solo para Stripe)
 */
export type CreateCheckoutSessionResponseSessionId = string | null;

/**
 * Schema para la respuesta de creación de sesión de checkout.
 */
export interface CreateCheckoutSessionResponse {
  /** ID de la sesión de checkout (solo para Stripe) */
  session_id?: CreateCheckoutSessionResponseSessionId;
  /** URL de la sesión de checkout */
  url: string;
}

/**
 * URL de redirección en caso de éxito
 */
export type CreateCheckoutSessionRequestSuccessUrl = string | null;

/**
 * Pasarela de pago a utilizar (mercadopago)
 */
export type CreateCheckoutSessionRequestPaymentGateway = string | null;

/**
 * URL de redirección en caso de cancelación
 */
export type CreateCheckoutSessionRequestCancelUrl = string | null;

/**
 * Schema para crear una sesión de checkout.
 */
export interface CreateCheckoutSessionRequest {
  /** URL de redirección en caso de cancelación */
  cancel_url?: CreateCheckoutSessionRequestCancelUrl;
  /** Pasarela de pago a utilizar (mercadopago) */
  payment_gateway?: CreateCheckoutSessionRequestPaymentGateway;
  /** Price ID (Mercado Pago) */
  price_id: string;
  /** URL de redirección en caso de éxito */
  success_url?: CreateCheckoutSessionRequestSuccessUrl;
}

export type CategoryRelevance = number | null;

/**
 * Esquema para representar una categoría de productos.
 */
export interface Category {
  id: number;
  name: string;
  relevance?: CategoryRelevance;
}

export type BatchIngestionResponseTaskId = string | null;

/**
 * Esquema para la respuesta de la carga masiva
 */
export interface BatchIngestionResponse {
  jobId: number;
  message: string;
  status: string;
  taskId?: BatchIngestionResponseTaskId;
  totalInteractions: number;
  totalProducts: number;
  totalUsers: number;
}

/**
 * Lista de usuarios a cargar
 */
export type BatchIngestionRequestUsers = EndUserCreate[] | null;

/**
 * Lista de productos a cargar
 */
export type BatchIngestionRequestProducts = ProductCreate[] | null;

export type BatchIngestionRequestInteractionsAnyOfItem =
  | InteractionCreate
  | InteractionExternalCreate;

/**
 * Lista de interacciones a cargar (pueden usar IDs internos o externos)
 */
export type BatchIngestionRequestInteractions =
  | BatchIngestionRequestInteractionsAnyOfItem[]
  | null;

/**
 * Esquema para la carga masiva de datos
 */
export interface BatchIngestionRequest {
  /** Lista de interacciones a cargar (pueden usar IDs internos o externos) */
  interactions?: BatchIngestionRequestInteractions;
  /** Lista de productos a cargar */
  products?: BatchIngestionRequestProducts;
  /** Lista de usuarios a cargar */
  users?: BatchIngestionRequestUsers;
}

export type BatchIngestionJobStatusTaskId = string | null;

export type BatchIngestionJobStatusSuccessRate = number | null;

export type BatchIngestionJobStatusStartedAt = string | null;

export type BatchIngestionJobStatusProgressPercentage = number | null;

export type BatchIngestionJobStatusProcessedCountAnyOf = {
  [key: string]: unknown;
};

export type BatchIngestionJobStatusProcessedCount =
  BatchIngestionJobStatusProcessedCountAnyOf | null;

export type BatchIngestionJobStatusParametersAnyOf = { [key: string]: unknown };

export type BatchIngestionJobStatusParameters =
  BatchIngestionJobStatusParametersAnyOf | null;

export type BatchIngestionJobStatusEstimatedRemainingTime = number | null;

export type BatchIngestionJobStatusErrorMessage = string | null;

/**
 * Esquema para consultar el estado de un trabajo de ingesta masiva.
 */
export interface BatchIngestionJobStatus {
  completedAt?: BatchIngestionJobStatusCompletedAt;
  createdAt: string;
  errorCount?: BatchIngestionJobStatusErrorCount;
  errorDetails?: BatchIngestionJobStatusErrorDetails;
  errorMessage?: BatchIngestionJobStatusErrorMessage;
  estimatedRemainingTime?: BatchIngestionJobStatusEstimatedRemainingTime;
  jobId: number;
  parameters?: BatchIngestionJobStatusParameters;
  processedCount?: BatchIngestionJobStatusProcessedCount;
  progressPercentage?: BatchIngestionJobStatusProgressPercentage;
  startedAt?: BatchIngestionJobStatusStartedAt;
  status: string;
  successRate?: BatchIngestionJobStatusSuccessRate;
  taskId?: BatchIngestionJobStatusTaskId;
}

export type BatchIngestionJobStatusErrorDetailsAnyOf = {
  [key: string]: unknown;
};

export type BatchIngestionJobStatusErrorDetails =
  BatchIngestionJobStatusErrorDetailsAnyOf | null;

export type BatchIngestionJobStatusErrorCount = number | null;

export type BatchIngestionJobStatusCompletedAt = string | null;

export type AuditLogSystemUserId = number | null;

export type AuditLogEntityId = string | number;

export type AuditLogDetails = string | null;

export type AuditLogChangesAnyOf = { [key: string]: unknown };

export type AuditLogChanges = AuditLogChangesAnyOf | null;

export interface AuditLog {
  account_id: number;
  /** @maxLength 50 */
  action: string;
  changes?: AuditLogChanges;
  created_at: string;
  details?: AuditLogDetails;
  entity_id: AuditLogEntityId;
  /** @maxLength 50 */
  entity_type: string;
  id: number;
  performed_by: string;
  system_user_id?: AuditLogSystemUserId;
}

/**
 * Nuevo nombre descriptivo para esta API Key
 */
export type ApiKeyUpdateName = string | null;

/**
 * Schema for updating an API Key
 */
export interface ApiKeyUpdate {
  /** Nuevo nombre descriptivo para esta API Key */
  name?: ApiKeyUpdateName;
}

/**
 * Prefijo de la API Key
 */
export type ApiKeyResponsePrefix = string | null;

/**
 * Nombre descriptivo para esta API Key
 */
export type ApiKeyResponseName = string | null;

/**
 * Último uso de la API Key
 */
export type ApiKeyResponseLastUsed = string | null;

/**
 * Últimos caracteres de la API Key
 */
export type ApiKeyResponseLastChars = string | null;

/**
 * Fecha de creación
 */
export type ApiKeyResponseCreatedAt = string | null;

/**
 * Schema for API Key information (no sensitive data)
 */
export interface ApiKeyResponse {
  /** Fecha de creación */
  created_at?: ApiKeyResponseCreatedAt;
  /** ID único de la API Key */
  id: number;
  /** Estado de la API Key */
  is_active?: boolean;
  /** Últimos caracteres de la API Key */
  last_chars?: ApiKeyResponseLastChars;
  /** Último uso de la API Key */
  last_used?: ApiKeyResponseLastUsed;
  /** Nombre descriptivo para esta API Key */
  name?: ApiKeyResponseName;
  /** Prefijo de la API Key */
  prefix?: ApiKeyResponsePrefix;
}

/**
 * Schema for listing API Keys
 */
export interface ApiKeyListResponse {
  /** Lista de API Keys */
  api_keys: ApiKeyResponse[];
  /** Número total de API Keys */
  total: number;
}

/**
 * Nombre descriptivo para esta API Key
 */
export type ApiKeyCreateName = string | null;

/**
 * Schema for creating a new API Key
 */
export interface ApiKeyCreate {
  /** Nombre descriptivo para esta API Key */
  name?: ApiKeyCreateName;
}

/**
 * Date when usage resets
 */
export type ApiCallsUsageResetDate = string | null;

/**
 * API calls usage information
 */
export interface ApiCallsUsage {
  /** API calls limit */
  limit?: number;
  /** Percentage of limit used */
  percentage?: number;
  /** Date when usage resets */
  resetDate?: ApiCallsUsageResetDate;
  /** Number of API calls used */
  used?: number;
}

export type AccountUpdateOnboardingChecklistStatusAnyOf = {
  [key: string]: unknown;
};

export type AccountUpdateOnboardingChecklistStatus =
  AccountUpdateOnboardingChecklistStatusAnyOf | null;

export type AccountUpdateName = string | null;

export type AccountUpdateMercadopagoCustomerId = string | null;

export type AccountUpdateIsActive = boolean | null;

export interface AccountUpdate {
  isActive?: AccountUpdateIsActive;
  mercadopagoCustomerId?: AccountUpdateMercadopagoCustomerId;
  name?: AccountUpdateName;
  onboardingChecklistStatus?: AccountUpdateOnboardingChecklistStatus;
}

export type AccountResponseSubscription = SubscriptionBasicInfo | null;

export type AccountResponseOnboardingChecklistStatusAnyOf = {
  [key: string]: unknown;
};

export type AccountResponseOnboardingChecklistStatus =
  AccountResponseOnboardingChecklistStatusAnyOf | null;

export type AccountResponseMercadopagoCustomerId = string | null;

export type AccountResponseDeletedAt = string | null;

export interface AccountResponse {
  accountId: number;
  createdAt: string;
  deletedAt?: AccountResponseDeletedAt;
  isActive: boolean;
  mercadopagoCustomerId?: AccountResponseMercadopagoCustomerId;
  name: string;
  onboardingChecklistStatus?: AccountResponseOnboardingChecklistStatus;
  subscription?: AccountResponseSubscription;
  updatedAt: string;
}

export type AccountCreateMercadopagoCustomerId = string | null;

export interface AccountCreate {
  isActive?: boolean;
  mercadopagoCustomerId?: AccountCreateMercadopagoCustomerId;
  /** @minLength 1 */
  name: string;
}

export const getRayuela = () => {
  /**
   * @summary Health Check
   */
  const healthCheckHealthGet = <TData = AxiosResponse<unknown>>(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/health`, options);
  };

  /**
 * Endpoint para verificar el estado de la API.
No requiere autenticación.
 * @summary Health Check
 */
  const healthCheckApiV1HealthGet = <
    TData = AxiosResponse<HealthCheckApiV1HealthGet200>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/health`, options);
  };

  /**
 * Endpoint para verificar la conexión a la base de datos.
No requiere autenticación.
 * @summary Db Health Check
 */
  const dbHealthCheckApiV1HealthDbGet = <
    TData = AxiosResponse<DbHealthCheckApiV1HealthDbGet200>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/health/db`, options);
  };

  /**
 * Endpoint para verificar la autenticación.
Requiere autenticación con API Key.
 * @summary Auth Health Check
 */
  const authHealthCheckApiV1HealthAuthGet = <
    TData = AxiosResponse<AuthHealthCheckApiV1HealthAuthGet200>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/health/auth`, options);
  };

  /**
   * Envía un email de verificación al usuario actual.
   * @summary Send Verification Email
   */
  const sendVerificationEmailApiV1AuthSendVerificationEmailPost = <
    TData = AxiosResponse<unknown>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(
      `/api/v1/auth/send-verification-email`,
      undefined,
      options,
    );
  };

  /**
   * Verifica el email de un usuario usando un token.
   * @summary Verify Email
   */
  const verifyEmailApiV1AuthVerifyEmailGet = <TData = AxiosResponse<unknown>>(
    params: VerifyEmailApiV1AuthVerifyEmailGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/auth/verify-email`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
   * Creates a new account with global email uniqueness validation and returns a JWT token and the first API Key
   * @summary Register a new account
   */
  const registerApiV1AuthRegisterPost = <
    TData = AxiosResponse<RegisterResponse>,
  >(
    registerRequest: RegisterRequest,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(`/api/v1/auth/register`, registerRequest, options);
  };

  /**
   * Authenticates a user and returns a JWT token for dashboard access. Accepts JSON payload with email and password.
   * @summary Login to obtain JWT token
   */
  const loginApiV1AuthTokenPost = <TData = AxiosResponse<LoginResponse>>(
    loginRequest: LoginRequest,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(`/api/v1/auth/token`, loginRequest, options);
  };

  /**
   * Revoca el token JWT actual.
   * @summary Logout
   */
  const logoutApiV1AuthLogoutPost = <TData = AxiosResponse<unknown>>(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(`/api/v1/auth/logout`, undefined, options);
  };

  /**
   * List all accounts.
   * @summary List Accounts
   */
  const listAccountsApiV1AccountsGet = <
    TData = AxiosResponse<AccountResponse[]>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/accounts/`, options);
  };

  /**
 * Crea una nueva cuenta.

Utiliza la columna Identity para generar automáticamente el ID de la cuenta.
 * @summary Create Account
 */
  const createAccountApiV1AccountsAccountsPost = <
    TData = AxiosResponse<AccountResponse>,
  >(
    accountCreate: AccountCreate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(`/api/v1/accounts/accounts`, accountCreate, options);
  };

  /**
   * Get account by ID.
   * @summary Get Account
   */
  const getAccountApiV1AccountsAccountIdGet = <
    TData = AxiosResponse<AccountResponse>,
  >(
    accountId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/accounts/${accountId}`, options);
  };

  /**
   * @summary Deactivate Account
   */
  const deactivateAccountApiV1AccountsAccountIdDeactivatePatch = <
    TData = AxiosResponse<void>,
  >(
    accountId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.patch(
      `/api/v1/accounts/${accountId}/deactivate`,
      undefined,
      options,
    );
  };

  /**
   * Activate an account.
   * @summary Activate Account
   */
  const activateAccountApiV1AccountsAccountIdActivatePatch = <
    TData = AxiosResponse<AccountResponse>,
  >(
    accountId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.patch(
      `/api/v1/accounts/${accountId}/activate`,
      undefined,
      options,
    );
  };

  /**
   * Get information about the current account.
   * @summary Get Account Info
   */
  const getAccountInfoApiV1AccountsCurrentGet = <
    TData = AxiosResponse<AccountResponse>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/accounts/current`, options);
  };

  /**
 * Update the current account information.

This endpoint allows administrators to update their own account details,
such as the account name.
 * @summary Update Current Account
 */
  const updateCurrentAccountApiV1AccountsCurrentPut = <
    TData = AxiosResponse<AccountResponse>,
  >(
    accountUpdate: AccountUpdate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.put(`/api/v1/accounts/current`, accountUpdate, options);
  };

  /**
 * Partially update the current account information.

This endpoint allows administrators to update specific fields of their own account,
such as the account name, without having to provide all fields.
 * @summary Patch Current Account
 */
  const patchCurrentAccountApiV1AccountsCurrentPatch = <
    TData = AxiosResponse<AccountResponse>,
  >(
    accountUpdate: AccountUpdate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.patch(`/api/v1/accounts/current`, accountUpdate, options);
  };

  /**
   * Get audit logs with optional filters for a specific account.
   * @summary Get Audit Logs
   */
  const getAuditLogsApiV1AccountsAccountIdAuditLogsGet = <
    TData = AxiosResponse<AuditLog[]>,
  >(
    accountId: number,
    params?: GetAuditLogsApiV1AccountsAccountIdAuditLogsGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/accounts/${accountId}/audit-logs`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
   * Get API usage statistics for the current account.
   * @summary Get Api Usage
   */
  const getApiUsageApiV1AccountsUsageGet = <TData = AxiosResponse<UsageStats>>(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/accounts/usage`, options);
  };

  /**
 * Get all available subscription plans with their details.

Returns:
    Dict with plan information for all available plans.
 * @summary Get Available Plans
 */
  const getAvailablePlansApiV1PlansGet = <
    TData = AxiosResponse<GetAvailablePlansApiV1PlansGet200>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/plans/`, options);
  };

  /**
 * Get information about the current authenticated user.

This endpoint only requires a valid JWT token, no API Key is needed.
It's useful for the initial login flow when the user hasn't confirmed
the API Key yet.
 * @summary Get Current User Info
 */
  const getCurrentUserInfoApiV1SystemUsersMeGet = <
    TData = AxiosResponse<SystemUserResponse>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/system-users/me`, options);
  };

  /**
   * Update current user's information.
   * @summary Update User Me
   */
  const updateUserMeApiV1SystemUsersMePut = <
    TData = AxiosResponse<SystemUserResponse>,
  >(
    systemUserUpdate: SystemUserUpdate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.put(`/api/v1/system-users/me`, systemUserUpdate, options);
  };

  /**
   * Delete current user.
   * @summary Delete User Me
   */
  const deleteUserMeApiV1SystemUsersMeDelete = <TData = AxiosResponse<void>>(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.delete(`/api/v1/system-users/me`, options);
  };

  /**
   * Create a new system user.
   * @summary Create System User
   */
  const createSystemUserApiV1SystemUsersPost = <
    TData = AxiosResponse<SystemUserResponse>,
  >(
    systemUserCreate: SystemUserCreate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(`/api/v1/system-users/`, systemUserCreate, options);
  };

  /**
   * Get a system user by ID.
   * @summary Get System User
   */
  const getSystemUserApiV1SystemUsersUserIdGet = <
    TData = AxiosResponse<SystemUserResponse>,
  >(
    userId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/system-users/${userId}`, options);
  };

  /**
   * Create a new role.
   * @summary Create Role
   */
  const createRoleApiV1SystemUsersRolesPost = <TData = AxiosResponse<Role>>(
    roleCreate: RoleCreate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(`/api/v1/system-users/roles/`, roleCreate, options);
  };

  /**
   * Assign a role to a system user.
   * @summary Assign Role
   */
  const assignRoleApiV1SystemUsersUserIdRolesRoleIdPost = <
    TData = AxiosResponse<unknown>,
  >(
    userId: number,
    roleId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(
      `/api/v1/system-users/${userId}/roles/${roleId}`,
      undefined,
      options,
    );
  };

  /**
   * Remove a role from a system user.
   * @summary Remove Role
   */
  const removeRoleApiV1SystemUsersUserIdRolesRoleIdDelete = <
    TData = AxiosResponse<void>,
  >(
    userId: number,
    roleId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.delete(
      `/api/v1/system-users/${userId}/roles/${roleId}`,
      options,
    );
  };

  /**
   * Get all roles assigned to a system user.
   * @summary Get User Roles
   */
  const getUserRolesApiV1SystemUsersUserIdRolesGet = <
    TData = AxiosResponse<Role[]>,
  >(
    userId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/system-users/${userId}/roles`, options);
  };

  /**
   * Get all permissions assigned to a system user through their roles.
   * @summary Get User Permissions
   */
  const getUserPermissionsApiV1SystemUsersUserIdPermissionsGet = <
    TData = AxiosResponse<string[]>,
  >(
    userId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/system-users/${userId}/permissions`, options);
  };

  /**
   * @summary Create End User
   */
  const createEndUserApiV1EndUsersPost = <TData = AxiosResponse<EndUser>>(
    endUserCreate: EndUserCreate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(`/api/v1/end-users/`, endUserCreate, options);
  };

  /**
   * @summary Read End Users
   */
  const readEndUsersApiV1EndUsersGet = <
    TData = AxiosResponse<PaginatedResponseEndUser>,
  >(
    params?: ReadEndUsersApiV1EndUsersGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/end-users/`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
   * Get an end user by ID.
   * @summary Read End User
   */
  const readEndUserApiV1EndUsersUserIdGet = <TData = AxiosResponse<EndUser>>(
    userId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/end-users/${userId}`, options);
  };

  /**
   * @summary Create Product
   */
  const createProductApiV1ProductsPost = <TData = AxiosResponse<Product>>(
    productCreate: ProductCreate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(`/api/v1/products/`, productCreate, options);
  };

  /**
   * Obtener productos paginados para la cuenta actual
   * @summary Read Products
   */
  const readProductsApiV1ProductsGet = <
    TData = AxiosResponse<PaginatedResponseProduct>,
  >(
    params?: ReadProductsApiV1ProductsGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/products/`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
   * Get a product by ID.
   * @summary Get Product
   */
  const getProductApiV1ProductsProductIdGet = <TData = AxiosResponse<Product>>(
    productId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/products/${productId}`, options);
  };

  /**
   * @summary Update Product
   */
  const updateProductApiV1ProductsProductIdPut = <
    TData = AxiosResponse<Product>,
  >(
    productId: number,
    productUpdate: ProductUpdate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.put(`/api/v1/products/${productId}`, productUpdate, options);
  };

  /**
   * Update product inventory.
   * @summary Update Inventory
   */
  const updateInventoryApiV1ProductsProductIdInventoryPatch = <
    TData = AxiosResponse<Product>,
  >(
    productId: number,
    inventoryUpdate: InventoryUpdate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.patch(
      `/api/v1/products/${productId}/inventory`,
      inventoryUpdate,
      options,
    );
  };

  /**
   * Get a product by its external_id.
   * @summary Get Product By External Id
   */
  const getProductByExternalIdApiV1ProductsExternalExternalIdGet = <
    TData = AxiosResponse<Product>,
  >(
    externalId: string,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/products/external/${externalId}`, options);
  };

  /**
   * Update a product by its external_id.
   * @summary Update Product By External Id
   */
  const updateProductByExternalIdApiV1ProductsExternalExternalIdPut = <
    TData = AxiosResponse<Product>,
  >(
    externalId: string,
    productUpdate: ProductUpdate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.put(
      `/api/v1/products/external/${externalId}`,
      productUpdate,
      options,
    );
  };

  /**
   * Delete (soft delete) a product by its external_id.
   * @summary Delete Product By External Id
   */
  const deleteProductByExternalIdApiV1ProductsExternalExternalIdDelete = <
    TData = AxiosResponse<void>,
  >(
    externalId: string,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.delete(`/api/v1/products/external/${externalId}`, options);
  };

  /**
   * Update product inventory by external_id.
   * @summary Update Inventory By External Id
   */
  const updateInventoryByExternalIdApiV1ProductsExternalExternalIdInventoryPatch =
    <TData = AxiosResponse<Product>>(
      externalId: string,
      inventoryUpdate: InventoryUpdate,
      options?: AxiosRequestConfig,
    ): Promise<TData> => {
      return axios.patch(
        `/api/v1/products/external/${externalId}/inventory`,
        inventoryUpdate,
        options,
      );
    };

  /**
   * @summary Get Most Searched
   */
  const getMostSearchedApiV1RecommendationsMostSearchedGet = <
    TData = AxiosResponse<PaginatedResponseProduct>,
  >(
    params?: GetMostSearchedApiV1RecommendationsMostSearchedGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/recommendations/most-searched/`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
   * @summary Get Trending Searches
   */
  const getTrendingSearchesApiV1RecommendationsTrendingSearchesGet = <
    TData = AxiosResponse<PaginatedResponseSearch>,
  >(
    params?: GetTrendingSearchesApiV1RecommendationsTrendingSearchesGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/recommendations/trending-searches/`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
   * @summary Get Popular Trends
   */
  const getPopularTrendsApiV1RecommendationsPopularTrendsGet = <
    TData = AxiosResponse<PaginatedResponseProduct>,
  >(
    params?: GetPopularTrendsApiV1RecommendationsPopularTrendsGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/recommendations/popular-trends/`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
   * @summary Get Related Searches
   */
  const getRelatedSearchesApiV1RecommendationsRelatedSearchesProductIdGet = <
    TData = AxiosResponse<PaginatedResponseSearch>,
  >(
    productId: number,
    params?: GetRelatedSearchesApiV1RecommendationsRelatedSearchesProductIdGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/recommendations/related-searches/${productId}`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
   * @summary Get Most Sold
   */
  const getMostSoldApiV1RecommendationsMostSoldGet = <
    TData = AxiosResponse<PaginatedResponseProduct>,
  >(
    params?: GetMostSoldApiV1RecommendationsMostSoldGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/recommendations/most-sold/`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
   * @summary Get Top Rated
   */
  const getTopRatedApiV1RecommendationsTopRatedGet = <
    TData = AxiosResponse<PaginatedResponseProduct>,
  >(
    params?: GetTopRatedApiV1RecommendationsTopRatedGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/recommendations/top-rated/`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
   * @summary Get Category Products
   */
  const getCategoryProductsApiV1RecommendationsCategoryCategoryGet = <
    TData = AxiosResponse<PaginatedResponseProduct>,
  >(
    category: string,
    params?: GetCategoryProductsApiV1RecommendationsCategoryCategoryGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/recommendations/category/${category}`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
   * @summary Get Related Categories
   */
  const getRelatedCategoriesApiV1RecommendationsRelatedCategoriesCategoryGet = <
    TData = AxiosResponse<PaginatedResponseCategory>,
  >(
    category: string,
    params?: GetRelatedCategoriesApiV1RecommendationsRelatedCategoriesCategoryGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/recommendations/related-categories/${category}`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
   * @summary Get Also Bought
   */
  const getAlsoBoughtApiV1RecommendationsAlsoBoughtProductIdGet = <
    TData = AxiosResponse<PaginatedResponseProduct>,
  >(
    productId: number,
    params?: GetAlsoBoughtApiV1RecommendationsAlsoBoughtProductIdGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/recommendations/also-bought/${productId}`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
 * Obtiene productos similares al producto especificado basados en contenido.

Utiliza vectores de características de productos para calcular similitud coseno
y encontrar productos con características similares.

Parámetros:
- product_id: ID del producto para el que se buscan similares
- limit: Número máximo de productos similares a devolver
- include_explanation: Si es True, incluye explicaciones de por qué los productos son similares
- explanation_level: Nivel de detalle de la explicación (simple o detailed)

Retorna:
- Lista paginada de productos similares ordenados por similitud
- Cada producto incluye un score de similitud
- Si se solicita, incluye explicaciones de la similitud
 * @summary Get Similar Products
 */
  const getSimilarProductsApiV1RecommendationsProductsProductIdSimilarGet = <
    TData = AxiosResponse<PaginatedResponseProduct>,
  >(
    productId: number,
    params?: GetSimilarProductsApiV1RecommendationsProductsProductIdSimilarGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/recommendations/products/${productId}/similar`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
 * Invalida la caché de recomendaciones para un usuario específico.

Args:
    user_id: ID del usuario
    account: Cuenta actual
    db: Sesión de base de datos

Returns:
    Mensaje de confirmación
 * @summary Invalidate User Cache
 */
  const invalidateUserCacheApiV1RecommendationsInvalidateCacheUserIdPost = <
    TData = AxiosResponse<unknown>,
  >(
    userId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(
      `/api/v1/recommendations/invalidate-cache/${userId}`,
      undefined,
      options,
    );
  };

  /**
   * Invalida toda la caché de recomendaciones para una cuenta.
   * @summary Invalidate Account Cache
   */
  const invalidateAccountCacheApiV1RecommendationsInvalidateCachePost = <
    TData = AxiosResponse<unknown>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(
      `/api/v1/recommendations/invalidate-cache`,
      undefined,
      options,
    );
  };

  /**
   * Main endpoint for obtaining personalized recommendations with complex filters and structured context.
   * @summary Get personalized recommendations
   */
  const queryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPost =
    <TData = AxiosResponse<PaginatedResponseProduct>>(
      queryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPostBody: QueryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPostBody,
      options?: AxiosRequestConfig,
    ): Promise<TData> => {
      return axios.post(
        `/api/v1/recommendations/personalized/query`,
        queryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPostBody,
        options,
      );
    };

  /**
 * Obtiene una explicación detallada de por qué un producto específico se recomienda a un usuario.

Esta explicación incluye:
- Razón principal de la recomendación
- Razones secundarias
- Nivel de confianza
- Evidencia que respalda la recomendación (productos similares, categorías afines, etc.)
- Explicación en texto plano

Args:
    user_id: ID del usuario
    item_id: ID del producto
    account: Información de la cuenta autenticada
    db: Sesión de base de datos
    limit_service: Servicio de límites

Returns:
    Explicación detallada de la recomendación
 * @summary Get Recommendation Explanation
 */
  const getRecommendationExplanationApiV1RecommendationsExplainUserIdItemIdGet =
    <TData = AxiosResponse<DetailedExplanation>>(
      userId: number,
      itemId: number,
      options?: AxiosRequestConfig,
    ): Promise<TData> => {
      return axios.get(
        `/api/v1/recommendations/explain/${userId}/${itemId}`,
        options,
      );
    };

  /**
   * Get explanation using external IDs by resolving to internal IDs.
   * @summary Get explanation using external IDs
   */
  const getRecommendationExplanationExternalApiV1RecommendationsExplainExternalExternalUserIdExternalItemIdGet =
    <TData = AxiosResponse<DetailedExplanation>>(
      externalUserId: string,
      externalItemId: string,
      options?: AxiosRequestConfig,
    ): Promise<TData> => {
      return axios.get(
        `/api/v1/recommendations/explain/external/${externalUserId}/${externalItemId}`,
        options,
      );
    };

  /**
   * @summary Get similar products using external ID
   */
  const getSimilarProductsExternalApiV1RecommendationsProductsExternalExternalProductIdSimilarGet =
    <TData = AxiosResponse<PaginatedResponseProduct>>(
      externalProductId: string,
      params?: GetSimilarProductsExternalApiV1RecommendationsProductsExternalExternalProductIdSimilarGetParams,
      options?: AxiosRequestConfig,
    ): Promise<TData> => {
      return axios.get(
        `/api/v1/recommendations/products/external/${externalProductId}/similar`,
        {
          ...options,
          params: { ...params, ...options?.params },
        },
      );
    };

  /**
   * @summary Get also-bought products using external ID
   */
  const getAlsoBoughtExternalApiV1RecommendationsAlsoBoughtExternalExternalProductIdGet =
    <TData = AxiosResponse<PaginatedResponseProduct>>(
      externalProductId: string,
      params?: GetAlsoBoughtExternalApiV1RecommendationsAlsoBoughtExternalExternalProductIdGetParams,
      options?: AxiosRequestConfig,
    ): Promise<TData> => {
      return axios.get(
        `/api/v1/recommendations/also-bought/external/${externalProductId}`,
        {
          ...options,
          params: { ...params, ...options?.params },
        },
      );
    };

  /**
 * Obtiene métricas de confianza para las recomendaciones.

Estas métricas incluyen:
- Distribución de scores de confianza por tipo de modelo (colaborativo, contenido, híbrido)
- Confianza promedio por categoría de producto
- Factores que influyen en la confianza
- Tendencias de confianza a lo largo del tiempo

Returns:
    Diccionario con métricas de confianza
 * @summary Get Confidence Metrics
 */
  const getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet = <
    TData = AxiosResponse<GetConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet200>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/recommendations/confidence-metrics`, options);
  };

  /**
   * @summary Rollback Model
   */
  const rollbackModelApiV1RecommendationsRollbackArtifactVersionPost = <
    TData = AxiosResponse<unknown>,
  >(
    artifactVersion: string,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(
      `/api/v1/recommendations/rollback/${artifactVersion}`,
      undefined,
      options,
    );
  };

  /**
 * Get performance metrics for recommendation models.

Returns metrics such as:
- Precision/Recall/NDCG/MAP: Accuracy of recommendations
- Coverage: Percentage of catalog being recommended
- Diversity: Variety in recommendations
- Novelty: Measure of how unpopular/unknown the recommended items are (based on inverse popularity)
- Serendipity: Measure of how surprising but relevant the recommendations are
- CTR (Click-Through Rate): Percentage of recommended items that receive clicks
- CVR (Conversion Rate): Percentage of recommended items that result in conversions
- Training time: Time taken to train models
- Inference time: Time taken to generate recommendations
- System metrics: CPU/Memory usage, latency percentiles

If account_id is provided and the user has admin privileges, returns data for that account.
Otherwise, returns data for the current account.
 * @summary Get Recommendation Performance
 */
  const getRecommendationPerformanceApiV1RecommendationsPerformanceGet = <
    TData = AxiosResponse<RecommendationPerformanceResponse>,
  >(
    params?: GetRecommendationPerformanceApiV1RecommendationsPerformanceGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/recommendations/performance`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
   * Create a new interaction.
   * @summary Create Interaction
   */
  const createInteractionApiV1InteractionsPost = <
    TData = AxiosResponse<Interaction>,
  >(
    interactionCreate: InteractionCreate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(`/api/v1/interactions/`, interactionCreate, options);
  };

  /**
   * Get all interactions for the current account.
   * @summary Read Interactions
   */
  const readInteractionsApiV1InteractionsGet = <
    TData = AxiosResponse<PaginatedResponseInteraction>,
  >(
    params?: ReadInteractionsApiV1InteractionsGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/interactions/`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
   * Crear una nueva interacción usando external_user_id y external_product_id.
   * @summary Create Interaction External
   */
  const createInteractionExternalApiV1InteractionsExternalPost = <
    TData = AxiosResponse<Interaction>,
  >(
    interactionExternalCreate: InteractionExternalCreate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(
      `/api/v1/interactions/external`,
      interactionExternalCreate,
      options,
    );
  };

  /**
 * Inicia el entrenamiento de modelos usando Celery.

Verifica los límites de API y la frecuencia de entrenamiento permitida según el plan de suscripción
antes de crear el trabajo de entrenamiento y encolar la tarea.

Returns:
    TrainingResponse: Respuesta con el ID del trabajo y la tarea iniciada.
    Status: 202 Accepted - El trabajo ha sido aceptado y está siendo procesado.
 * @summary Train Models
 */
  const trainModelsApiV1PipelineTrainPost = <
    TData = AxiosResponse<TrainingResponse>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(`/api/v1/pipeline/train`, undefined, options);
  };

  /**
 * Obtiene el estado del último entrenamiento completado.

Este endpoint devuelve las métricas del último modelo entrenado exitosamente,
no el estado de un trabajo en curso. Para consultar el estado de un trabajo
específico, use el endpoint /pipeline/jobs/{job_id}/status.
 * @summary Get Training Status
 */
  const getTrainingStatusApiV1PipelineStatusGet = <
    TData = AxiosResponse<TrainingStatus>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/pipeline/status`, options);
  };

  /**
 * Consulta el estado de un trabajo de entrenamiento específico.

Args:
    job_id: ID del trabajo de entrenamiento
    account: Cuenta actual
    db: Sesión de base de datos

Returns:
    Estado detallado del trabajo de entrenamiento
 * @summary Get Training Job Status
 */
  const getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet = <
    TData = AxiosResponse<TrainingJobStatus>,
  >(
    jobId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/pipeline/jobs/${jobId}/status`, options);
  };

  /**
   * Lista todos los modelos entrenados para la cuenta.
   * @summary List Models
   */
  const listModelsApiV1PipelineModelsGet = <
    TData = AxiosResponse<ModelMetadataResponse[]>,
  >(
    params?: ListModelsApiV1PipelineModelsGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/pipeline/models`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
   * Obtiene las métricas de un modelo específico.
   * @summary Get Model Metrics
   */
  const getModelMetricsApiV1PipelineModelsModelIdMetricsGet = <
    TData = AxiosResponse<TrainingStatus>,
  >(
    modelId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/pipeline/models/${modelId}/metrics`, options);
  };

  /**
   * Invalida la caché de modelos y métricas.
   * @summary Invalidate Cache
   */
  const invalidateCacheApiV1PipelineInvalidateCachePost = <
    TData = AxiosResponse<unknown>,
  >(
    params?: InvalidateCacheApiV1PipelineInvalidateCachePostParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(`/api/v1/pipeline/invalidate-cache`, undefined, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
 * Inicia un entrenamiento específico para la cuenta `account_id`.
Solo para administradores del sistema.

Returns:
    Dict[str, Any]: Respuesta con el ID del trabajo y la tarea iniciada.
    Status: 202 Accepted - El trabajo ha sido aceptado y está siendo procesado.
 * @summary Train Artifact For Account
 */
  const trainArtifactForAccountApiV1PipelineTrainAccountIdPost = <
    TData = AxiosResponse<TrainArtifactForAccountApiV1PipelineTrainAccountIdPost202>,
  >(
    accountId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(
      `/api/v1/pipeline/train/${accountId}`,
      undefined,
      options,
    );
  };

  /**
 * Endpoint para procesar trabajos de entrenamiento manualmente.
Este endpoint es para uso administrativo o debugging.
 * @summary Process Training Job
 */
  const processTrainingJobApiV1PipelineProcessPost = <
    TData = AxiosResponse<ProcessTrainingJobApiV1PipelineProcessPost200>,
  >(
    processTrainingJobApiV1PipelineProcessPostBody: ProcessTrainingJobApiV1PipelineProcessPostBody,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(
      `/api/v1/pipeline/process`,
      processTrainingJobApiV1PipelineProcessPostBody,
      options,
    );
  };

  /**
 * Endpoint de callback para notificaciones de finalización de entrenamiento.
Este endpoint puede ser llamado por Celery o por sistemas externos.
 * @summary Training Callback
 */
  const trainingCallbackApiV1PipelineCallbackJobIdPost = <
    TData = AxiosResponse<TrainingCallbackApiV1PipelineCallbackJobIdPost200>,
  >(
    jobId: number,
    trainingCallbackApiV1PipelineCallbackJobIdPostBody: TrainingCallbackApiV1PipelineCallbackJobIdPostBody,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(
      `/api/v1/pipeline/callback/${jobId}`,
      trainingCallbackApiV1PipelineCallbackJobIdPostBody,
      options,
    );
  };

  /**
 * Lista los trabajos de entrenamiento recientes para la cuenta actual.

Este endpoint permite obtener una lista de trabajos de entrenamiento,
ordenados por fecha de creación (más recientes primero).

Args:
    limit: Número máximo de trabajos a devolver (máximo 100)
    status: Filtrar por estado del trabajo (opcional)
    account: Cuenta actual
    db: Sesión de base de datos

Returns:
    Lista de trabajos de entrenamiento
 * @summary List Training Jobs
 */
  const listTrainingJobsApiV1PipelineJobsGet = <
    TData = AxiosResponse<TrainingJobStatus[]>,
  >(
    params?: ListTrainingJobsApiV1PipelineJobsGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/pipeline/jobs`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
   * @summary Test Cache
   */
  const testCacheApiV1CacheTestCacheGet = <TData = AxiosResponse<unknown>>(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/cache/test-cache`, options);
  };

  /**
   * @summary Check Redis
   */
  const checkRedisApiV1CacheRedisHealthGet = <TData = AxiosResponse<unknown>>(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/cache/redis-health`, options);
  };

  /**
 * Get analytics data for the current account.

If account_id is provided and the user has admin privileges, returns data for that account.
Otherwise, returns data for the current account.
 * @summary Get Account Analytics
 */
  const getAccountAnalyticsApiV1AnalyticsAnalyticsAccountGet = <
    TData = AxiosResponse<GetAccountAnalyticsApiV1AnalyticsAnalyticsAccountGet200>,
  >(
    params?: GetAccountAnalyticsApiV1AnalyticsAnalyticsAccountGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/analytics/analytics/account`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
 * Get analytics data for API endpoints.

If account_id is provided and the user has admin privileges, returns data for that account.
Otherwise, returns data for the current account.

Can be filtered by endpoint path and HTTP method.
 * @summary Get Endpoint Analytics
 */
  const getEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGet = <
    TData = AxiosResponse<
      GetEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGet200Item[]
    >,
  >(
    params?: GetEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/analytics/analytics/endpoints`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
 * Get performance metrics for recommendation models.

Returns metrics such as:
- Precision/Recall/NDCG/MAP: Accuracy of recommendations
- Coverage: Percentage of catalog being recommended
- Diversity: Variety in recommendations
- Novelty: Measure of how unpopular/unknown the recommended items are (based on inverse popularity)
- Serendipity: Measure of how surprising but relevant the recommendations are
- CTR (Click-Through Rate): Percentage of recommended items that receive clicks
- CVR (Conversion Rate): Percentage of recommended items that result in conversions
- Training time: Time taken to train models
- Inference time: Time taken to generate recommendations
- System metrics: CPU/Memory usage, latency percentiles

If account_id is provided and the user has admin privileges, returns data for that account.
Otherwise, returns data for the current account.
 * @summary Get Recommendation Performance
 */
  const getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet =
    <TData = AxiosResponse<RecommendationPerformanceResponse>>(
      params?: GetRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGetParams,
      options?: AxiosRequestConfig,
    ): Promise<TData> => {
      return axios.get(
        `/api/v1/analytics/analytics/recommendation_performance`,
        {
          ...options,
          params: { ...params, ...options?.params },
        },
      );
    };

  /**
 * Compara métricas entre diferentes versiones de modelos.

Este endpoint permite comparar las métricas de rendimiento entre diferentes modelos
para analizar mejoras o regresiones en el tiempo.

Incluye métricas estándar de ML así como métricas proxy de negocio como:
- Tasa de conversión en el conjunto de prueba
- ROI estimado
- Engagement estimado
- Valor del cliente estimado

Si se proporcionan model_ids, compara específicamente esos modelos.
Si no, compara los últimos N modelos según el parámetro limit.

Returns:
    Diccionario con comparación detallada de métricas entre versiones de modelos
 * @summary Compare Model Versions
 */
  const compareModelVersionsApiV1AnalyticsAnalyticsModelsCompareGet = <
    TData = AxiosResponse<ModelComparisonResponse>,
  >(
    params?: CompareModelVersionsApiV1AnalyticsAnalyticsModelsCompareGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/analytics/analytics/models/compare`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
 * Obtiene el historial de valores para una métrica específica.

Este endpoint permite visualizar la evolución de una métrica a lo largo del tiempo
para diferentes versiones de modelos.

Útil para análisis de tendencias y para evaluar el impacto de cambios en el modelo.

Args:
    metric_name: Nombre de la métrica a consultar
    limit: Número máximo de puntos de datos a devolver
    account_id: ID opcional de la cuenta
    
Returns:
    Lista con valores históricos de la métrica
 * @summary Get Metrics History
 */
  const getMetricsHistoryApiV1AnalyticsAnalyticsMetricsHistoryGet = <
    TData = AxiosResponse<MetricsHistoryResponse>,
  >(
    params: GetMetricsHistoryApiV1AnalyticsAnalyticsMetricsHistoryGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/analytics/analytics/metrics/history`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
 * Crea una sesión de checkout para suscribirse o cambiar de plan.

Requiere autenticación con API Key y JWT.
 * @summary Create Checkout Session
 */
  const createCheckoutSessionApiV1BillingCreateCheckoutSessionPost = <
    TData = AxiosResponse<CreateCheckoutSessionResponse>,
  >(
    createCheckoutSessionRequest: CreateCheckoutSessionRequest,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(
      `/api/v1/billing/create-checkout-session`,
      createCheckoutSessionRequest,
      options,
    );
  };

  /**
 * Crea una sesión del portal de facturación para gestionar la suscripción.

Requiere autenticación con API Key y JWT.
 * @summary Create Portal Session
 */
  const createPortalSessionApiV1BillingCreatePortalSessionPost = <
    TData = AxiosResponse<CreatePortalSessionResponse>,
  >(
    createPortalSessionRequest: CreatePortalSessionRequest,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(
      `/api/v1/billing/create-portal-session`,
      createPortalSessionRequest,
      options,
    );
  };

  /**
 * Webhook para recibir eventos de Mercado Pago.

No requiere autenticación, pero verifica la firma de Mercado Pago.
 * @summary Mercadopago Webhook
 */
  const mercadopagoWebhookApiV1BillingWebhookMercadopagoPost = <
    TData = AxiosResponse<unknown>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(
      `/api/v1/billing/webhook/mercadopago`,
      undefined,
      options,
    );
  };

  /**
 * List all roles available for the current account.

Requires admin privileges.
 * @summary List Roles
 */
  const listRolesApiV1RolesGet = <TData = AxiosResponse<Role[]>>(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/roles/`, options);
  };

  /**
 * Create a new role.

Requires admin privileges.
 * @summary Create Role
 */
  const createRoleApiV1RolesPost = <TData = AxiosResponse<Role>>(
    roleCreate: RoleCreate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(`/api/v1/roles/`, roleCreate, options);
  };

  /**
 * Get a specific role by ID.

Requires admin privileges.
 * @summary Get Role
 */
  const getRoleApiV1RolesRoleIdGet = <TData = AxiosResponse<Role>>(
    roleId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/roles/${roleId}`, options);
  };

  /**
 * Update an existing role.

Requires admin privileges.
 * @summary Update Role
 */
  const updateRoleApiV1RolesRoleIdPut = <TData = AxiosResponse<Role>>(
    roleId: number,
    roleCreate: RoleCreate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.put(`/api/v1/roles/${roleId}`, roleCreate, options);
  };

  /**
 * Delete a role.

Requires admin privileges.
 * @summary Delete Role
 */
  const deleteRoleApiV1RolesRoleIdDelete = <TData = AxiosResponse<void>>(
    roleId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.delete(`/api/v1/roles/${roleId}`, options);
  };

  /**
 * Get all permissions assigned to a role.

Requires admin privileges.
 * @summary Get Role Permissions
 */
  const getRolePermissionsApiV1RolesRoleIdPermissionsGet = <
    TData = AxiosResponse<Permission[]>,
  >(
    roleId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/roles/${roleId}/permissions`, options);
  };

  /**
 * Assign a permission to a role.

Requires admin privileges.
 * @summary Assign Permission To Role
 */
  const assignPermissionToRoleApiV1RolesRoleIdPermissionsPermissionIdPost = <
    TData = AxiosResponse<unknown>,
  >(
    roleId: number,
    permissionId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(
      `/api/v1/roles/${roleId}/permissions/${permissionId}`,
      undefined,
      options,
    );
  };

  /**
 * Remove a permission from a role.

Requires admin privileges.
 * @summary Remove Permission From Role
 */
  const removePermissionFromRoleApiV1RolesRoleIdPermissionsPermissionIdDelete =
    <TData = AxiosResponse<void>>(
      roleId: number,
      permissionId: number,
      options?: AxiosRequestConfig,
    ): Promise<TData> => {
      return axios.delete(
        `/api/v1/roles/${roleId}/permissions/${permissionId}`,
        options,
      );
    };

  /**
 * List all permissions available for the current account.

Requires admin privileges.
 * @summary List Permissions
 */
  const listPermissionsApiV1PermissionsGet = <
    TData = AxiosResponse<Permission[]>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/permissions/`, options);
  };

  /**
 * Create a new permission.

Requires admin privileges.
 * @summary Create Permission
 */
  const createPermissionApiV1PermissionsPost = <
    TData = AxiosResponse<Permission>,
  >(
    permissionCreate: PermissionCreate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(`/api/v1/permissions/`, permissionCreate, options);
  };

  /**
 * Get a specific permission by ID.

Requires admin privileges.
 * @summary Get Permission
 */
  const getPermissionApiV1PermissionsPermissionIdGet = <
    TData = AxiosResponse<Permission>,
  >(
    permissionId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/permissions/${permissionId}`, options);
  };

  /**
 * Get all roles that have a specific permission.

Requires admin privileges.
 * @summary Get Roles With Permission
 */
  const getRolesWithPermissionApiV1PermissionsPermissionIdRolesGet = <
    TData = AxiosResponse<Role[]>,
  >(
    permissionId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/permissions/${permissionId}/roles`, options);
  };

  /**
 * Limpia logs de auditoría más antiguos que el período especificado.

Args:
    days_to_keep: Número de días a mantener los logs (por defecto 90)
    account_id: ID de la cuenta específica (None para todas las cuentas)
    run_async: Si es True, ejecuta la limpieza en segundo plano

Returns:
    Diccionario con información sobre la operación o el ID de la tarea
 * @summary Cleanup Audit Logs
 */
  const cleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPost = <
    TData = AxiosResponse<CleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPost200>,
  >(
    params?: CleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPostParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(
      `/api/v1/maintenance/maintenance/cleanup-audit-logs`,
      undefined,
      {
        ...options,
        params: { ...params, ...options?.params },
      },
    );
  };

  /**
 * Limpia interacciones más antiguas que el período especificado.

Args:
    days_to_keep: Número de días a mantener las interacciones (por defecto 180)
    account_id: ID de la cuenta específica (None para todas las cuentas)
    batch_size: Tamaño del lote para eliminación (por defecto 10000)
    run_async: Si es True, ejecuta la limpieza en segundo plano

Returns:
    Diccionario con información sobre la operación o el ID de la tarea
 * @summary Cleanup Interactions
 */
  const cleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPost =
    <
      TData = AxiosResponse<CleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPost200>,
    >(
      params?: CleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPostParams,
      options?: AxiosRequestConfig,
    ): Promise<TData> => {
      return axios.post(
        `/api/v1/maintenance/maintenance/cleanup-interactions`,
        undefined,
        {
          ...options,
          params: { ...params, ...options?.params },
        },
      );
    };

  /**
 * Obtiene el estado de una tarea de mantenimiento.

Args:
    task_id: ID de la tarea

Returns:
    Diccionario con información sobre la tarea
 * @summary Get Task Status
 */
  const getTaskStatusApiV1MaintenanceMaintenanceTaskTaskIdGet = <
    TData = AxiosResponse<GetTaskStatusApiV1MaintenanceMaintenanceTaskTaskIdGet200>,
  >(
    taskId: string,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/maintenance/maintenance/task/${taskId}`, options);
  };

  /**
 * Archiva y luego limpia logs de auditoría más antiguos que el período especificado.

Esta función primero exporta los datos a GCS y luego los elimina de Cloud SQL,
proporcionando una estrategia de retención de datos costo-efectiva.

Args:
    days_to_keep: Número de días a mantener los logs en Cloud SQL (por defecto 90)
    account_id: ID de la cuenta específica (None para todas las cuentas)
    batch_size: Tamaño del lote para procesamiento (por defecto 10000)
    run_async: Si es True, ejecuta el archivado en segundo plano

Returns:
    Diccionario con información sobre la operación o el ID de la tarea
 * @summary Archive And Cleanup Audit Logs
 */
  const archiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPost =
    <
      TData = AxiosResponse<ArchiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPost200>,
    >(
      params?: ArchiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPostParams,
      options?: AxiosRequestConfig,
    ): Promise<TData> => {
      return axios.post(
        `/api/v1/maintenance/maintenance/archive-and-cleanup-audit-logs`,
        undefined,
        {
          ...options,
          params: { ...params, ...options?.params },
        },
      );
    };

  /**
 * Archiva y luego limpia interacciones más antiguas que el período especificado.

Esta función primero exporta los datos a GCS y luego los elimina de Cloud SQL,
proporcionando una estrategia de retención de datos costo-efectiva.

Args:
    days_to_keep: Número de días a mantener las interacciones en Cloud SQL (por defecto 180)
    account_id: ID de la cuenta específica (None para todas las cuentas)
    batch_size: Tamaño del lote para procesamiento (por defecto 10000)
    run_async: Si es True, ejecuta el archivado en segundo plano

Returns:
    Diccionario con información sobre la operación o el ID de la tarea
 * @summary Archive And Cleanup Interactions
 */
  const archiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPost =
    <
      TData = AxiosResponse<ArchiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPost200>,
    >(
      params?: ArchiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPostParams,
      options?: AxiosRequestConfig,
    ): Promise<TData> => {
      return axios.post(
        `/api/v1/maintenance/maintenance/archive-and-cleanup-interactions`,
        undefined,
        {
          ...options,
          params: { ...params, ...options?.params },
        },
      );
    };

  /**
 * Lista archivos archivados para una tabla específica.

Args:
    table_name: Nombre de la tabla (audit_logs o interactions)
    start_date: Fecha de inicio para filtrar (formato ISO)
    end_date: Fecha de fin para filtrar (formato ISO)
    account_id: ID de cuenta para filtrar

Returns:
    Lista de archivos archivados con metadatos
 * @summary List Archived Files
 */
  const listArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGet =
    <
      TData = AxiosResponse<ListArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGet200>,
    >(
      tableName: string,
      params?: ListArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGetParams,
      options?: AxiosRequestConfig,
    ): Promise<TData> => {
      return axios.get(
        `/api/v1/maintenance/maintenance/archived-files/${tableName}`,
        {
          ...options,
          params: { ...params, ...options?.params },
        },
      );
    };

  /**
 * Limpia registros con soft delete que han excedido el período de retención final.

Esta función identifica y elimina permanentemente (o archiva y luego elimina)
registros que tienen is_active = FALSE y deleted_at anterior al umbral definido.

Args:
    retention_days: Número de días de retención después del soft delete (por defecto 365)
    account_id: ID de la cuenta específica (None para todas las cuentas)
    dry_run: Si es True, solo reporta qué se eliminaría sin hacer cambios
    run_async: Si es True, ejecuta la limpieza en segundo plano

Returns:
    Diccionario con información sobre la operación o el ID de la tarea
 * @summary Cleanup Soft Deleted Records
 */
  const cleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPost =
    <
      TData = AxiosResponse<CleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPost200>,
    >(
      params?: CleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPostParams,
      options?: AxiosRequestConfig,
    ): Promise<TData> => {
      return axios.post(
        `/api/v1/maintenance/maintenance/cleanup-soft-deleted-records`,
        undefined,
        {
          ...options,
          params: { ...params, ...options?.params },
        },
      );
    };

  /**
 * Obtiene estadísticas sobre registros con soft delete.

Args:
    account_id: ID de cuenta para filtrar (None para todas las cuentas)
    run_async: Si es True, ejecuta como tarea en segundo plano

Returns:
    Estadísticas por tabla o ID de tarea si run_async=True
 * @summary Get Soft Delete Statistics
 */
  const getSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGet =
    <
      TData = AxiosResponse<GetSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGet200>,
    >(
      params?: GetSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGetParams,
      options?: AxiosRequestConfig,
    ): Promise<TData> => {
      return axios.get(
        `/api/v1/maintenance/maintenance/soft-delete-statistics`,
        {
          ...options,
          params: { ...params, ...options?.params },
        },
      );
    };

  /**
 * Monitorea las tablas de alto volumen y devuelve estadísticas.

Args:
    run_async: Si es True, ejecuta el monitoreo en segundo plano

Returns:
    Diccionario con estadísticas de las tablas o el ID de la tarea
 * @summary Monitor Tables
 */
  const monitorTablesApiV1MaintenanceMaintenanceMonitorTablesPost = <
    TData = AxiosResponse<MonitorTablesApiV1MaintenanceMaintenanceMonitorTablesPost200>,
  >(
    params?: MonitorTablesApiV1MaintenanceMaintenanceMonitorTablesPostParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(
      `/api/v1/maintenance/maintenance/monitor-tables`,
      undefined,
      {
        ...options,
        params: { ...params, ...options?.params },
      },
    );
  };

  /**
 * Get subscription usage information for the current account.

Returns:
    Dict with subscription usage information including:
    - API calls used and limit
    - Storage used and limit
    - Available models
    - Reset date for API calls counter
 * @summary Get Subscription Usage
 */
  const getSubscriptionUsageApiV1SubscriptionUsageGet = <
    TData = AxiosResponse<GetSubscriptionUsageApiV1SubscriptionUsageGet200>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/subscription/usage`, options);
  };

  /**
 * Get storage usage information for the current account.

Returns:
    Dict with storage usage information including:
    - Storage used and limit
    - Breakdown by data type
    - Last measurement time
 * @summary Get Storage Usage
 */
  const getStorageUsageApiV1StorageUsageGet = <
    TData = AxiosResponse<GetStorageUsageApiV1StorageUsageGet200>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/storage/usage`, options);
  };

  /**
 * Force a refresh of storage usage calculation for the current account.

Returns:
    Dict with updated storage usage information
 * @summary Refresh Storage Usage
 */
  const refreshStorageUsageApiV1StorageRefreshPost = <
    TData = AxiosResponse<RefreshStorageUsageApiV1StorageRefreshPost200>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(`/api/v1/storage/refresh`, undefined, options);
  };

  /**
 * Carga masiva de datos de usuarios, productos e interacciones.

Este endpoint permite cargar datos en lote para inicializar o actualizar el sistema de recomendación.
Los datos se procesan de forma asíncrona utilizando Celery para mayor robustez.
Los datos sensibles se almacenan de forma segura en GCS o en el sistema de archivos local.

Para una guía detallada sobre cómo formatear y enviar datos, consulte la
[Guía de Ingesta de Datos Masiva](/docs/guides/data_ingestion_guide).
 * @summary Batch Data Ingestion
 */
  const batchDataIngestionApiV1IngestionBatchPost = <
    TData = AxiosResponse<BatchIngestionResponse>,
  >(
    batchIngestionRequest: BatchIngestionRequest,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(
      `/api/v1/ingestion/batch`,
      batchIngestionRequest,
      options,
    );
  };

  /**
 * Lista los trabajos de ingesta masiva recientes para la cuenta actual.

Este endpoint permite obtener una lista de trabajos de ingesta masiva,
ordenados por fecha de creación (más recientes primero).

Args:
    limit: Número máximo de trabajos a devolver (máximo 100)
    status: Filtrar por estado del trabajo (opcional)
    account: Cuenta actual
    db: Sesión de base de datos

Returns:
    Lista de trabajos de ingesta masiva
 * @summary List Batch Jobs
 */
  const listBatchJobsApiV1IngestionBatchGet = <
    TData = AxiosResponse<BatchIngestionJobStatus[]>,
  >(
    params?: ListBatchJobsApiV1IngestionBatchGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/ingestion/batch`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
 * Consulta el estado de un trabajo de ingesta masiva.

Este endpoint permite verificar el progreso de un trabajo de ingesta masiva,
incluyendo la cantidad de registros procesados, errores encontrados y tiempo estimado
de finalización.

Para más detalles sobre cómo monitorear el proceso de ingesta y manejar errores,
consulte la sección [Monitoreo del Proceso](/docs/guides/data_ingestion_guide#monitoreo-del-proceso)
en la Guía de Ingesta de Datos.

Args:
    job_id: ID del trabajo de ingesta masiva
    account: Cuenta actual
    db: Sesión de base de datos

Returns:
    Estado del trabajo de ingesta masiva
 * @summary Get Batch Job Status
 */
  const getBatchJobStatusApiV1IngestionBatchJobIdGet = <
    TData = AxiosResponse<BatchIngestionJobStatus>,
  >(
    jobId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/ingestion/batch/${jobId}`, options);
  };

  /**
   * Return a consolidated summary of usage information for the current account.
   * @summary Get Usage Summary
   */
  const getUsageSummaryApiV1UsageSummaryGet = <
    TData = AxiosResponse<UsageSummaryResponse>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/usage/summary`, options);
  };

  /**
 * Get historical usage data for the current account.

Args:
    start_date: Optional start date for filtering data
    end_date: Optional end date for filtering data
    
Returns:
    List of daily usage data points
 * @summary Get Usage History
 */
  const getUsageHistoryApiV1UsageGet = <
    TData = AxiosResponse<UsageHistoryItemResponse[]>,
  >(
    params?: GetUsageHistoryApiV1UsageGetParams,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/usage/`, {
      ...options,
      params: { ...params, ...options?.params },
    });
  };

  /**
 * Lista todas las API Keys activas para la cuenta actual.

Retorna una lista de todas las API Keys activas asociadas a la cuenta,
incluyendo información como nombre, prefijo, últimos caracteres, fecha de creación
y último uso.

**Nota**: No se incluyen las API Keys completas por seguridad.
 * @summary List Api Keys
 */
  const listApiKeysApiV1ApiKeysGet = <
    TData = AxiosResponse<ApiKeyListResponse>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/api-keys/`, options);
  };

  /**
 * Crea una nueva API Key para la cuenta actual.

Este endpoint permite crear múltiples API Keys para una cuenta, lo que mejora
la seguridad y flexibilidad:
- Diferentes claves para diferentes entornos (desarrollo, staging, producción)
- Claves específicas para diferentes miembros del equipo
- Posibilidad de revocar claves específicas sin afectar otras integraciones

**IMPORTANTE**:
- Requiere autenticación JWT (debes estar logueado)
- La API Key completa solo se devolverá una vez
- Puedes crear múltiples API Keys activas
- Cada API Key puede tener un nombre descriptivo

**Autenticación requerida**: JWT token en el header Authorization: Bearer <token>

Returns:
- **id**: ID único de la API Key
- **api_key**: Tu nueva API Key completa (solo se muestra una vez)
- **name**: Nombre descriptivo de la API Key
- **prefix**: Prefijo de la API Key para identificación
- **created_at**: Fecha y hora de creación
- **message**: Mensaje informativo sobre el uso seguro
 * @summary Create Api Key
 */
  const createApiKeyApiV1ApiKeysPost = <
    TData = AxiosResponse<NewApiKeyResponse>,
  >(
    apiKeyCreate: ApiKeyCreate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(`/api/v1/api-keys/`, apiKeyCreate, options);
  };

  /**
 * Revoca todas las API Keys de la cuenta actual.
Esta acción es irreversible. Después de revocar, tendrás que generar nuevas API Keys.
 * @summary Revoke Api Key
 */
  const revokeApiKeyApiV1ApiKeysDelete = <TData = AxiosResponse<void>>(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.delete(`/api/v1/api-keys/`, options);
  };

  /**
 * Obtiene información sobre la API Key actual.
No devuelve la API Key completa, solo metadatos como:
- Prefijo de la API Key
- Últimos caracteres
- Fecha de creación
- Último uso (si está disponible)

Esta información permite identificar la API Key sin comprometer seguridad.
 * @summary Get Current Api Key
 */
  const getCurrentApiKeyApiV1ApiKeysCurrentGet = <
    TData = AxiosResponse<ApiKeyResponse>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/api-keys/current`, options);
  };

  /**
 * Actualiza los metadatos de una API Key específica.

Permite actualizar información como el nombre descriptivo de la API Key.
No se puede cambiar la clave en sí, solo sus metadatos.

**IMPORTANTE**:
- Solo puedes actualizar API Keys que pertenecen a tu cuenta
- No se puede cambiar la API Key en sí, solo metadatos
- La API Key debe estar activa para poder actualizarla
 * @summary Update Api Key
 */
  const updateApiKeyApiV1ApiKeysApiKeyIdPut = <
    TData = AxiosResponse<ApiKeyResponse>,
  >(
    apiKeyId: number,
    apiKeyUpdate: ApiKeyUpdate,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.put(`/api/v1/api-keys/${apiKeyId}`, apiKeyUpdate, options);
  };

  /**
 * Revoca una API Key específica.

Esta acción es irreversible. La API Key será desactivada y no podrá
utilizarse para autenticar solicitudes.

**IMPORTANTE**:
- Solo puedes revocar API Keys que pertenecen a tu cuenta
- Esta acción es irreversible
- La API Key dejará de funcionar inmediatamente
- Otras API Keys de la cuenta no se verán afectadas
 * @summary Revoke Specific Api Key
 */
  const revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete = <
    TData = AxiosResponse<void>,
  >(
    apiKeyId: number,
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.delete(`/api/v1/api-keys/${apiKeyId}`, options);
  };

  /**
 * Reset all sandbox data for FREE plan users.

This endpoint allows FREE plan users to clean their experimental data
to start fresh with new experiments.

Only available for FREE plan users.
 * @summary Reset Sandbox Data
 */
  const resetSandboxDataApiV1SandboxResetPost = <
    TData = AxiosResponse<unknown>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.post(`/api/v1/sandbox/reset`, undefined, options);
  };

  /**
 * Get current sandbox status and data counts.

Returns information about the current state of sandbox data.
 * @summary Get Sandbox Status
 */
  const getSandboxStatusApiV1SandboxStatusGet = <
    TData = AxiosResponse<unknown>,
  >(
    options?: AxiosRequestConfig,
  ): Promise<TData> => {
    return axios.get(`/api/v1/sandbox/status`, options);
  };

  return {
    healthCheckHealthGet,
    healthCheckApiV1HealthGet,
    dbHealthCheckApiV1HealthDbGet,
    authHealthCheckApiV1HealthAuthGet,
    sendVerificationEmailApiV1AuthSendVerificationEmailPost,
    verifyEmailApiV1AuthVerifyEmailGet,
    registerApiV1AuthRegisterPost,
    loginApiV1AuthTokenPost,
    logoutApiV1AuthLogoutPost,
    listAccountsApiV1AccountsGet,
    createAccountApiV1AccountsAccountsPost,
    getAccountApiV1AccountsAccountIdGet,
    deactivateAccountApiV1AccountsAccountIdDeactivatePatch,
    activateAccountApiV1AccountsAccountIdActivatePatch,
    getAccountInfoApiV1AccountsCurrentGet,
    updateCurrentAccountApiV1AccountsCurrentPut,
    patchCurrentAccountApiV1AccountsCurrentPatch,
    getAuditLogsApiV1AccountsAccountIdAuditLogsGet,
    getApiUsageApiV1AccountsUsageGet,
    getAvailablePlansApiV1PlansGet,
    getCurrentUserInfoApiV1SystemUsersMeGet,
    updateUserMeApiV1SystemUsersMePut,
    deleteUserMeApiV1SystemUsersMeDelete,
    createSystemUserApiV1SystemUsersPost,
    getSystemUserApiV1SystemUsersUserIdGet,
    createRoleApiV1SystemUsersRolesPost,
    assignRoleApiV1SystemUsersUserIdRolesRoleIdPost,
    removeRoleApiV1SystemUsersUserIdRolesRoleIdDelete,
    getUserRolesApiV1SystemUsersUserIdRolesGet,
    getUserPermissionsApiV1SystemUsersUserIdPermissionsGet,
    createEndUserApiV1EndUsersPost,
    readEndUsersApiV1EndUsersGet,
    readEndUserApiV1EndUsersUserIdGet,
    createProductApiV1ProductsPost,
    readProductsApiV1ProductsGet,
    getProductApiV1ProductsProductIdGet,
    updateProductApiV1ProductsProductIdPut,
    updateInventoryApiV1ProductsProductIdInventoryPatch,
    getProductByExternalIdApiV1ProductsExternalExternalIdGet,
    updateProductByExternalIdApiV1ProductsExternalExternalIdPut,
    deleteProductByExternalIdApiV1ProductsExternalExternalIdDelete,
    updateInventoryByExternalIdApiV1ProductsExternalExternalIdInventoryPatch,
    getMostSearchedApiV1RecommendationsMostSearchedGet,
    getTrendingSearchesApiV1RecommendationsTrendingSearchesGet,
    getPopularTrendsApiV1RecommendationsPopularTrendsGet,
    getRelatedSearchesApiV1RecommendationsRelatedSearchesProductIdGet,
    getMostSoldApiV1RecommendationsMostSoldGet,
    getTopRatedApiV1RecommendationsTopRatedGet,
    getCategoryProductsApiV1RecommendationsCategoryCategoryGet,
    getRelatedCategoriesApiV1RecommendationsRelatedCategoriesCategoryGet,
    getAlsoBoughtApiV1RecommendationsAlsoBoughtProductIdGet,
    getSimilarProductsApiV1RecommendationsProductsProductIdSimilarGet,
    invalidateUserCacheApiV1RecommendationsInvalidateCacheUserIdPost,
    invalidateAccountCacheApiV1RecommendationsInvalidateCachePost,
    queryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPost,
    getRecommendationExplanationApiV1RecommendationsExplainUserIdItemIdGet,
    getRecommendationExplanationExternalApiV1RecommendationsExplainExternalExternalUserIdExternalItemIdGet,
    getSimilarProductsExternalApiV1RecommendationsProductsExternalExternalProductIdSimilarGet,
    getAlsoBoughtExternalApiV1RecommendationsAlsoBoughtExternalExternalProductIdGet,
    getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet,
    rollbackModelApiV1RecommendationsRollbackArtifactVersionPost,
    getRecommendationPerformanceApiV1RecommendationsPerformanceGet,
    createInteractionApiV1InteractionsPost,
    readInteractionsApiV1InteractionsGet,
    createInteractionExternalApiV1InteractionsExternalPost,
    trainModelsApiV1PipelineTrainPost,
    getTrainingStatusApiV1PipelineStatusGet,
    getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet,
    listModelsApiV1PipelineModelsGet,
    getModelMetricsApiV1PipelineModelsModelIdMetricsGet,
    invalidateCacheApiV1PipelineInvalidateCachePost,
    trainArtifactForAccountApiV1PipelineTrainAccountIdPost,
    processTrainingJobApiV1PipelineProcessPost,
    trainingCallbackApiV1PipelineCallbackJobIdPost,
    listTrainingJobsApiV1PipelineJobsGet,
    testCacheApiV1CacheTestCacheGet,
    checkRedisApiV1CacheRedisHealthGet,
    getAccountAnalyticsApiV1AnalyticsAnalyticsAccountGet,
    getEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGet,
    getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet,
    compareModelVersionsApiV1AnalyticsAnalyticsModelsCompareGet,
    getMetricsHistoryApiV1AnalyticsAnalyticsMetricsHistoryGet,
    createCheckoutSessionApiV1BillingCreateCheckoutSessionPost,
    createPortalSessionApiV1BillingCreatePortalSessionPost,
    mercadopagoWebhookApiV1BillingWebhookMercadopagoPost,
    listRolesApiV1RolesGet,
    createRoleApiV1RolesPost,
    getRoleApiV1RolesRoleIdGet,
    updateRoleApiV1RolesRoleIdPut,
    deleteRoleApiV1RolesRoleIdDelete,
    getRolePermissionsApiV1RolesRoleIdPermissionsGet,
    assignPermissionToRoleApiV1RolesRoleIdPermissionsPermissionIdPost,
    removePermissionFromRoleApiV1RolesRoleIdPermissionsPermissionIdDelete,
    listPermissionsApiV1PermissionsGet,
    createPermissionApiV1PermissionsPost,
    getPermissionApiV1PermissionsPermissionIdGet,
    getRolesWithPermissionApiV1PermissionsPermissionIdRolesGet,
    cleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPost,
    cleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPost,
    getTaskStatusApiV1MaintenanceMaintenanceTaskTaskIdGet,
    archiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPost,
    archiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPost,
    listArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGet,
    cleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPost,
    getSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGet,
    monitorTablesApiV1MaintenanceMaintenanceMonitorTablesPost,
    getSubscriptionUsageApiV1SubscriptionUsageGet,
    getStorageUsageApiV1StorageUsageGet,
    refreshStorageUsageApiV1StorageRefreshPost,
    batchDataIngestionApiV1IngestionBatchPost,
    listBatchJobsApiV1IngestionBatchGet,
    getBatchJobStatusApiV1IngestionBatchJobIdGet,
    getUsageSummaryApiV1UsageSummaryGet,
    getUsageHistoryApiV1UsageGet,
    listApiKeysApiV1ApiKeysGet,
    createApiKeyApiV1ApiKeysPost,
    revokeApiKeyApiV1ApiKeysDelete,
    getCurrentApiKeyApiV1ApiKeysCurrentGet,
    updateApiKeyApiV1ApiKeysApiKeyIdPut,
    revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete,
    resetSandboxDataApiV1SandboxResetPost,
    getSandboxStatusApiV1SandboxStatusGet,
  };
};
export type HealthCheckHealthGetResult = AxiosResponse<unknown>;
export type HealthCheckApiV1HealthGetResult =
  AxiosResponse<HealthCheckApiV1HealthGet200>;
export type DbHealthCheckApiV1HealthDbGetResult =
  AxiosResponse<DbHealthCheckApiV1HealthDbGet200>;
export type AuthHealthCheckApiV1HealthAuthGetResult =
  AxiosResponse<AuthHealthCheckApiV1HealthAuthGet200>;
export type SendVerificationEmailApiV1AuthSendVerificationEmailPostResult =
  AxiosResponse<unknown>;
export type VerifyEmailApiV1AuthVerifyEmailGetResult = AxiosResponse<unknown>;
export type RegisterApiV1AuthRegisterPostResult =
  AxiosResponse<RegisterResponse>;
export type LoginApiV1AuthTokenPostResult = AxiosResponse<LoginResponse>;
export type LogoutApiV1AuthLogoutPostResult = AxiosResponse<unknown>;
export type ListAccountsApiV1AccountsGetResult = AxiosResponse<
  AccountResponse[]
>;
export type CreateAccountApiV1AccountsAccountsPostResult =
  AxiosResponse<AccountResponse>;
export type GetAccountApiV1AccountsAccountIdGetResult =
  AxiosResponse<AccountResponse>;
export type DeactivateAccountApiV1AccountsAccountIdDeactivatePatchResult =
  AxiosResponse<void>;
export type ActivateAccountApiV1AccountsAccountIdActivatePatchResult =
  AxiosResponse<AccountResponse>;
export type GetAccountInfoApiV1AccountsCurrentGetResult =
  AxiosResponse<AccountResponse>;
export type UpdateCurrentAccountApiV1AccountsCurrentPutResult =
  AxiosResponse<AccountResponse>;
export type PatchCurrentAccountApiV1AccountsCurrentPatchResult =
  AxiosResponse<AccountResponse>;
export type GetAuditLogsApiV1AccountsAccountIdAuditLogsGetResult =
  AxiosResponse<AuditLog[]>;
export type GetApiUsageApiV1AccountsUsageGetResult = AxiosResponse<UsageStats>;
export type GetAvailablePlansApiV1PlansGetResult =
  AxiosResponse<GetAvailablePlansApiV1PlansGet200>;
export type GetCurrentUserInfoApiV1SystemUsersMeGetResult =
  AxiosResponse<SystemUserResponse>;
export type UpdateUserMeApiV1SystemUsersMePutResult =
  AxiosResponse<SystemUserResponse>;
export type DeleteUserMeApiV1SystemUsersMeDeleteResult = AxiosResponse<void>;
export type CreateSystemUserApiV1SystemUsersPostResult =
  AxiosResponse<SystemUserResponse>;
export type GetSystemUserApiV1SystemUsersUserIdGetResult =
  AxiosResponse<SystemUserResponse>;
export type CreateRoleApiV1SystemUsersRolesPostResult = AxiosResponse<Role>;
export type AssignRoleApiV1SystemUsersUserIdRolesRoleIdPostResult =
  AxiosResponse<unknown>;
export type RemoveRoleApiV1SystemUsersUserIdRolesRoleIdDeleteResult =
  AxiosResponse<void>;
export type GetUserRolesApiV1SystemUsersUserIdRolesGetResult = AxiosResponse<
  Role[]
>;
export type GetUserPermissionsApiV1SystemUsersUserIdPermissionsGetResult =
  AxiosResponse<string[]>;
export type CreateEndUserApiV1EndUsersPostResult = AxiosResponse<EndUser>;
export type ReadEndUsersApiV1EndUsersGetResult =
  AxiosResponse<PaginatedResponseEndUser>;
export type ReadEndUserApiV1EndUsersUserIdGetResult = AxiosResponse<EndUser>;
export type CreateProductApiV1ProductsPostResult = AxiosResponse<Product>;
export type ReadProductsApiV1ProductsGetResult =
  AxiosResponse<PaginatedResponseProduct>;
export type GetProductApiV1ProductsProductIdGetResult = AxiosResponse<Product>;
export type UpdateProductApiV1ProductsProductIdPutResult =
  AxiosResponse<Product>;
export type UpdateInventoryApiV1ProductsProductIdInventoryPatchResult =
  AxiosResponse<Product>;
export type GetProductByExternalIdApiV1ProductsExternalExternalIdGetResult =
  AxiosResponse<Product>;
export type UpdateProductByExternalIdApiV1ProductsExternalExternalIdPutResult =
  AxiosResponse<Product>;
export type DeleteProductByExternalIdApiV1ProductsExternalExternalIdDeleteResult =
  AxiosResponse<void>;
export type UpdateInventoryByExternalIdApiV1ProductsExternalExternalIdInventoryPatchResult =
  AxiosResponse<Product>;
export type GetMostSearchedApiV1RecommendationsMostSearchedGetResult =
  AxiosResponse<PaginatedResponseProduct>;
export type GetTrendingSearchesApiV1RecommendationsTrendingSearchesGetResult =
  AxiosResponse<PaginatedResponseSearch>;
export type GetPopularTrendsApiV1RecommendationsPopularTrendsGetResult =
  AxiosResponse<PaginatedResponseProduct>;
export type GetRelatedSearchesApiV1RecommendationsRelatedSearchesProductIdGetResult =
  AxiosResponse<PaginatedResponseSearch>;
export type GetMostSoldApiV1RecommendationsMostSoldGetResult =
  AxiosResponse<PaginatedResponseProduct>;
export type GetTopRatedApiV1RecommendationsTopRatedGetResult =
  AxiosResponse<PaginatedResponseProduct>;
export type GetCategoryProductsApiV1RecommendationsCategoryCategoryGetResult =
  AxiosResponse<PaginatedResponseProduct>;
export type GetRelatedCategoriesApiV1RecommendationsRelatedCategoriesCategoryGetResult =
  AxiosResponse<PaginatedResponseCategory>;
export type GetAlsoBoughtApiV1RecommendationsAlsoBoughtProductIdGetResult =
  AxiosResponse<PaginatedResponseProduct>;
export type GetSimilarProductsApiV1RecommendationsProductsProductIdSimilarGetResult =
  AxiosResponse<PaginatedResponseProduct>;
export type InvalidateUserCacheApiV1RecommendationsInvalidateCacheUserIdPostResult =
  AxiosResponse<unknown>;
export type InvalidateAccountCacheApiV1RecommendationsInvalidateCachePostResult =
  AxiosResponse<unknown>;
export type QueryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPostResult =
  AxiosResponse<PaginatedResponseProduct>;
export type GetRecommendationExplanationApiV1RecommendationsExplainUserIdItemIdGetResult =
  AxiosResponse<DetailedExplanation>;
export type GetRecommendationExplanationExternalApiV1RecommendationsExplainExternalExternalUserIdExternalItemIdGetResult =
  AxiosResponse<DetailedExplanation>;
export type GetSimilarProductsExternalApiV1RecommendationsProductsExternalExternalProductIdSimilarGetResult =
  AxiosResponse<PaginatedResponseProduct>;
export type GetAlsoBoughtExternalApiV1RecommendationsAlsoBoughtExternalExternalProductIdGetResult =
  AxiosResponse<PaginatedResponseProduct>;
export type GetConfidenceMetricsApiV1RecommendationsConfidenceMetricsGetResult =
  AxiosResponse<GetConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet200>;
export type RollbackModelApiV1RecommendationsRollbackArtifactVersionPostResult =
  AxiosResponse<unknown>;
export type GetRecommendationPerformanceApiV1RecommendationsPerformanceGetResult =
  AxiosResponse<RecommendationPerformanceResponse>;
export type CreateInteractionApiV1InteractionsPostResult =
  AxiosResponse<Interaction>;
export type ReadInteractionsApiV1InteractionsGetResult =
  AxiosResponse<PaginatedResponseInteraction>;
export type CreateInteractionExternalApiV1InteractionsExternalPostResult =
  AxiosResponse<Interaction>;
export type TrainModelsApiV1PipelineTrainPostResult =
  AxiosResponse<TrainingResponse>;
export type GetTrainingStatusApiV1PipelineStatusGetResult =
  AxiosResponse<TrainingStatus>;
export type GetTrainingJobStatusApiV1PipelineJobsJobIdStatusGetResult =
  AxiosResponse<TrainingJobStatus>;
export type ListModelsApiV1PipelineModelsGetResult = AxiosResponse<
  ModelMetadataResponse[]
>;
export type GetModelMetricsApiV1PipelineModelsModelIdMetricsGetResult =
  AxiosResponse<TrainingStatus>;
export type InvalidateCacheApiV1PipelineInvalidateCachePostResult =
  AxiosResponse<unknown>;
export type TrainArtifactForAccountApiV1PipelineTrainAccountIdPostResult =
  AxiosResponse<TrainArtifactForAccountApiV1PipelineTrainAccountIdPost202>;
export type ProcessTrainingJobApiV1PipelineProcessPostResult =
  AxiosResponse<ProcessTrainingJobApiV1PipelineProcessPost200>;
export type TrainingCallbackApiV1PipelineCallbackJobIdPostResult =
  AxiosResponse<TrainingCallbackApiV1PipelineCallbackJobIdPost200>;
export type ListTrainingJobsApiV1PipelineJobsGetResult = AxiosResponse<
  TrainingJobStatus[]
>;
export type TestCacheApiV1CacheTestCacheGetResult = AxiosResponse<unknown>;
export type CheckRedisApiV1CacheRedisHealthGetResult = AxiosResponse<unknown>;
export type GetAccountAnalyticsApiV1AnalyticsAnalyticsAccountGetResult =
  AxiosResponse<GetAccountAnalyticsApiV1AnalyticsAnalyticsAccountGet200>;
export type GetEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGetResult =
  AxiosResponse<
    GetEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGet200Item[]
  >;
export type GetRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGetResult =
  AxiosResponse<RecommendationPerformanceResponse>;
export type CompareModelVersionsApiV1AnalyticsAnalyticsModelsCompareGetResult =
  AxiosResponse<ModelComparisonResponse>;
export type GetMetricsHistoryApiV1AnalyticsAnalyticsMetricsHistoryGetResult =
  AxiosResponse<MetricsHistoryResponse>;
export type CreateCheckoutSessionApiV1BillingCreateCheckoutSessionPostResult =
  AxiosResponse<CreateCheckoutSessionResponse>;
export type CreatePortalSessionApiV1BillingCreatePortalSessionPostResult =
  AxiosResponse<CreatePortalSessionResponse>;
export type MercadopagoWebhookApiV1BillingWebhookMercadopagoPostResult =
  AxiosResponse<unknown>;
export type ListRolesApiV1RolesGetResult = AxiosResponse<Role[]>;
export type CreateRoleApiV1RolesPostResult = AxiosResponse<Role>;
export type GetRoleApiV1RolesRoleIdGetResult = AxiosResponse<Role>;
export type UpdateRoleApiV1RolesRoleIdPutResult = AxiosResponse<Role>;
export type DeleteRoleApiV1RolesRoleIdDeleteResult = AxiosResponse<void>;
export type GetRolePermissionsApiV1RolesRoleIdPermissionsGetResult =
  AxiosResponse<Permission[]>;
export type AssignPermissionToRoleApiV1RolesRoleIdPermissionsPermissionIdPostResult =
  AxiosResponse<unknown>;
export type RemovePermissionFromRoleApiV1RolesRoleIdPermissionsPermissionIdDeleteResult =
  AxiosResponse<void>;
export type ListPermissionsApiV1PermissionsGetResult = AxiosResponse<
  Permission[]
>;
export type CreatePermissionApiV1PermissionsPostResult =
  AxiosResponse<Permission>;
export type GetPermissionApiV1PermissionsPermissionIdGetResult =
  AxiosResponse<Permission>;
export type GetRolesWithPermissionApiV1PermissionsPermissionIdRolesGetResult =
  AxiosResponse<Role[]>;
export type CleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPostResult =
  AxiosResponse<CleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPost200>;
export type CleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPostResult =
  AxiosResponse<CleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPost200>;
export type GetTaskStatusApiV1MaintenanceMaintenanceTaskTaskIdGetResult =
  AxiosResponse<GetTaskStatusApiV1MaintenanceMaintenanceTaskTaskIdGet200>;
export type ArchiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPostResult =
  AxiosResponse<ArchiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPost200>;
export type ArchiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPostResult =
  AxiosResponse<ArchiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPost200>;
export type ListArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGetResult =
  AxiosResponse<ListArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGet200>;
export type CleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPostResult =
  AxiosResponse<CleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPost200>;
export type GetSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGetResult =
  AxiosResponse<GetSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGet200>;
export type MonitorTablesApiV1MaintenanceMaintenanceMonitorTablesPostResult =
  AxiosResponse<MonitorTablesApiV1MaintenanceMaintenanceMonitorTablesPost200>;
export type GetSubscriptionUsageApiV1SubscriptionUsageGetResult =
  AxiosResponse<GetSubscriptionUsageApiV1SubscriptionUsageGet200>;
export type GetStorageUsageApiV1StorageUsageGetResult =
  AxiosResponse<GetStorageUsageApiV1StorageUsageGet200>;
export type RefreshStorageUsageApiV1StorageRefreshPostResult =
  AxiosResponse<RefreshStorageUsageApiV1StorageRefreshPost200>;
export type BatchDataIngestionApiV1IngestionBatchPostResult =
  AxiosResponse<BatchIngestionResponse>;
export type ListBatchJobsApiV1IngestionBatchGetResult = AxiosResponse<
  BatchIngestionJobStatus[]
>;
export type GetBatchJobStatusApiV1IngestionBatchJobIdGetResult =
  AxiosResponse<BatchIngestionJobStatus>;
export type GetUsageSummaryApiV1UsageSummaryGetResult =
  AxiosResponse<UsageSummaryResponse>;
export type GetUsageHistoryApiV1UsageGetResult = AxiosResponse<
  UsageHistoryItemResponse[]
>;
export type ListApiKeysApiV1ApiKeysGetResult =
  AxiosResponse<ApiKeyListResponse>;
export type CreateApiKeyApiV1ApiKeysPostResult =
  AxiosResponse<NewApiKeyResponse>;
export type RevokeApiKeyApiV1ApiKeysDeleteResult = AxiosResponse<void>;
export type GetCurrentApiKeyApiV1ApiKeysCurrentGetResult =
  AxiosResponse<ApiKeyResponse>;
export type UpdateApiKeyApiV1ApiKeysApiKeyIdPutResult =
  AxiosResponse<ApiKeyResponse>;
export type RevokeSpecificApiKeyApiV1ApiKeysApiKeyIdDeleteResult =
  AxiosResponse<void>;
export type ResetSandboxDataApiV1SandboxResetPostResult =
  AxiosResponse<unknown>;
export type GetSandboxStatusApiV1SandboxStatusGetResult =
  AxiosResponse<unknown>;
