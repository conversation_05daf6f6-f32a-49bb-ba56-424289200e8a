(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ui/RayuelaLogo.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/components/ui/RayuelaLogo.tsx
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
"use client";
;
;
;
const RayuelaLogo = ({ variant = 'default', size = 'md', showText = false, href, className = '', priority = false, widthPx, heightPx, textClassName })=>{
    // Tamaños del logo (manteniendo aspect ratio de la imagen original)
    const logoSizes = {
        sm: {
            width: 100,
            height: 32,
            text: 'text-lg'
        },
        md: {
            width: 125,
            height: 40,
            text: 'text-xl'
        },
        lg: {
            width: 150,
            height: 48,
            text: 'text-2xl'
        },
        xl: {
            width: 200,
            height: 64,
            text: 'text-3xl'
        }
    };
    // Colores según variante para el texto adicional (si se usa)
    const colorClasses = {
        default: {
            text: 'text-foreground',
            accent: 'text-accent'
        },
        white: {
            text: 'text-white',
            accent: 'text-white/80'
        },
        dark: {
            text: 'text-foreground',
            accent: 'text-foreground'
        }
    };
    const { width, height, text: textSize } = logoSizes[size];
    const finalWidth = widthPx ?? width;
    const finalHeight = heightPx ?? height;
    const colors = colorClasses[variant];
    const logoContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `flex items-center ${className}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                src: "/logo-rayuela.png",
                alt: "Rayuela.ai",
                width: finalWidth,
                height: finalHeight,
                priority: priority,
                className: "object-contain"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/RayuelaLogo.tsx",
                lineNumber: 62,
                columnNumber: 7
            }, this),
            showText && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `font-bold ${textClassName ?? textSize} ${colors.text} tracking-tight ml-3`,
                children: [
                    "Rayuela",
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: colors.accent,
                        children: ".ai"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/RayuelaLogo.tsx",
                        lineNumber: 72,
                        columnNumber: 18
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/RayuelaLogo.tsx",
                lineNumber: 71,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/RayuelaLogo.tsx",
        lineNumber: 61,
        columnNumber: 5
    }, this);
    if (href) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            href: href,
            className: "group transition-transform duration-200 hover:scale-105 inline-flex",
            children: logoContent
        }, void 0, false, {
            fileName: "[project]/src/components/ui/RayuelaLogo.tsx",
            lineNumber: 80,
            columnNumber: 7
        }, this);
    }
    return logoContent;
};
_c = RayuelaLogo;
const __TURBOPACK__default__export__ = RayuelaLogo;
var _c;
__turbopack_context__.k.register(_c, "RayuelaLogo");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_components_ui_RayuelaLogo_tsx_387f293d._.js.map