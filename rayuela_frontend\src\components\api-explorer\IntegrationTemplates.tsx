"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Copy,
  CheckCircle,
  Download,
  Rocket,
  Code,
  Package,
  ShoppingCart,
  FileText,
  Globe
} from "lucide-react";

interface IntegrationTemplate {
  id: string;
  name: string;
  description: string;
  category: 'ecommerce' | 'content' | 'saas' | 'marketplace';
  framework: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  features: string[];
  files: {
    name: string;
    content: string;
    language: string;
  }[];
}

const INTEGRATION_TEMPLATES: IntegrationTemplate[] = [
  {
    id: 'react-ecommerce',
    name: 'React E-commerce Store',
    description: 'Complete e-commerce integration with homepage, product pages, and cart recommendations',
    category: 'ecommerce',
    framework: 'React',
    difficulty: 'intermediate',
    features: ['Homepage recommendations', 'Product page cross-sell', 'Cart upsells', 'A/B testing', 'Analytics tracking'],
    files: [
      {
        name: 'components/RecommendationWidget.tsx',
        language: 'typescript',
        content: 'import React, { useState, useEffect } from "react";\nimport Rayuela from "rayuela-sdk";\n\n// React component for recommendations\nexport default function RecommendationWidget() {\n  // Component implementation here\n  return <div>Recommendations</div>;\n}'
      },
      {
        name: 'hooks/useRecommendations.ts',
        language: 'typescript',
        content: 'import { useState, useEffect } from "react";\nimport Rayuela from "rayuela-sdk";\n\n// Custom hook for recommendations\nexport function useRecommendations(userId: string) {\n  // Hook implementation here\n  return { recommendations: [], loading: false };\n}'
      }
    ]
  },
  {
    id: 'nextjs-api',
    name: 'Next.js API Integration',
    description: 'Server-side API routes with caching and error handling',
    category: 'ecommerce',
    framework: 'Next.js',
    difficulty: 'intermediate',
    features: ['Server-side rendering', 'API caching', 'Error handling', 'Rate limiting'],
    files: [
      {
        name: 'pages/api/recommendations/[userId].ts',
        language: 'typescript',
        content: 'import type { NextApiRequest, NextApiResponse } from "next";\nimport Rayuela from "rayuela-sdk";\n\n// Next.js API route\nexport default async function handler(req: NextApiRequest, res: NextApiResponse) {\n  // API implementation here\n  res.json({ success: true });\n}'
      }
    ]
  },
  {
    id: 'python-flask',
    name: 'Python Flask Application',
    description: 'Flask web application with recommendations and analytics',
    category: 'ecommerce',
    framework: 'Flask',
    difficulty: 'intermediate',
    features: ['Flask routes', 'Template integration', 'Error handling', 'Analytics'],
    files: [
      {
        name: 'app.py',
        language: 'python',
        content: 'from flask import Flask, render_template\nfrom rayuela import Rayuela\n\n# Flask application\napp = Flask(__name__)\nclient = Rayuela(api_key="your-api-key")\n\<EMAIL>("/")\ndef homepage():\n    return render_template("homepage.html")'
      }
    ]
  }
];

function IntegrationTemplates() {
  const [selectedTemplate, setSelectedTemplate] = useState<IntegrationTemplate>(INTEGRATION_TEMPLATES[0]);
  const [selectedFile, setSelectedFile] = useState(0);
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  const copyToClipboard = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedCode(id);
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const downloadTemplate = () => {
    const zip = selectedTemplate.files.map(file => ({
      name: file.name,
      content: file.content
    }));

    // Create a simple text file with all files
    const allFiles = zip.map(file =>
      `// File: ${file.name}\n${file.content}\n\n${'='.repeat(80)}\n\n`
    ).join('');

    const blob = new Blob([allFiles], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${selectedTemplate.id}-template.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-muted text-foreground';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'ecommerce': return <ShoppingCart className="h-4 w-4" />;
      case 'content': return <FileText className="h-4 w-4" />;
      case 'saas': return <Globe className="h-4 w-4" />;
      case 'marketplace': return <Package className="h-4 w-4" />;
      default: return <Code className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Integration Templates</h1>
        <p className="text-muted-foreground">
          Production-ready code templates you can copy and paste into your project
        </p>
      </div>

      {/* Template Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Rocket className="h-5 w-5" />
            Choose Your Template
          </CardTitle>
          <CardDescription>
            Select a template that matches your technology stack
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {INTEGRATION_TEMPLATES.map((template) => (
              <Card
                key={template.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedTemplate.id === template.id ? 'ring-2 ring-blue-500' : ''
                }`}
                onClick={() => {
                  setSelectedTemplate(template);
                  setSelectedFile(0);
                }}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    {getCategoryIcon(template.category)}
                    <h3 className="font-semibold">{template.name}</h3>
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">{template.description}</p>
                  <div className="flex items-center justify-between">
                    <Badge variant="outline">{template.framework}</Badge>
                    <Badge className={getDifficultyColor(template.difficulty)}>
                      {template.difficulty}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Template Details */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                {getCategoryIcon(selectedTemplate.category)}
                {selectedTemplate.name}
              </CardTitle>
              <CardDescription>{selectedTemplate.description}</CardDescription>
            </div>
            <div className="flex gap-2">
              <Button onClick={downloadTemplate} variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Download All
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Features */}
          <div className="mb-6">
            <h4 className="font-semibold mb-3">Features Included</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {selectedTemplate.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </div>

          {/* File Tabs */}
          <Tabs value={selectedFile.toString()} onValueChange={(value) => setSelectedFile(Number(value))}>
            <TabsList className="grid w-full grid-cols-auto overflow-x-auto">
              {selectedTemplate.files.map((file, index) => (
                <TabsTrigger key={index} value={index.toString()} className="text-xs">
                  {file.name}
                </TabsTrigger>
              ))}
            </TabsList>

            {selectedTemplate.files.map((file, index) => (
              <TabsContent key={index} value={index.toString()} className="mt-4">
                <div className="relative">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Code className="h-4 w-4" />
                      <span className="font-medium">{file.name}</span>
                      <Badge variant="secondary" className="text-xs">
                        {file.language}
                      </Badge>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(file.content, `${selectedTemplate.id}-${index}`)}
                    >
                      {copiedCode === `${selectedTemplate.id}-${index}` ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto text-sm">
                    <code>{file.content}</code>
                  </pre>
                </div>
              </TabsContent>
            ))}
          </Tabs>

          {/* Setup Instructions */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-semibold mb-2 flex items-center gap-2">
              <Rocket className="h-4 w-4" />
              Quick Setup
            </h4>
            <div className="space-y-2 text-sm">
              <div>
                <strong>1. Install dependencies:</strong>
                <code className="block bg-white px-2 py-1 rounded border mt-1">
                  {selectedTemplate.framework === 'React' && 'npm install rayuela-sdk'}
                  {selectedTemplate.framework === 'Next.js' && 'npm install rayuela-sdk'}
                  {selectedTemplate.framework === 'Flask' && 'pip install rayuela-sdk'}
                </code>
              </div>
              <div>
                <strong>2. Set up environment variables:</strong>
                <code className="block bg-white px-2 py-1 rounded border mt-1">
                  {selectedTemplate.framework.includes('React') || selectedTemplate.framework === 'Next.js'
                    ? 'NEXT_PUBLIC_RAYUELA_API_KEY=ray_your_api_key_here'
                    : 'RAYUELA_API_KEY=ray_your_api_key_here'
                  }
                </code>
              </div>
              <div>
                <strong>3. Copy the files above into your project</strong>
              </div>
              <div>
                <strong>4. Start using recommendations!</strong>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default IntegrationTemplates;
