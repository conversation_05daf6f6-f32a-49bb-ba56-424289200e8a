"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  Users, 
  Code, 
  BookOpen, 
  MessageSquare, 
  Star, 
  Github,
  ExternalLink,
  Search,
  Filter,
  Heart,
  Eye,
  Download,
  Zap,
  Trophy,
  Calendar,
  MapPin,
  Rocket,
  Target,
  TrendingUp
} from "lucide-react";

interface CommunityPost {
  id: string;
  title: string;
  description: string;
  author: {
    name: string;
    avatar: string;
    role: string;
    company: string;
  };
  category: string;
  tags: string[];
  likes: number;
  views: number;
  comments: number;
  createdAt: string;
  featured: boolean;
}

interface CodeExample {
  id: string;
  title: string;
  description: string;
  language: string;
  framework: string;
  useCase: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  author: string;
  downloads: number;
  rating: number;
  code: string;
  tags: string[];
}

interface CaseStudy {
  id: string;
  title: string;
  company: string;
  industry: string;
  challenge: string;
  solution: string;
  results: {
    ctrLift: number;
    cvrLift: number;
    revenueIncrease: string;
  };
  implementation: string;
  author: string;
  featured: boolean;
}

export default function CommunityHub() {
  const [activeTab, setActiveTab] = useState('discussions');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const discussions: CommunityPost[] = [
    {
      id: '1',
      title: 'Best practices for A/B testing recommendations',
      description: 'Share your experience with A/B testing different recommendation strategies. What metrics do you track?',
      author: {
        name: 'Sarah Chen',
        avatar: '👩‍💻',
        role: 'Senior Product Manager',
        company: 'TechCorp'
      },
      category: 'best-practices',
      tags: ['ab-testing', 'metrics', 'optimization'],
      likes: 24,
      views: 156,
      comments: 8,
      createdAt: '2 hours ago',
      featured: true
    },
    {
      id: '2',
      title: 'React integration with real-time updates',
      description: 'How to implement real-time recommendation updates in React apps using WebSockets',
      author: {
        name: 'Mike Rodriguez',
        avatar: '👨‍💻',
        role: 'Frontend Developer',
        company: 'StartupXYZ'
      },
      category: 'technical',
      tags: ['react', 'websockets', 'real-time'],
      likes: 18,
      views: 89,
      comments: 5,
      createdAt: '4 hours ago',
      featured: false
    },
    {
      id: '3',
      title: 'E-commerce conversion optimization case study',
      description: 'How we increased CVR by 35% using personalized product recommendations',
      author: {
        name: 'Alex Thompson',
        avatar: '🧑‍💼',
        role: 'Growth Lead',
        company: 'ShopFast'
      },
      category: 'case-study',
      tags: ['ecommerce', 'conversion', 'personalization'],
      likes: 42,
      views: 234,
      comments: 12,
      createdAt: '1 day ago',
      featured: true
    }
  ];

  const codeExamples: CodeExample[] = [
    {
      id: '1',
      title: 'Next.js E-commerce Integration',
      description: 'Complete Next.js integration with SSR support and caching',
      language: 'TypeScript',
      framework: 'Next.js',
      useCase: 'E-commerce',
      difficulty: 'intermediate',
      author: 'community',
      downloads: 1247,
      rating: 4.8,
      tags: ['nextjs', 'ssr', 'ecommerce', 'typescript'],
      code: `// pages/api/recommendations/[userId].ts
import type { NextApiRequest, NextApiResponse } from 'next';
import Rayuela from 'rayuela-sdk';
import { LRUCache } from 'lru-cache';

const client = new Rayuela({ apiKey: process.env.RAYUELA_API_KEY! });
const cache = new LRUCache({ max: 1000, ttl: 5 * 60 * 1000 });

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { userId } = req.query;
  const cacheKey = \`recs:\${userId}\`;
  
  // Check cache first
  const cached = cache.get(cacheKey);
  if (cached) {
    return res.json({ success: true, data: cached, cached: true });
  }
  
  try {
    const recommendations = await client.ecommerce(userId as string, {
      page: 'homepage',
      limit: 12
    });
    
    cache.set(cacheKey, recommendations);
    res.json({ success: true, data: recommendations, cached: false });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
}`
    },
    {
      id: '2',
      title: 'Python Flask Microservice',
      description: 'Scalable Flask microservice for recommendation serving',
      language: 'Python',
      framework: 'Flask',
      useCase: 'Microservice',
      difficulty: 'advanced',
      author: 'rayuela-team',
      downloads: 892,
      rating: 4.9,
      tags: ['python', 'flask', 'microservice', 'docker'],
      code: `# app.py
from flask import Flask, request, jsonify
from rayuela import Rayuela
import redis
import json
from functools import wraps

app = Flask(__name__)
client = Rayuela(api_key=os.getenv('RAYUELA_API_KEY'))
redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_recommendations(timeout=300):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            cache_key = f"recs:{kwargs.get('user_id', 'anonymous')}"
            cached = redis_client.get(cache_key)
            
            if cached:
                return json.loads(cached)
            
            result = f(*args, **kwargs)
            redis_client.setex(cache_key, timeout, json.dumps(result))
            return result
        return decorated_function
    return decorator

@app.route('/recommendations/<user_id>')
@cache_recommendations(timeout=300)
def get_recommendations(user_id):
    try:
        recommendations = client.recommend(
            user_id=user_id,
            limit=request.args.get('limit', 10, type=int),
            strategy=request.args.get('strategy', 'balanced')
        )
        return jsonify({
            'success': True,
            'data': recommendations.dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500`
    },
    {
      id: '3',
      title: 'React Custom Hook',
      description: 'Reusable React hook for recommendations with loading states',
      language: 'TypeScript',
      framework: 'React',
      useCase: 'Frontend',
      difficulty: 'beginner',
      author: 'community',
      downloads: 2156,
      rating: 4.7,
      tags: ['react', 'hooks', 'typescript', 'frontend'],
      code: `// hooks/useRecommendations.ts
import { useState, useEffect } from 'react';
import Rayuela from 'rayuela-sdk';

const client = new Rayuela({ apiKey: process.env.NEXT_PUBLIC_RAYUELA_API_KEY! });

interface UseRecommendationsOptions {
  limit?: number;
  strategy?: 'balanced' | 'engagement' | 'discovery';
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function useRecommendations(
  userId: string, 
  options: UseRecommendationsOptions = {}
) {
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchRecommendations = async () => {
    if (!userId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await client.recommend(userId, {
        limit: options.limit || 10,
        strategy: options.strategy || 'balanced'
      });
      setRecommendations(result.items);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecommendations();
    
    if (options.autoRefresh && options.refreshInterval) {
      const interval = setInterval(fetchRecommendations, options.refreshInterval);
      return () => clearInterval(interval);
    }
  }, [userId, JSON.stringify(options)]);

  return {
    recommendations,
    loading,
    error,
    refetch: fetchRecommendations
  };
}`
    }
  ];

  const caseStudies: CaseStudy[] = [
    {
      id: '1',
      title: 'Fashion E-commerce: 40% Revenue Increase',
      company: 'StyleHub',
      industry: 'Fashion E-commerce',
      challenge: 'Low product discovery and high bounce rate on category pages',
      solution: 'Implemented personalized recommendations on homepage, category pages, and product detail pages',
      results: {
        ctrLift: 28,
        cvrLift: 35,
        revenueIncrease: '$2.4M annually'
      },
      implementation: 'React frontend with Next.js API routes, A/B tested over 3 months',
      author: 'StyleHub Team',
      featured: true
    },
    {
      id: '2',
      title: 'Media Platform: 60% Engagement Boost',
      company: 'ContentStream',
      industry: 'Media & Entertainment',
      challenge: 'Users consuming limited content, low session duration',
      solution: 'Content recommendations based on viewing history and preferences',
      results: {
        ctrLift: 45,
        cvrLift: 60,
        revenueIncrease: '3.2x subscription retention'
      },
      implementation: 'Python microservices with real-time ML pipeline',
      author: 'ContentStream Engineering',
      featured: true
    },
    {
      id: '3',
      title: 'SaaS Platform: 25% Feature Adoption',
      company: 'ProductivityPro',
      industry: 'SaaS',
      challenge: 'Low feature discovery and user onboarding completion',
      solution: 'Personalized feature recommendations and guided workflows',
      results: {
        ctrLift: 22,
        cvrLift: 25,
        revenueIncrease: '18% MRR increase'
      },
      implementation: 'Vue.js frontend with Django backend integration',
      author: 'ProductivityPro Team',
      featured: false
    }
  ];

  const events = [
    {
      id: '1',
      title: 'Rayuela Developer Meetup',
      date: '2024-09-15',
      location: 'San Francisco, CA',
      type: 'In-person',
      attendees: 45,
      description: 'Monthly meetup for Rayuela developers to share experiences and best practices'
    },
    {
      id: '2',
      title: 'Personalization Best Practices Webinar',
      date: '2024-09-20',
      location: 'Online',
      type: 'Virtual',
      attendees: 234,
      description: 'Learn advanced personalization techniques from industry experts'
    }
  ];

  const leaderboard = [
    { name: 'Sarah Chen', points: 1250, contributions: 23, avatar: '👩‍💻' },
    { name: 'Mike Rodriguez', points: 980, contributions: 18, avatar: '👨‍💻' },
    { name: 'Alex Thompson', points: 875, contributions: 15, avatar: '🧑‍💼' },
    { name: 'Emma Wilson', points: 720, contributions: 12, avatar: '👩‍🔬' },
    { name: 'David Kim', points: 650, contributions: 11, avatar: '👨‍🎨' }
  ];

  return (
    <div className="min-h-screen bg-muted">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-2">
            Rayuela Developer Community 🚀
          </h1>
          <p className="text-xl text-muted-foreground mb-6">
            Connect, learn, and build amazing recommendation experiences together
          </p>
          
          <div className="flex items-center justify-center gap-6 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">2,847</div>
              <div className="text-sm text-muted-foreground">Developers</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">156</div>
              <div className="text-sm text-muted-foreground">Code Examples</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">89</div>
              <div className="text-sm text-muted-foreground">Case Studies</div>
            </div>
          </div>
        </div>

        {/* Search and Filter */}
        <Card className="mb-8">
          <CardContent className="p-4">
            <div className="flex gap-4 items-center">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground/70 h-4 w-4" />
                <Input
                  placeholder="Search discussions, code examples, case studies..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <select 
                className="px-3 py-2 border rounded-md"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                <option value="all">All Categories</option>
                <option value="technical">Technical</option>
                <option value="best-practices">Best Practices</option>
                <option value="case-study">Case Studies</option>
                <option value="beginner">Beginner</option>
              </select>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="discussions">Discussions</TabsTrigger>
                <TabsTrigger value="examples">Code Examples</TabsTrigger>
                <TabsTrigger value="case-studies">Case Studies</TabsTrigger>
                <TabsTrigger value="events">Events</TabsTrigger>
              </TabsList>
              
              {/* Discussions Tab */}
              <TabsContent value="discussions" className="space-y-4">
                {discussions.map((post) => (
                  <Card key={post.id} className={post.featured ? 'border-blue-200 bg-blue-50' : ''}>
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div className="text-2xl">{post.author.avatar}</div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold text-lg">{post.title}</h3>
                            {post.featured && <Badge variant="secondary">Featured</Badge>}
                          </div>
                          <p className="text-muted-foreground mb-3">{post.description}</p>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                            <span>{post.author.name} • {post.author.role} at {post.author.company}</span>
                            <span>{post.createdAt}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="flex gap-2">
                              {post.tags.map((tag) => (
                                <Badge key={tag} variant="outline" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <span className="flex items-center gap-1">
                                <Heart className="h-4 w-4" />
                                {post.likes}
                              </span>
                              <span className="flex items-center gap-1">
                                <Eye className="h-4 w-4" />
                                {post.views}
                              </span>
                              <span className="flex items-center gap-1">
                                <MessageSquare className="h-4 w-4" />
                                {post.comments}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
                
                <Button className="w-full" variant="outline">
                  Load More Discussions
                </Button>
              </TabsContent>

              {/* Code Examples Tab */}
              <TabsContent value="examples" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {codeExamples.map((example) => (
                    <Card key={example.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">{example.title}</CardTitle>
                          <Badge variant={example.difficulty === 'beginner' ? 'secondary' : 
                                        example.difficulty === 'intermediate' ? 'default' : 'destructive'}>
                            {example.difficulty}
                          </Badge>
                        </div>
                        <CardDescription>{example.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div className="flex gap-2">
                            <Badge variant="outline">{example.language}</Badge>
                            <Badge variant="outline">{example.framework}</Badge>
                            <Badge variant="outline">{example.useCase}</Badge>
                          </div>
                          
                          <div className="flex items-center justify-between text-sm text-muted-foreground">
                            <div className="flex items-center gap-4">
                              <span className="flex items-center gap-1">
                                <Download className="h-4 w-4" />
                                {example.downloads}
                              </span>
                              <span className="flex items-center gap-1">
                                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                {example.rating}
                              </span>
                            </div>
                            <span>by {example.author}</span>
                          </div>
                          
                          <div className="flex gap-2">
                            <Button size="sm" className="flex-1">
                              <Code className="h-4 w-4 mr-2" />
                              View Code
                            </Button>
                            <Button size="sm" variant="outline">
                              <Download className="h-4 w-4 mr-2" />
                              Download
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              {/* Case Studies Tab */}
              <TabsContent value="case-studies" className="space-y-4">
                {caseStudies.map((study) => (
                  <Card key={study.id} className={study.featured ? 'border-green-200 bg-green-50' : ''}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-xl">{study.title}</CardTitle>
                        {study.featured && <Badge variant="secondary">Featured</Badge>}
                      </div>
                      <CardDescription>
                        {study.company} • {study.industry}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium mb-2">Challenge</h4>
                          <p className="text-muted-foreground">{study.challenge}</p>
                        </div>
                        
                        <div>
                          <h4 className="font-medium mb-2">Solution</h4>
                          <p className="text-muted-foreground">{study.solution}</p>
                        </div>
                        
                        <div>
                          <h4 className="font-medium mb-3">Results</h4>
                          <div className="grid grid-cols-3 gap-4">
                            <div className="text-center p-3 bg-blue-50 rounded-lg">
                              <div className="text-xl font-bold text-blue-600">+{study.results.ctrLift}%</div>
                              <div className="text-sm text-muted-foreground">CTR Lift</div>
                            </div>
                            <div className="text-center p-3 bg-green-50 rounded-lg">
                              <div className="text-xl font-bold text-green-600">+{study.results.cvrLift}%</div>
                              <div className="text-sm text-muted-foreground">CVR Lift</div>
                            </div>
                            <div className="text-center p-3 bg-purple-50 rounded-lg">
                              <div className="text-lg font-bold text-purple-600">{study.results.revenueIncrease}</div>
                              <div className="text-sm text-muted-foreground">Revenue Impact</div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between pt-4 border-t">
                          <span className="text-sm text-muted-foreground">Implementation: {study.implementation}</span>
                          <Button variant="outline" size="sm">
                            <ExternalLink className="h-4 w-4 mr-2" />
                            Read Full Case Study
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>

              {/* Events Tab */}
              <TabsContent value="events" className="space-y-4">
                {events.map((event) => (
                  <Card key={event.id}>
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                          <Calendar className="h-6 w-6 text-blue-600 mx-auto mb-1" />
                          <div className="text-sm font-medium">
                            {new Date(event.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold text-lg">{event.title}</h3>
                            <Badge variant={event.type === 'Virtual' ? 'secondary' : 'default'}>
                              {event.type}
                            </Badge>
                          </div>
                          <p className="text-muted-foreground mb-3">{event.description}</p>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <MapPin className="h-4 w-4" />
                              {event.location}
                            </span>
                            <span className="flex items-center gap-1">
                              <Users className="h-4 w-4" />
                              {event.attendees} attending
                            </span>
                          </div>
                        </div>
                        <Button>
                          Join Event
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Start Discussion
                </Button>
                <Button variant="outline" className="w-full">
                  <Code className="h-4 w-4 mr-2" />
                  Share Code
                </Button>
                <Button variant="outline" className="w-full">
                  <BookOpen className="h-4 w-4 mr-2" />
                  Submit Case Study
                </Button>
              </CardContent>
            </Card>

            {/* Community Leaderboard */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Trophy className="h-5 w-5 text-yellow-500" />
                  Top Contributors
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {leaderboard.map((user, index) => (
                    <div key={user.name} className="flex items-center gap-3">
                      <div className="text-lg">{user.avatar}</div>
                      <div className="flex-1">
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-muted-foreground">{user.contributions} contributions</div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{user.points}</div>
                        <div className="text-sm text-muted-foreground">points</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Resources */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Resources</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="ghost" className="w-full justify-start">
                  <BookOpen className="h-4 w-4 mr-2" />
                  Documentation
                </Button>
                <Button variant="ghost" className="w-full justify-start">
                  <Github className="h-4 w-4 mr-2" />
                  GitHub Repository
                </Button>
                <Button variant="ghost" className="w-full justify-start">
                  <Zap className="h-4 w-4 mr-2" />
                  API Explorer
                </Button>
                <Button variant="ghost" className="w-full justify-start">
                  <Target className="h-4 w-4 mr-2" />
                  ROI Calculator
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
