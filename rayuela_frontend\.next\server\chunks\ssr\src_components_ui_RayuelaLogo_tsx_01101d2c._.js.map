{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/RayuelaLogo.tsx"], "sourcesContent": ["// src/components/ui/RayuelaLogo.tsx\n\"use client\";\n\nimport React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\n\ninterface RayuelaLogoProps {\n  variant?: 'default' | 'white' | 'dark';\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  showText?: boolean;\n  href?: string;\n  className?: string;\n  priority?: boolean;\n  widthPx?: number;\n  heightPx?: number;\n  textClassName?: string;\n}\n\nconst RayuelaLogo: React.FC<RayuelaLogoProps> = ({\n  variant = 'default',\n  size = 'md',\n  showText = false,\n  href, // no default to avoid nested <a> when parent wraps it\n  className = '',\n  priority = false,\n  widthPx,\n  heightPx,\n  textClassName,\n}) => {\n  // Tamaños del logo (manteniendo aspect ratio de la imagen original)\n  const logoSizes = {\n    sm: { width: 100, height: 32, text: 'text-lg' },\n    md: { width: 125, height: 40, text: 'text-xl' },\n    lg: { width: 150, height: 48, text: 'text-2xl' },\n    xl: { width: 200, height: 64, text: 'text-3xl' }\n  };\n\n  // Colores según variante para el texto adicional (si se usa)\n  const colorClasses = {\n    default: {\n      text: 'text-foreground',\n      accent: 'text-accent'\n    },\n    white: {\n      text: 'text-white',\n      accent: 'text-white/80'\n    },\n    dark: {\n      text: 'text-foreground',\n      accent: 'text-foreground'\n    }\n  };\n\n  const { width, height, text: textSize } = logoSizes[size];\n  const finalWidth = widthPx ?? width;\n  const finalHeight = heightPx ?? height;\n  const colors = colorClasses[variant];\n\n  const logoContent = (\n    <div className={`flex items-center ${className}`}>\n      <Image\n        src=\"/logo-rayuela.png\"\n        alt=\"Rayuela.ai\"\n        width={finalWidth}\n        height={finalHeight}\n        priority={priority}\n        className=\"object-contain\"\n      />\n      {showText && (\n        <div className={`font-bold ${textClassName ?? textSize} ${colors.text} tracking-tight ml-3`}>\n          Rayuela<span className={colors.accent}>.ai</span>\n        </div>\n      )}\n    </div>\n  );\n\n  if (href) {\n    return (\n      <Link\n        href={href}\n        className=\"group transition-transform duration-200 hover:scale-105 inline-flex\"\n      >\n        {logoContent}\n      </Link>\n    );\n  }\n\n  return logoContent;\n};\n\nexport default RayuelaLogo;\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;;AAIpC;AACA;AAJA;;;;AAkBA,MAAM,cAA0C,CAAC,EAC/C,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,IAAI,EACJ,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,OAAO,EACP,QAAQ,EACR,aAAa,EACd;IACC,oEAAoE;IACpE,MAAM,YAAY;QAChB,IAAI;YAAE,OAAO;YAAK,QAAQ;YAAI,MAAM;QAAU;QAC9C,IAAI;YAAE,OAAO;YAAK,QAAQ;YAAI,MAAM;QAAU;QAC9C,IAAI;YAAE,OAAO;YAAK,QAAQ;YAAI,MAAM;QAAW;QAC/C,IAAI;YAAE,OAAO;YAAK,QAAQ;YAAI,MAAM;QAAW;IACjD;IAEA,6DAA6D;IAC7D,MAAM,eAAe;QACnB,SAAS;YACP,MAAM;YACN,QAAQ;QACV;QACA,OAAO;YACL,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,SAAS,CAAC,KAAK;IACzD,MAAM,aAAa,WAAW;IAC9B,MAAM,cAAc,YAAY;IAChC,MAAM,SAAS,YAAY,CAAC,QAAQ;IAEpC,MAAM,4BACJ,8OAAC;QAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW;;0BAC9C,8OAAC,6HAAA,CAAA,UAAK;gBACJ,KAAI;gBACJ,KAAI;gBACJ,OAAO;gBACP,QAAQ;gBACR,UAAU;gBACV,WAAU;;;;;;YAEX,0BACC,8OAAC;gBAAI,WAAW,CAAC,UAAU,EAAE,iBAAiB,SAAS,CAAC,EAAE,OAAO,IAAI,CAAC,oBAAoB,CAAC;;oBAAE;kCACpF,8OAAC;wBAAK,WAAW,OAAO,MAAM;kCAAE;;;;;;;;;;;;;;;;;;IAM/C,IAAI,MAAM;QACR,qBACE,8OAAC,4JAAA,CAAA,UAAI;YACH,MAAM;YACN,WAAU;sBAET;;;;;;IAGP;IAEA,OAAO;AACT;uCAEe", "debugId": null}}]}