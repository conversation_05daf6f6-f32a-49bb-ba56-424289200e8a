// src/app/(public)/login/page.tsx
"use client";

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import LoginForm from '@/components/auth/LoginForm';

export default function LoginPage() {

  return (
    <div className="min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-heading-lg font-bold text-foreground mb-2">
            Iniciar Se<PERSON>
          </h1>
          <p className="text-muted-foreground">
            Accede a tu panel de control de Rayuela.ai
          </p>
        </div>

        <Card className="shadow-soft border-border/50 backdrop-blur-sm bg-card/95">
          <CardContent className="p-6 md:p-8">
            <LoginForm showHeader={false} />
          </CardContent>
        </Card>

        {/* Additional info */}
        <div className="text-center mt-6">
          <p className="text-sm text-muted-foreground">
            ¿No tienes cuenta?{' '}
            <a href="/register" className="text-primary hover:text-primary/80 font-medium underline underline-offset-2">
              Regístrate gratis
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
