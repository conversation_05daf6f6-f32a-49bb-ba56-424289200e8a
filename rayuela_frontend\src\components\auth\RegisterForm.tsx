"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/lib/auth";
import { handleApiError } from "@/lib/error-handler";
import { InitialApiKeyModal } from "./InitialApiKeyModal";

// Esquema de validación
const registerSchema = z.object({
  accountName: z.string().min(1, { message: "Nombre de cuenta es requerido" }),
  email: z.string().email({ message: "Email inválido" }),
  password: z
    .string()
    .min(8, { message: "La contraseña debe tener al menos 8 caracteres" })
    .max(100)
    .regex(/[A-Z]/, { message: "La contraseña debe tener al menos una mayúscula" })
    .regex(/[a-z]/, { message: "La contraseña debe tener al menos una minúscula" })
    .regex(/[0-9]/, { message: "La contraseña debe tener al menos un número" }),
  confirmPassword: z.string(),
}).refine(data => data.password === data.confirmPassword, {
  message: "Las contraseñas no coinciden",
  path: ["confirmPassword"],
});

type RegisterFormValues = z.infer<typeof registerSchema>;

interface RegisterFormProps {
  showHeader?: boolean;
}

export default function RegisterForm({ showHeader = true }: RegisterFormProps) {
  const { register } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [apiKey, setApiKey] = useState<string | null>(null);

  // Inicializar el formulario
  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      accountName: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  // Manejar envío del formulario
  const onSubmit = async (values: RegisterFormValues) => {
    try {
      setIsLoading(true);
      const result = await register(values.accountName, values.email, values.password);

      if (result?.apiKey) {
        // Mostrar modal con la API Key
        setApiKey(result.apiKey);
      } else {
        // Redirigir al dashboard
        router.push("/dashboard");
        toast.success("¡Bienvenido a Rayuela! Revisá tu email para tu API Key y empezá con el Quickstart de 5 min.");
      }
    } catch (error: unknown) {
      handleApiError(error, "Error al registrarse");
    } finally {
      setIsLoading(false);
    }
  };

  const onModalClose = () => {
    setApiKey(null);
    router.push("/dashboard");
  };

  return (
    <>
      <div className="space-y-6">
        {showHeader && (
          <div className="space-y-2 text-center">
            <h1 className="text-3xl font-bold">Crear Cuenta</h1>
            <p className="text-muted-foreground">
              Ingresa tus datos para registrarte
            </p>
          </div>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="accountName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nombre de la Cuenta</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Mi Empresa Inc." className="placeholder-gray-500"
                      type="text"
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="<EMAIL>" className="placeholder-gray-500"
                      type="email"
                      autoComplete="email"
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Contraseña</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="••••••••" className="placeholder-gray-500"
                      type="password"
                      autoComplete="new-password"
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Confirmar Contraseña</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="••••••••" className="placeholder-gray-500"
                      type="password"
                      autoComplete="new-password"
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" className="w-full transition-transform hover:scale-[1.02]" disabled={isLoading}>
              {isLoading ? "Registrando..." : "Registrarse"}
            </Button>
          </form>
        </Form>

        {showHeader && (
          <div className="text-center text-sm">
            ¿Ya tienes una cuenta?{" "}
            <Link
              href="/login"
              className="underline underline-offset-4 hover:text-primary"
            >
              Iniciar Sesión
            </Link>
          </div>
        )}
      </div>

      {apiKey && (
        <InitialApiKeyModal apiKey={apiKey} onContinue={onModalClose} />
      )}
    </>
  );
} 
