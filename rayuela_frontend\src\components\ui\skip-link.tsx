import { cn } from "@/lib/utils";

interface SkipLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
}

/**
 * Componente SkipLink para mejorar la accesibilidad de navegación por teclado
 * Se muestra solo cuando recibe focus, permitiendo a usuarios de teclado
 * saltar directamente al contenido principal
 */
export function SkipLink({ href, children, className }: SkipLinkProps) {
  return (
    <a
      href={href}
      className={cn(
        // Posicionamiento: fuera de la vista por defecto
        "absolute -top-40 left-6 z-[9999]",
        // Estilos cuando recibe focus
        "focus:top-6 focus:bg-primary focus:text-primary-foreground",
        "focus:px-4 focus:py-2 focus:rounded-lg focus:shadow-lg",
        // Transiciones suaves
        "transition-all duration-200 ease-in-out",
        // Estilos base
        "text-sm font-medium outline-none",
        // Focus ring semántico
        "rayuela-focus-ring",
        className
      )}
    >
      {children}
    </a>
  );
}

/**
 * Componente para crear una región landmark accesible
 */
interface LandmarkProps {
  as?: 'main' | 'nav' | 'aside' | 'section' | 'header' | 'footer';
  id?: string;
  'aria-label'?: string;
  'aria-labelledby'?: string;
  children: React.ReactNode;
  className?: string;
}

export function Landmark({ 
  as: Component = 'div', 
  id, 
  'aria-label': ariaLabel,
  'aria-labelledby': ariaLabelledby,
  children, 
  className 
}: LandmarkProps) {
  return (
    <Component
      id={id}
      aria-label={ariaLabel}
      aria-labelledby={ariaLabelledby}
      className={className}
    >
      {children}
    </Component>
  );
}

/**
 * Componente para texto que solo es visible para lectores de pantalla
 */
interface ScreenReaderOnlyProps {
  children: React.ReactNode;
  as?: keyof JSX.IntrinsicElements;
}

export function ScreenReaderOnly({ children, as: Component = 'span' }: ScreenReaderOnlyProps) {
  return (
    <Component className="sr-only">
      {children}
    </Component>
  );
}
