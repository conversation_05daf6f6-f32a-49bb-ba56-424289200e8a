// Ruta EXACTA: rayuela-frontend/src/components/dashboard/Sidebar.tsx
"use client";

import Link from 'next/link';
// Asegúrate de haber añadido 'button' con shadcn: npx shadcn-ui@latest add button
import { Button } from '@/components/ui/button';
import { Logo } from '@/components/ui/logo';
import {
  ExternalLinkIcon,
  MessageSquareIcon,
  HomeIcon,
  KeyIcon,
  ActivityIcon,
  TrendingUpIcon,
  CreditCardIcon,
  BrainIcon,
  SettingsIcon,
  BookOpenIcon,
  CompassIcon,
  CpuIcon
} from 'lucide-react';
import { IconText, Stack } from '@/components/ui/spacing-system';
import { SemanticIcon } from '@/components/ui/icon';

const navItems = [
  { 
    href: '/dashboard', 
    label: 'Dashboard', 
    icon: <SemanticIcon icon={HomeIcon} size="sm" context="navigation" className="rayuela-icon-accent" /> 
  },
  { 
    href: '/api-keys', 
    label: 'API Keys', 
    icon: <SemanticIcon icon={KeyIcon} size="sm" context="navigation" className="rayuela-icon-accent" /> 
  },
  { 
    href: '/pipeline', 
    label: 'Pipeline', 
    icon: <SemanticIcon icon={CpuIcon} size="sm" context="navigation" className="rayuela-icon-primary" /> 
  },
  { 
    href: '/usage', 
    label: 'Usage', 
    icon: <SemanticIcon icon={ActivityIcon} size="sm" context="navigation" className="rayuela-icon-progress" /> 
  },
  { 
    href: '/recommendation-metrics', 
    label: 'Metrics', 
    icon: <SemanticIcon icon={TrendingUpIcon} size="sm" context="navigation" className="rayuela-icon-progress" /> 
  },
  { 
    href: '/billing', 
    label: 'Billing', 
    icon: <SemanticIcon icon={CreditCardIcon} size="sm" context="navigation" className="rayuela-icon-accent" /> 
  },
  { 
    href: '/models', 
    label: 'Models', 
    icon: <SemanticIcon icon={BrainIcon} size="sm" context="navigation" className="rayuela-icon-exploration" /> 
  },
  { 
    href: '/settings', 
    label: 'Settings', 
    icon: <SemanticIcon icon={SettingsIcon} size="sm" context="navigation" className="rayuela-icon-accent" /> 
  },
  { 
    href: 'https://docs.rayuela.ai', 
    label: 'Docs', 
    external: true,
    icon: <SemanticIcon icon={BookOpenIcon} size="sm" context="navigation" className="rayuela-icon-exploration" />
  },
];

export default function Sidebar() {
  return (
    <aside className="w-64 bg-sidebar text-sidebar-foreground border-r border-sidebar-border p-4 flex flex-col h-full shrink-0 rayuela-subtle-gradient">
      <div className="mb-6">
        <Logo href="/dashboard" className="text-sidebar-foreground" />
      </div>
      
      <nav>
        <Stack spacing="sm" className="rayuela-stagger-2">
          {navItems.map((item) => (
            <Button
              key={item.href}
              variant="ghost"
              className="justify-start text-left text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground px-3 rayuela-interactive group"
              asChild
            >
              {item.external ? (
                <a
                  href={item.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-full"
                >
                  <IconText 
                    icon={
                      <div className="flex items-center gap-2">
                        {item.icon}
                        <SemanticIcon icon={ExternalLinkIcon} size="xs" context="navigation" className="opacity-60" />
                      </div>
                    }
                    className="w-full justify-between"
                  >
                    {item.label}
                  </IconText>
                </a>
              ) : (
                <Link href={item.href} className="w-full">
                  <IconText icon={item.icon}>
                    {item.label}
                  </IconText>
                </Link>
              )}
            </Button>
          ))}
        </Stack>
      </nav>

      {/* Spacer para empujar el enlace de feedback al fondo */}
      <div className="flex-grow"></div>

      {/* Enlace de Feedback mejorado */}
      <div className="mt-6 pt-4 border-t border-sidebar-border/50 rayuela-exploration-glow">
        <Button
          variant="ghost"
          size="sm"
          className="w-full justify-start text-sidebar-foreground/80 hover:text-sidebar-foreground hover:bg-sidebar-accent/50 rayuela-interactive group"
          asChild
        >
          <a
            href="mailto:<EMAIL>?subject=Feedback%20sobre%20Rayuela.ai"
            target="_blank"
            rel="noopener noreferrer"
          >
            <IconText 
              icon={<SemanticIcon icon={MessageSquareIcon} size="sm" context="navigation" className="rayuela-icon-exploration" />}
            >
              Feedback
            </IconText>
          </a>
        </Button>
        
        {/* Exploration accent */}
        <div className="mt-3 flex items-center justify-center">
          <div className="flex items-center gap-1 text-xs text-sidebar-foreground/60 group">
            <SemanticIcon icon={CompassIcon} size="xs" context="navigation" className="rayuela-icon-exploration animate-pulse" />
            <span>Explora Rayuela.ai</span>
          </div>
        </div>
      </div>
    </aside>
  );
}
