import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import { useApiKeys } from '@/lib/useApiKeys';
import { getRayuela } from '@/lib/generated/rayuelaAPI';
import { handleApiError } from '@/lib/error-handler';

export function SandboxResetButton() {
  const [isResetting, setIsResetting] = useState(false);
  const { primaryKey: apiKey } = useApiKeys();

  const handleReset = async () => {
    if (!apiKey) {
      toast.error('API Key no encontrada');
      return;
    }

    if (!confirm('¿Estás seguro de que quieres resetear todos los datos del sandbox? Esta acción no se puede deshacer.')) {
      return;
    }

    setIsResetting(true);

    try {
      // Use generated API client with custom instance that handles auth
      const api = getRayuela();
      await api.resetSandboxDataApiV1SandboxResetPost();
      
      toast.success('Datos del sandbox reseteados exitosamente');
      
      // Refresh the page to reflect changes
      window.location.reload();
    } catch (error) {
      handleApiError(error, 'Error al resetear los datos del sandbox');
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <Button
      onClick={handleReset}
      disabled={isResetting || !apiKey}
      variant="destructive"
      size="sm"
      className="gap-2"
    >
      <Trash2 className="h-4 w-4" />
      {isResetting ? 'Reseteando...' : 'Resetear Sandbox'}
    </Button>
  );
} 
