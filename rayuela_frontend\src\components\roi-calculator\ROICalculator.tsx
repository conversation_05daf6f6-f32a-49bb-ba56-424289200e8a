"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Slider } from "@/components/ui/slider";
import { 
  Calculator, 
  TrendingUp, 
  DollarSign, 
  Users, 
  ShoppingCart,
  Download,
  FileText,
  BarChart3,
  Target,
  Zap,
  CheckCircle,
  ArrowRight,
  Building,
  Calendar
} from "lucide-react";

interface CompanyMetrics {
  monthlyRevenue: number;
  monthlyVisitors: number;
  conversionRate: number;
  averageOrderValue: number;
  industry: string;
  companySize: string;
  currentCTR: number;
  currentCVR: number;
}

interface ROIProjection {
  ctrImprovement: number;
  cvrImprovement: number;
  monthlyRevenueIncrease: number;
  annualRevenueIncrease: number;
  implementationCost: number;
  monthlyCost: number;
  annualCost: number;
  roi: number;
  paybackPeriod: number;
  netPresentValue: number;
}

export default function ROICalculator() {
  const [metrics, setMetrics] = useState<CompanyMetrics>({
    monthlyRevenue: 1000000,
    monthlyVisitors: 100000,
    conversionRate: 2.5,
    averageOrderValue: 150,
    industry: 'ecommerce',
    companySize: 'mid-market',
    currentCTR: 3.2,
    currentCVR: 2.5
  });

  const [projection, setProjection] = useState<ROIProjection | null>(null);
  const [selectedScenario, setSelectedScenario] = useState<'conservative' | 'realistic' | 'optimistic'>('realistic');

  const industryBenchmarks = {
    ecommerce: { ctrLift: 0.25, cvrLift: 0.18, baseline: { ctr: 3.2, cvr: 2.5 } },
    media: { ctrLift: 0.35, cvrLift: 0.22, baseline: { ctr: 4.1, cvr: 1.8 } },
    fintech: { ctrLift: 0.20, cvrLift: 0.15, baseline: { ctr: 2.8, cvr: 3.2 } },
    saas: { ctrLift: 0.30, cvrLift: 0.25, baseline: { ctr: 3.5, cvr: 2.1 } },
    marketplace: { ctrLift: 0.28, cvrLift: 0.20, baseline: { ctr: 3.8, cvr: 2.3 } }
  };

  const scenarioMultipliers = {
    conservative: { ctr: 0.6, cvr: 0.6 },
    realistic: { ctr: 1.0, cvr: 1.0 },
    optimistic: { ctr: 1.4, cvr: 1.4 }
  };

  const calculateROI = () => {
    const benchmark = industryBenchmarks[metrics.industry as keyof typeof industryBenchmarks];
    const multiplier = scenarioMultipliers[selectedScenario];
    
    // Calculate improvements
    const ctrImprovement = benchmark.ctrLift * multiplier.ctr;
    const cvrImprovement = benchmark.cvrLift * multiplier.cvr;
    
    // Calculate revenue impact
    const currentMonthlyOrders = (metrics.monthlyVisitors * metrics.currentCTR / 100 * metrics.currentCVR / 100);
    const newCTR = metrics.currentCTR * (1 + ctrImprovement);
    const newCVR = metrics.currentCVR * (1 + cvrImprovement);
    const newMonthlyOrders = (metrics.monthlyVisitors * newCTR / 100 * newCVR / 100);
    
    const monthlyRevenueIncrease = (newMonthlyOrders - currentMonthlyOrders) * metrics.averageOrderValue;
    const annualRevenueIncrease = monthlyRevenueIncrease * 12;
    
    // Calculate costs
    const implementationCost = metrics.companySize === 'enterprise' ? 25000 : 
                              metrics.companySize === 'mid-market' ? 10000 : 5000;
    const monthlyCost = metrics.companySize === 'enterprise' ? 2000 : 
                       metrics.companySize === 'mid-market' ? 800 : 300;
    const annualCost = monthlyCost * 12 + implementationCost;
    
    // Calculate ROI metrics
    const roi = (annualRevenueIncrease - annualCost) / annualCost;
    const paybackPeriod = implementationCost / monthlyRevenueIncrease;
    const netPresentValue = annualRevenueIncrease * 3 - annualCost * 3; // 3-year NPV simplified
    
    setProjection({
      ctrImprovement,
      cvrImprovement,
      monthlyRevenueIncrease,
      annualRevenueIncrease,
      implementationCost,
      monthlyCost,
      annualCost,
      roi,
      paybackPeriod,
      netPresentValue
    });
  };

  useEffect(() => {
    calculateROI();
  }, [metrics, selectedScenario]);

  const generateBusinessCase = () => {
    if (!projection) return '';
    
    return `# Business Case for Rayuela Recommendations
## Executive Summary

**Company:** ${metrics.companySize === 'enterprise' ? 'Enterprise' : metrics.companySize === 'mid-market' ? 'Mid-Market' : 'Growth'} ${metrics.industry} company
**Current Performance:** $${metrics.monthlyRevenue.toLocaleString()}/month revenue, ${metrics.monthlyVisitors.toLocaleString()} monthly visitors
**Projected Impact:** $${projection.annualRevenueIncrease.toLocaleString()} annual revenue increase

## Current State Analysis
- Monthly Revenue: $${metrics.monthlyRevenue.toLocaleString()}
- Monthly Visitors: ${metrics.monthlyVisitors.toLocaleString()}
- Current CTR: ${metrics.currentCTR}%
- Current CVR: ${metrics.currentCVR}%
- Average Order Value: $${metrics.averageOrderValue}

## Projected Improvements (${selectedScenario} scenario)
- CTR Improvement: +${(projection.ctrImprovement * 100).toFixed(1)}%
- CVR Improvement: +${(projection.cvrImprovement * 100).toFixed(1)}%
- Monthly Revenue Increase: $${projection.monthlyRevenueIncrease.toLocaleString()}
- Annual Revenue Increase: $${projection.annualRevenueIncrease.toLocaleString()}

## Investment & Returns
- Implementation Cost: $${projection.implementationCost.toLocaleString()}
- Monthly Cost: $${projection.monthlyCost.toLocaleString()}
- Annual Cost: $${projection.annualCost.toLocaleString()}
- **ROI: ${(projection.roi * 100).toFixed(0)}%**
- **Payback Period: ${projection.paybackPeriod.toFixed(1)} months**
- **3-Year NPV: $${projection.netPresentValue.toLocaleString()}**

## Risk Mitigation
- Conservative estimates based on industry benchmarks
- Gradual rollout with A/B testing
- 30-day money-back guarantee
- Dedicated customer success manager

## Implementation Timeline
- Week 1-2: Setup and integration
- Week 3-4: A/B testing and optimization
- Month 2: Full rollout
- Month 3+: Ongoing optimization and scaling

## Recommendation
Based on the analysis, implementing Rayuela recommendations will generate significant ROI with minimal risk. The ${projection.paybackPeriod.toFixed(1)}-month payback period and ${(projection.roi * 100).toFixed(0)}% annual ROI make this a compelling investment.

**Next Steps:**
1. Approve budget allocation
2. Schedule technical integration call
3. Begin 30-day pilot program
4. Scale based on results

---
*Generated on ${new Date().toLocaleDateString()} using Rayuela ROI Calculator*`;
  };

  const downloadBusinessCase = () => {
    const businessCase = generateBusinessCase();
    const blob = new Blob([businessCase], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `rayuela-business-case-${new Date().toISOString().split('T')[0]}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-muted p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-2">
            ROI Calculator & Business Case Generator
          </h1>
          <p className="text-xl text-muted-foreground">
            Calculate the business impact of Rayuela recommendations for your company
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Panel */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Company Information
                </CardTitle>
                <CardDescription>
                  Tell us about your business to get accurate projections
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Industry</Label>
                    <select 
                      className="w-full p-2 border rounded-md mt-1"
                      value={metrics.industry}
                      onChange={(e) => setMetrics({...metrics, industry: e.target.value})}
                    >
                      <option value="ecommerce">E-commerce</option>
                      <option value="media">Media & Content</option>
                      <option value="fintech">Fintech</option>
                      <option value="saas">SaaS</option>
                      <option value="marketplace">Marketplace</option>
                    </select>
                  </div>
                  <div>
                    <Label>Company Size</Label>
                    <select 
                      className="w-full p-2 border rounded-md mt-1"
                      value={metrics.companySize}
                      onChange={(e) => setMetrics({...metrics, companySize: e.target.value})}
                    >
                      <option value="startup">Startup</option>
                      <option value="mid-market">Mid-Market</option>
                      <option value="enterprise">Enterprise</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Current Performance Metrics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label>Monthly Revenue: ${metrics.monthlyRevenue.toLocaleString()}</Label>
                  <Slider
                    value={[metrics.monthlyRevenue]}
                    onValueChange={([value]) => setMetrics({...metrics, monthlyRevenue: value})}
                    max={10000000}
                    min={10000}
                    step={10000}
                    className="mt-2"
                  />
                </div>
                
                <div>
                  <Label>Monthly Visitors: {metrics.monthlyVisitors.toLocaleString()}</Label>
                  <Slider
                    value={[metrics.monthlyVisitors]}
                    onValueChange={([value]) => setMetrics({...metrics, monthlyVisitors: value})}
                    max={1000000}
                    min={1000}
                    step={1000}
                    className="mt-2"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Current CTR: {metrics.currentCTR}%</Label>
                    <Slider
                      value={[metrics.currentCTR]}
                      onValueChange={([value]) => setMetrics({...metrics, currentCTR: value})}
                      max={10}
                      min={0.5}
                      step={0.1}
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <Label>Current CVR: {metrics.currentCVR}%</Label>
                    <Slider
                      value={[metrics.currentCVR]}
                      onValueChange={([value]) => setMetrics({...metrics, currentCVR: value})}
                      max={10}
                      min={0.5}
                      step={0.1}
                      className="mt-2"
                    />
                  </div>
                </div>
                
                <div>
                  <Label>Average Order Value: ${metrics.averageOrderValue}</Label>
                  <Slider
                    value={[metrics.averageOrderValue]}
                    onValueChange={([value]) => setMetrics({...metrics, averageOrderValue: value})}
                    max={1000}
                    min={10}
                    step={5}
                    className="mt-2"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Projection Scenario</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-2">
                  {(['conservative', 'realistic', 'optimistic'] as const).map((scenario) => (
                    <Button
                      key={scenario}
                      variant={selectedScenario === scenario ? 'default' : 'outline'}
                      onClick={() => setSelectedScenario(scenario)}
                      className="capitalize"
                    >
                      {scenario}
                    </Button>
                  ))}
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  {selectedScenario === 'conservative' && 'Lower-bound estimates for risk-averse planning'}
                  {selectedScenario === 'realistic' && 'Industry-average improvements based on benchmarks'}
                  {selectedScenario === 'optimistic' && 'Upper-bound potential with optimal implementation'}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Results Panel */}
          <div className="space-y-6">
            {projection && (
              <>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5" />
                      Projected Performance Improvements
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">
                          +{(projection.ctrImprovement * 100).toFixed(1)}%
                        </div>
                        <div className="text-sm text-muted-foreground">CTR Improvement</div>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          +{(projection.cvrImprovement * 100).toFixed(1)}%
                        </div>
                        <div className="text-sm text-muted-foreground">CVR Improvement</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <DollarSign className="h-5 w-5" />
                      Revenue Impact
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                        <span className="font-medium">Monthly Increase</span>
                        <span className="text-xl font-bold text-green-600">
                          ${projection.monthlyRevenueIncrease.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-green-100 rounded-lg">
                        <span className="font-medium">Annual Increase</span>
                        <span className="text-2xl font-bold text-green-700">
                          ${projection.annualRevenueIncrease.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Calculator className="h-5 w-5" />
                      Investment & ROI
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span>Implementation Cost</span>
                        <span className="font-medium">${projection.implementationCost.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Monthly Cost</span>
                        <span className="font-medium">${projection.monthlyCost.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Annual Cost</span>
                        <span className="font-medium">${projection.annualCost.toLocaleString()}</span>
                      </div>
                      <hr />
                      <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                        <span className="font-medium">ROI</span>
                        <span className="text-xl font-bold text-purple-600">
                          {(projection.roi * 100).toFixed(0)}%
                        </span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                        <span className="font-medium">Payback Period</span>
                        <span className="text-xl font-bold text-blue-600">
                          {projection.paybackPeriod.toFixed(1)} months
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="h-5 w-5" />
                      Key Metrics Summary
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 gap-3">
                      <div className="flex items-center justify-between p-2 border rounded">
                        <span className="text-sm">3-Year NPV</span>
                        <Badge variant="outline" className="text-green-600">
                          ${projection.netPresentValue.toLocaleString()}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between p-2 border rounded">
                        <span className="text-sm">Break-even</span>
                        <Badge variant="outline">
                          Month {Math.ceil(projection.paybackPeriod)}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between p-2 border rounded">
                        <span className="text-sm">Risk Level</span>
                        <Badge variant="outline" className="text-green-600">
                          Low
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Business Case
                    </CardTitle>
                    <CardDescription>
                      Generate a comprehensive business case for stakeholders
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="p-4 bg-muted rounded-lg">
                        <h4 className="font-medium mb-2">Executive Summary</h4>
                        <p className="text-sm text-muted-foreground">
                          Implementing Rayuela recommendations will generate 
                          <strong> ${projection.annualRevenueIncrease.toLocaleString()} annual revenue increase</strong> with 
                          <strong> {(projection.roi * 100).toFixed(0)}% ROI</strong> and 
                          <strong> {projection.paybackPeriod.toFixed(1)}-month payback period</strong>.
                        </p>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button onClick={downloadBusinessCase} className="flex-1">
                          <Download className="h-4 w-4 mr-2" />
                          Download Business Case
                        </Button>
                        <Button variant="outline">
                          <Calendar className="h-4 w-4 mr-2" />
                          Schedule Demo
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>
        </div>

        {/* Industry Benchmarks */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Industry Benchmarks & Methodology</CardTitle>
            <CardDescription>
              Our projections are based on real customer data and industry benchmarks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-medium mb-3">Data Sources</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• 500+ customer implementations</li>
                  <li>• Industry benchmark studies</li>
                  <li>• A/B test results database</li>
                  <li>• Third-party research reports</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-3">Conservative Assumptions</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• 6-month ramp-up period</li>
                  <li>• 80% of theoretical maximum</li>
                  <li>• Seasonal variations included</li>
                  <li>• Implementation challenges factored</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-3">Risk Mitigation</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• 30-day money-back guarantee</li>
                  <li>• Gradual rollout strategy</li>
                  <li>• Dedicated success manager</li>
                  <li>• Performance monitoring tools</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
