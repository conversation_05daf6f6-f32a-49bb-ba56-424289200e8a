'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import {
  KeyIcon,
  BookOpenIcon,
  DatabaseIcon,
  SparklesIcon,
  XIcon,
  ExternalLinkIcon,
  CheckCircleIcon,
  RefreshCwIcon,
  ArrowRightIcon,
  Copy,
  Code,
  Check,
  HelpCircle,
} from 'lucide-react';
import Link from 'next/link';
import { useAuth, useAccountInfo, useUsageSummary, useApiKeys, useIngestionJobs, useTrainingJobs } from '@/lib/hooks';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { ChecklistItem } from '@/types/checklist';
import { IconText } from '@/components/ui/spacing-system';
import {
  analyzeChecklistCompletion,
  createChecklistState,
  shouldHighlightChecklist,
} from '@/lib/analysis';
// Removed unused Progress import

// Componente para mostrar snippets de código
interface CodeSnippetProps {
  title: string;
  language: string;
  code: string;
  apiKey?: string;
}

const CodeSnippet: React.FC<CodeSnippetProps> = ({ title, language, code, apiKey }) => {
  const [copied, setCopied] = useState(false);

  // Reemplazar placeholder de API Key si está disponible
  const finalCode = apiKey ? code.replace(/YOUR_API_KEY/g, apiKey) : code;

  const handleCopy = () => {
    navigator.clipboard
      .writeText(finalCode)
      .then(() => {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      })
      .catch((err) => {
        console.error('Error al copiar:', err);
      });
  };

  return (
    <div className="mt-3 ml-6 border rounded-md bg-muted/50">
      <div className="flex items-center justify-between px-3 py-2 border-b border-border">
        <div className="flex items-center">
          <Code className="h-3 w-3 mr-1 text-muted-foreground" />
          <span className="text-xs font-medium text-foreground">{title}</span>
          <Badge variant="outline" className="ml-2 text-[10px] px-1 py-0 h-4">
            {language}
          </Badge>
        </div>
        <Button variant="ghost" size="sm" onClick={handleCopy} className="h-6 px-2 text-xs">
          {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
        </Button>
      </div>
      <pre className="p-3 text-xs overflow-x-auto">
        <code className="text-foreground dark:text-gray-200">{finalCode}</code>
      </pre>
    </div>
  );
};

// Agregar TooltipHelper component
const TooltipHelper: React.FC<{ content: string }> = ({ content }) => (
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger asChild>
        <HelpCircle className="h-3 w-3 text-muted-foreground hover:text-foreground cursor-help ml-1" />
      </TooltipTrigger>
      <TooltipContent>
        <p className="max-w-xs text-xs">{content}</p>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
);

const GettingStartedChecklist: React.FC = () => {
  const { apiKey } = useAuth();
  
  // Agregar hooks para detección automática
  const { jobs: ingestionJobs } = useIngestionJobs();
  const { jobs: trainingJobs } = useTrainingJobs();
  
  // Get API base URL for the code snippets
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001/api/v1';

  // Snippets de código para diferentes tareas con endpoints correctos
  const codeSnippets = {
    catalog: {
      curl: `curl -X POST "${apiBaseUrl}/ingestion/batch" \\
  -H "X-API-Key: YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "products": [
      {
        "external_id": "P001",
        "name": "Smartphone XYZ",
        "category": "Electronics",
        "price": 599.99,
        "description": "Un smartphone de última generación",
        "image_url": "https://ejemplo.com/images/p001.jpg"
      }
    ]
  }'`,
      python: `import requests

url = "${apiBaseUrl}/ingestion/batch"
headers = {
    "X-API-Key": "YOUR_API_KEY",
    "Content-Type": "application/json"
}
data = {
    "products": [
        {
            "external_id": "P001",
            "name": "Smartphone XYZ",
            "category": "Electronics",
            "price": 599.99,
            "description": "Un smartphone de última generación",
            "image_url": "https://ejemplo.com/images/p001.jpg"
        }
    ]
}

response = requests.post(url, headers=headers, json=data)
print(response.json())`,
      javascript: `const response = await fetch('${apiBaseUrl}/ingestion/batch', {
  method: 'POST',
  headers: {
    'X-API-Key': 'YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    products: [
      {
        external_id: 'P001',
        name: 'Smartphone XYZ',
        category: 'Electronics',
        price: 599.99,
        description: 'Un smartphone de última generación',
        image_url: 'https://ejemplo.com/images/p001.jpg'
      }
    ]
  })
});

const data = await response.json();
console.log(data);`,
    },
    interactions: {
      curl: `curl -X POST "${apiBaseUrl}/ingestion/batch" \\
  -H "X-API-Key: YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "interactions": [
      {
        "user_external_id": "U001",
        "product_external_id": "P001",
        "interaction_type": "view",
        "timestamp": "2024-01-15T10:30:00Z"
      }
    ]
  }'`,
      python: `import requests
from datetime import datetime

url = "${apiBaseUrl}/ingestion/batch"
headers = {
    "X-API-Key": "YOUR_API_KEY",
    "Content-Type": "application/json"
}
data = {
    "interactions": [
        {
            "user_external_id": "U001",
            "product_external_id": "P001",
            "interaction_type": "view",
            "timestamp": datetime.now().isoformat() + "Z"
        }
    ]
}

response = requests.post(url, headers=headers, json=data)
print(response.json())`,
      javascript: `const response = await fetch('${apiBaseUrl}/ingestion/batch', {
  method: 'POST',
  headers: {
    'X-API-Key': 'YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    interactions: [
      {
        user_external_id: 'U001',
        product_external_id: 'P001',
        interaction_type: 'view',
        timestamp: new Date().toISOString()
      }
    ]
  })
});

const data = await response.json();
console.log(data);`,
    },
    recommendations: {
      curl: `curl -X POST "${apiBaseUrl}/recommendations/personalized/query" \\
  -H "X-API-Key: YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "user_id": 123,
    "limit": 5,
    "strategy": "balanced",
    "model_type": "standard"
  }'`,
      python: `import requests

url = "${apiBaseUrl}/recommendations/personalized/query"
headers = {
    "X-API-Key": "YOUR_API_KEY",
    "Content-Type": "application/json"
}
data = {
    "user_id": 123,
    "limit": 5,
    "strategy": "balanced",
    "model_type": "standard"
}

response = requests.post(url, headers=headers, json=data)
recommendations = response.json()
print(recommendations)`,
      javascript: `const response = await fetch('${apiBaseUrl}/recommendations/personalized/query', {
  method: 'POST',
  headers: {
    'X-API-Key': 'YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    user_id: 123,
    limit: 5,
    strategy: 'balanced',
    model_type: 'standard'
  })
});

const recommendations = await response.json();
console.log(recommendations);`,
    },
  };

  const [isLoading, setIsLoading] = useState(true);
  const [dismissed, setDismissed] = useState(false);
  const [checklistItems, setChecklistItems] = useState<ChecklistItem[]>([
    {
      id: 'generate_key',
      label: 'He Guardado mi API Key',
      description: 'Confirma que has guardado tu API Key de forma segura. 🧪 En el Developer Sandbox puedes resetear todo y generar nuevas claves cuando necesites.',
      icon: <KeyIcon className="h-4 w-4 text-info" />,
      link: '/api-keys',
      docsLink: 'https://docs.rayuela.ai/authentication',
      docsTitle: 'Guía de Autenticación',
      completed: false,
      autoDetect: false,
      tooltipContent: 'Las API Keys te permiten autenticar solicitudes de forma segura. En el Developer Sandbox puedes experimentar sin costos.',
      category: 'setup',
    },
    {
      id: 'read_docs',
      label: 'Consultar Documentación',
      description: 'Lee la guía de inicio rápido para aprender a usar la API',
      icon: <BookOpenIcon className="h-4 w-4 text-success" />,
      link: 'https://docs.rayuela.ai/quickstart',
      docsLink: 'https://docs.rayuela.ai/api-reference',
      docsTitle: 'Referencia de API',
      isExternal: true,
      completed: false,
      tooltipContent: 'Familiarízate con los conceptos básicos y la estructura de la API',
      category: 'setup',
    },
    {
      id: 'send_catalog_data',
      label: 'Ingresar Datos de Productos',
      description: 'Usa el endpoint /ingestion/batch para cargar tus productos. Código listo para copiar incluido abajo.',
      icon: <DatabaseIcon className="h-4 w-4 text-purple-500" />,
      link: 'https://docs.rayuela.ai/guides/data_ingestion_guide',
      docsLink: 'https://docs.rayuela.ai/guides/data_ingestion_guide',
      docsTitle: 'Guía de Ingesta de Datos',
      isExternal: true,
      completed: false,
      autoDetect: true,
      tooltipContent: 'Los datos de productos son la base del catálogo. Sin productos, no hay nada que recomendar.',
      category: 'data',
    },
    {
      id: 'send_interaction_data',
      label: 'Ingresar Datos de Interacciones',
      description: 'Usa el endpoint /ingestion/batch para cargar interacciones usuario-producto. Código listo para copiar incluido abajo.',
      icon: <DatabaseIcon className="h-4 w-4 text-indigo-500" />,
      link: 'https://docs.rayuela.ai/guides/data_ingestion_guide#interacciones-interactions',
      docsLink: 'https://docs.rayuela.ai/guides/data_ingestion_guide',
      docsTitle: 'Guía de Ingesta de Datos',
      isExternal: true,
      completed: false,
      autoDetect: true,
      tooltipContent: 'Las interacciones (vistas, compras, clicks) son fundamentales para personalizar las recomendaciones según el comportamiento del usuario.',
      category: 'data',
    },
    {
      id: 'train_model',
      label: 'Entrenar Primer Modelo',
      description: 'Inicia el entrenamiento de tu modelo de recomendación',
      icon: <SparklesIcon className="h-4 w-4 text-orange-500" />,
      link: 'https://docs.rayuela.ai/quickstart#entrenamiento-de-modelo',
      docsLink: 'https://docs.rayuela.ai/models',
      docsTitle: 'Guía de Entrenamiento',
      isExternal: true,
      completed: false,
      autoDetect: true,
      tooltipContent: 'Una vez cargados tus datos, entrena tu primer modelo para comenzar a generar recomendaciones personalizadas.',
      category: 'model',
    },
    {
      id: 'first_recommendation',
      label: 'Obtener Recomendaciones',
      description: 'Usa el endpoint /recommendations/personalized/query para obtener recomendaciones. Código listo para copiar incluido abajo.',
      icon: <SparklesIcon className="h-4 w-4 text-warning" />,
      link: 'https://docs.rayuela.ai/quickstart#obtención-de-recomendaciones',
      docsLink: 'https://docs.rayuela.ai/recommendations',
      docsTitle: 'Guía de Recomendaciones',
      isExternal: true,
      completed: false,
      autoDetect: true,
      tooltipContent: 'Una vez que tienes datos, puedes obtener recomendaciones personalizadas para cualquier usuario de tu plataforma.',
      category: 'model',
    },
  ]);

  // Cargar el estado del checklist desde el backend (con fallback a localStorage)
  useEffect(() => {
    if (accountData && getChecklistStatus) {
      try {
        const backendStatus = getChecklistStatus();

        // Si hay estado en el backend, usarlo
        if (backendStatus && Object.keys(backendStatus).length > 0) {
          setChecklistItems((prev) =>
            prev.map((item) => ({
              ...item,
              completed: Boolean(backendStatus[item.id]) || false,
            })),
          );

          // También verificar si está dismissed en el backend
          if (backendStatus.dismissed) {
            setDismissed(true);
          }
        } else {
          // Si no hay estado en el backend, usar localStorage como fallback
          const savedChecklist = localStorage.getItem('gettingStartedChecklist');
          if (savedChecklist) {
            const parsedChecklist = JSON.parse(savedChecklist);
            setChecklistItems((prev) =>
              prev.map((item) => ({
                ...item,
                completed: Boolean(parsedChecklist[item.id]) || false,
              })),
            );
          }

          const isDismissed = localStorage.getItem('dismissedChecklist') === 'true';
          setDismissed(isDismissed);
        }
      } catch (e) {
        console.error('Error loading checklist state:', e);
        // Fallback a localStorage en caso de error
        const savedChecklist = localStorage.getItem('gettingStartedChecklist');
        if (savedChecklist) {
          try {
            const parsedChecklist = JSON.parse(savedChecklist);
            setChecklistItems((prev) =>
              prev.map((item) => ({
                ...item,
                completed: Boolean(parsedChecklist[item.id]) || false,
              })),
            );
          } catch (e2) {
            console.error('Error parsing saved checklist from localStorage:', e2);
          }
        }
      }
    }
  }, []);

  // Usar hooks personalizados para obtener datos con revalidación más agresiva para el checklist
  const {
    accountData,
    error: accountError,
    isLoading: isAccountLoading,
    refresh: mutateAccount,
    getChecklistStatus,
    updateChecklistStatus,
  } = useAccountInfo({
    revalidateOnFocus: true,
    dedupingInterval: 5000, // 5 segundos (más agresivo)
    refreshInterval: 15000, // 15 segundos (más frecuente)
  });

  // Hook para gestionar API Keys (using consolidated hook)
  useApiKeys({
    revalidateOnFocus: true,
    dedupingInterval: 5000,
    refreshInterval: 15000,
  });

  // Efecto para cargar el estado del checklist después de que accountData esté disponible
  useEffect(() => {
    if (accountData && getChecklistStatus) {
      // Código del useEffect anterior aquí
      const loadChecklistState = async () => {
        try {
          const checklistStatus = await getChecklistStatus();
          if (checklistStatus && typeof checklistStatus === 'object') {
            setChecklistItems((prev) =>
              prev.map((item) => ({
                ...item,
                completed: Boolean(checklistStatus[item.id]) || false,
              })),
            );
          }

          const isDismissed = localStorage.getItem('dismissedChecklist') === 'true';
          setDismissed(isDismissed);
        } catch (e) {
          console.error('Error loading checklist state:', e);
          // Fallback a localStorage en caso de error
          const savedChecklist = localStorage.getItem('gettingStartedChecklist');
          if (savedChecklist) {
            try {
              const parsedChecklist = JSON.parse(savedChecklist);
              setChecklistItems((prev) =>
                prev.map((item) => ({
                  ...item,
                  completed: parsedChecklist[item.id] || false,
                })),
              );
            } catch (e2) {
              console.error('Error parsing saved checklist from localStorage:', e2);
            }
          }
        }
      };
      loadChecklistState();
    }
  }, [accountData, getChecklistStatus]);

  // Usar hook personalizado para obtener datos de uso con revalidación más agresiva
  const {
    data: usageData,
    error: usageError,
    isLoading: isUsageLoading,
    refetch: mutateUsage,
  } = useUsageSummary();

  // Actualizar el estado del checklist cuando cambian los datos
  useEffect(() => {
    if (accountData || usageData) {
      updateChecklistState();
    }
  }, [accountData, usageData, updateChecklistState]);

  // Función para actualizar manualmente los datos
  const refreshData = async () => {
    try {
      await Promise.all([mutateAccount(), mutateUsage()]);
    } catch (error) {
      console.error('Error al actualizar los datos:', error);
    }
  };

  // Función para actualizar el estado del checklist basado en los datos actuales
  const updateChecklistState = useCallback(async () => {
    if (!accountData || !usageData) return;

    // Detección automática mejorada
    const hasCompletedIngestion = ingestionJobs?.some(job => job.status === 'completed') || false;
    const hasCompletedTraining = trainingJobs?.some(job => job.status === 'completed') || false;
    
    const {
      updatedItems,
      isNewApiKey,
      hasApiKey,
      hasSentCatalogData,
      hasSentInteractionData,
      hasTrainedModel,
      hasMadeApiCalls,
    } = analyzeChecklistCompletion(accountData, usageData, checklistItems, {
      hasCompletedIngestion,
      hasCompletedTraining
    });

    // Verificar si el usuario ha visto el PostModalHighlight
    const hasSeenPostModal = localStorage.getItem('seenPostModalHighlight') === 'true';

    // Actualizar el estado del checklist en memoria
    setChecklistItems(updatedItems);

    // Construir el objeto de estado para persistir
    const checklistState = createChecklistState(
      updatedItems,
      hasApiKey,
      hasSeenPostModal,
      hasSentCatalogData,
      hasSentInteractionData,
      hasTrainedModel,
      hasMadeApiCalls,
    );

    // Persistir PRIMARIAMENTE en el backend
    if (updateChecklistStatus) {
      try {
        await updateChecklistStatus(checklistState);
      } catch (error) {
        // En caso de fallo, registramos el error y continuamos con el fallback local
        console.error('Error updating checklist status in backend:', error);
      }
    }

    // Guardar también en localStorage como backup/fallback
    localStorage.setItem('gettingStartedChecklist', JSON.stringify(checklistState));

    // Si el usuario acaba de generar una API Key (según PostModalHighlight o timestamp reciente),
    // y no ha descartado el checklist, asegurarse de que sea visible
    if ((hasSeenPostModal || isNewApiKey) && !localStorage.getItem('dismissedChecklist')) {
      localStorage.removeItem('dismissedChecklist');
      setDismissed(false);
    }

    // Actualizar el estado de carga
    setIsLoading(false);
  }, [accountData, usageData, checklistItems, ingestionJobs, trainingJobs, updateChecklistStatus]);

  // Efecto para actualizar el estado del checklist cuando cambian los datos
  useEffect(() => {
    if (accountData && usageData) {
      updateChecklistState();
    } else if (!isAccountLoading && !isUsageLoading) {
      setIsLoading(false);
    }
  }, [accountData, usageData, isAccountLoading, isUsageLoading, updateChecklistState]);

  const handleToggleItem = async (id: string) => {
    // Actualizar el estado local de los items
    const updatedItems = checklistItems.map((item) =>
      item.id === id ? { ...item, completed: !item.completed } : item,
    );

    setChecklistItems(updatedItems);

    // Construir el estado del checklist para persistir
    const checklistState: Record<string, boolean> = {};
    updatedItems.forEach((item) => {
      checklistState[item.id] = item.completed;
    });

    // Persistir en backend como fuente de verdad
    if (updateChecklistStatus) {
      try {
        await updateChecklistStatus(checklistState);
      } catch (error) {
        console.error('Error updating checklist status in backend:', error);
      }
    }

    // Independientemente del resultado, guardar una copia en localStorage
    localStorage.setItem('gettingStartedChecklist', JSON.stringify(checklistState));
  };

  const handleDismiss = async () => {
    setDismissed(true);

    // Guardar en localStorage inmediatamente
    localStorage.setItem('dismissedChecklist', 'true');

    // Persistir en el backend que el checklist está dismissed
    if (updateChecklistStatus) {
      try {
        const currentStatus = getChecklistStatus?.() || {};
        await updateChecklistStatus({
          ...currentStatus,
          dismissed: true,
        });
      } catch (error) {
        console.error('Error updating dismissed status in backend:', error);
        // Si falla el backend, al menos tenemos localStorage
      }
    }
  };

  if (dismissed) {
    return null;
  }

  const completedCount = checklistItems.filter((item) => item.completed).length;
  const totalCount = checklistItems.length;
  const progress = Math.round((completedCount / totalCount) * 100);

  // Verificar si el usuario acaba de generar una API Key
  const hasSeenPostModal = localStorage.getItem('seenPostModalHighlight') === 'true';

  // Usar la función de utilidad para determinar si el checklist debe destacarse
  const isHighlighted = shouldHighlightChecklist(hasSeenPostModal, false);

  // Si es la primera vez que se muestra después de generar una API Key, marcar como destacado
  if (isHighlighted) {
    localStorage.setItem('checklistHighlighted', 'true');
  }

  return (
    <Card
      className={`mb-6 border-2 ${isHighlighted
        ? 'border-indigo-400 dark:border-indigo-500 shadow-lg animate-pulse'
        : 'border-indigo-200 dark:border-indigo-800/40 shadow-md hover:shadow-lg'
        } transition-all duration-300 ${completedCount < totalCount ? 'ring-2 ring-indigo-100 dark:ring-indigo-900/20' : ''
        }`}
    >
      <CardHeader
        className={`pb-2 ${isHighlighted
          ? 'bg-indigo-50 dark:bg-indigo-900/20'
          : completedCount < totalCount
            ? 'bg-gradient-to-r from-indigo-50 to-transparent dark:from-indigo-900/10 dark:to-transparent'
            : ''
          }`}
      >
        <div className="flex justify-between items-start">
          <div>
            <CardTitle accent className="text-lg flex items-center">
              <span
                className={`${isHighlighted
                  ? 'bg-indigo-200 dark:bg-indigo-800'
                  : 'bg-indigo-100 dark:bg-indigo-900/50'
                  } p-1.5 rounded-md mr-2 transition-colors`}
              >
                <ChecklistIcon className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
              </span>
              {isHighlighted ? '¡Comienza Aquí!' : 'Primeros Pasos'}
            </CardTitle>
            <CardDescription className="flex items-center justify-between">
              <span>
                {isHighlighted
                  ? 'Sigue estos pasos para comenzar a usar tu nueva API Key'
                  : 'Completa estas tareas para comenzar a usar Rayuela'}
              </span>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 text-indigo-500 hover:text-indigo-700 hover:bg-indigo-50 -mr-2"
                      onClick={refreshData}
                      disabled={isAccountLoading || isUsageLoading}
                    >
                      <RefreshCwIcon
                        className={`h-3.5 w-3.5 ${isAccountLoading || isUsageLoading ? 'animate-spin' : ''}`}
                      />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">Actualizar progreso</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </CardDescription>
          </div>
          <div className="flex gap-1">
            {!isLoading && accountData && usageData && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Badge
                      variant="outline"
                      className="text-[10px] px-1.5 py-0 h-5 bg-muted text-muted-foreground"
                    >
                      Actualizado:{' '}
                      {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">Última verificación de progreso</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}

            <Button
              variant="ghost"
              size="icon"
              className="text-muted-foreground/70 hover:text-muted-foreground"
              onClick={handleDismiss}
            >
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Progreso</span>
            <span className="text-sm text-muted-foreground">
              {completedCount}/{totalCount} completados
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 overflow-hidden">
            <div
              className={`h-2.5 rounded-full transition-all duration-500 ease-in-out ${
                progress === 100
                  ? 'bg-gradient-to-r from-green-500 to-emerald-500 dark:from-green-600 dark:to-emerald-600'
                  : progress > 50
                  ? 'bg-gradient-to-r from-blue-500 to-indigo-500 dark:from-blue-600 dark:to-indigo-600'
                  : 'bg-gradient-to-r from-gray-400 to-gray-500 dark:from-gray-500 dark:to-gray-600'
              }`}
              style={{ width: `${progress}%` }}
            />
          </div>

          {/* Agregar felicitación cuando se complete todo */}
          {progress === 100 && (
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 p-4 rounded-lg text-center mt-4">
              <div className="flex items-center justify-center mb-2">
                <CheckCircleIcon className="h-6 w-6 text-green-600 dark:text-green-400 mr-2" />
                <span className="text-green-800 dark:text-green-200 font-medium">
                  🎉 ¡Felicitaciones! Has completado la configuración inicial de Rayuela.
                </span>
              </div>
              <p className="text-sm text-green-700 dark:text-green-300">
                Ya puedes empezar a obtener recomendaciones personalizadas para tus usuarios.
              </p>
            </div>
          )}

          {(isAccountLoading || isUsageLoading) && (
            <div className="flex justify-center py-2 mb-4 mt-2">
              <IconText icon={<RefreshCwIcon className="h-4 w-4 animate-spin text-indigo-500" />}>
                <span className="text-xs text-muted-foreground">Verificando progreso...</span>
              </IconText>
            </div>
          )}

          {(accountError || usageError) && (
            <div className="bg-red-50 dark:bg-red-900/20 border-l-2 border-red-500 pl-3 py-2 mb-4 mt-2">
              <p className="text-xs text-red-600 dark:text-red-400">
                Error al verificar el progreso.
                <button
                  onClick={refreshData}
                  className="ml-2 underline hover:text-red-700 dark:hover:text-red-300"
                >
                  Reintentar
                </button>
              </p>
            </div>
          )}

          {/* Agrupar los elementos por categoría */}
          <div className="space-y-6 mt-4">
            {/* Sección de Configuración */}
            <div>
              <h3 className="text-sm font-semibold text-foreground mb-2">
                <IconText 
                  icon={
                    <span className="bg-blue-100 dark:bg-blue-900/50 p-1 rounded-md">
                      <KeyIcon className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
                    </span>
                  }
                >
                  Configuración Inicial
                </IconText>
              </h3>
              <ul className="space-y-3">
                {checklistItems
                  .filter((item) => item.category === 'setup')
                  .map((item) => (
                    <ChecklistItemComponent
                      key={item.id}
                      item={item}
                      onToggle={handleToggleItem}
                      onRefresh={refreshData}
                      apiKey={apiKey || undefined}
                      codeSnippets={codeSnippets}
                      colorScheme="blue"
                    />
                  ))}
              </ul>
            </div>

            {/* Sección de Datos */}
            <div>
              <h3 className="text-sm font-semibold text-foreground mb-2 flex items-center">
                <span className="bg-purple-100 dark:bg-purple-900/50 p-1 rounded-md mr-2">
                  <DatabaseIcon className="h-3.5 w-3.5 text-purple-600 dark:text-purple-400" />
                </span>
                Carga de Datos
              </h3>
              <ul className="space-y-3">
                {checklistItems
                  .filter((item) => item.category === 'data')
                  .map((item) => (
                    <ChecklistItemComponent
                      key={item.id}
                      item={item}
                      onToggle={handleToggleItem}
                      onRefresh={refreshData}
                      apiKey={apiKey || undefined}
                      codeSnippets={codeSnippets}
                      colorScheme="purple"
                    />
                  ))}
              </ul>
            </div>

            {/* Sección de Modelo y Recomendaciones */}
            <div>
              <h3 className="text-sm font-semibold text-foreground mb-2 flex items-center">
                <span className="bg-amber-100 dark:bg-amber-900/50 p-1 rounded-md mr-2">
                  <SparklesIcon className="h-3.5 w-3.5 text-amber-600 dark:text-amber-400" />
                </span>
                Modelo y Recomendaciones
              </h3>
              <ul className="space-y-3">
                {checklistItems
                  .filter((item) => item.category === 'model')
                  .map((item) => (
                    <ChecklistItemComponent
                      key={item.id}
                      item={item}
                      onToggle={handleToggleItem}
                      onRefresh={refreshData}
                      apiKey={apiKey || undefined}
                      codeSnippets={codeSnippets}
                      colorScheme="amber"
                    />
                  ))}
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Componente para cada elemento del checklist
interface ChecklistItemComponentProps {
  item: ChecklistItem;
  onToggle: (id: string) => void;
  onRefresh: () => void;
  apiKey?: string;
  codeSnippets: {
    catalog: { curl: string; python: string; javascript: string };
    interactions: { curl: string; python: string; javascript: string };
    recommendations: { curl: string; python: string; javascript: string };
  };
  colorScheme: 'blue' | 'purple' | 'amber';
}

const ChecklistItemComponent: React.FC<ChecklistItemComponentProps> = ({
  item,
  onToggle,
  onRefresh,
  apiKey,
  codeSnippets,
  colorScheme,
}) => {
  const colorClasses = {
    blue: {
      bg: 'bg-info-light/50',
      checkbox: 'bg-info border-info',
      badge: 'bg-info-light',
      link: 'text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300',
    },
    purple: {
      bg: 'bg-purple-50/50 dark:bg-purple-900/10',
      checkbox: 'bg-purple-600 border-purple-600',
      badge: 'bg-purple-50 dark:bg-purple-900/30',
      link: 'text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300',
    },
    amber: {
      bg: 'bg-warning-light/50',
      checkbox: 'bg-warning border-warning',
      badge: 'bg-warning-light',
      link: 'text-amber-600 hover:text-amber-700 dark:text-amber-400 dark:hover:text-amber-300',
    },
  };

  const colors = colorClasses[colorScheme];

  return (
    <li
      className={`flex items-start p-2 rounded-md transition-colors ${item.completed ? colors.bg : 'hover:bg-muted/50/50'
        }`}
    >
      <div className="flex h-5 items-center mr-3">
        <Checkbox
          id={item.id}
          checked={item.completed}
          onCheckedChange={() => onToggle(item.id)}
          className={item.completed ? colors.checkbox : ''}
        />
      </div>
      <div className="flex-1">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {item.icon}
            <label
              htmlFor={item.id}
              className={`ml-2 text-sm font-medium ${item.completed ? 'text-muted-foreground' : 'text-foreground dark:text-gray-100'}`}
            >
              {item.label}
            </label>
            {/* Agregar tooltip helper */}
            {item.tooltipContent && <TooltipHelper content={item.tooltipContent} />}
          </div>
          {item.completed && (
            <Badge variant="secondary" className={colors.badge}>
              <CheckCircleIcon className="h-3 w-3 mr-1" />
              Completado
            </Badge>
          )}
        </div>

        <p className="text-xs text-muted-foreground mt-0.5 ml-6">{item.description}</p>

        <div className="mt-2 ml-6 flex items-center">
          {item.isExternal ? (
            <a
              href={item.link}
              target="_blank"
              rel="noopener noreferrer"
              className={`text-xs ${colors.link} font-medium flex items-center`}
            >
              <ExternalLinkIcon className="h-3 w-3 mr-1" />
              Ver guía paso a paso
            </a>
          ) : (
            <Link
              href={item.link}
              className={`text-xs ${colors.link} font-medium flex items-center`}
            >
              <ArrowRightIcon className="h-3 w-3 mr-1" />
              Ir a {item.label}
            </Link>
          )}

          {item.autoDetect && !item.completed && (
            <button
              onClick={onRefresh}
              className="ml-3 text-xs text-muted-foreground hover:text-foreground dark:text-muted-foreground/70 dark:hover:text-muted-foreground/50 flex items-center"
              title="Verificar progreso"
            >
              <RefreshCwIcon className="h-3 w-3 mr-1" />
              Verificar
            </button>
          )}
        </div>

        {/* Snippets de código para elementos específicos */}
        {item.id === 'send_catalog_data' && (
          <div className="space-y-2">
            <CodeSnippet
              title="Cargar productos (cURL)"
              language="bash"
              code={codeSnippets.catalog.curl}
              apiKey={apiKey}
            />
            <CodeSnippet
              title="Cargar productos (Python)"
              language="python"
              code={codeSnippets.catalog.python}
              apiKey={apiKey}
            />
            <CodeSnippet
              title="Cargar productos (JavaScript)"
              language="javascript"
              code={codeSnippets.catalog.javascript}
              apiKey={apiKey}
            />
          </div>
        )}

        {item.id === 'send_interaction_data' && (
          <div className="space-y-2">
            <CodeSnippet
              title="Cargar interacciones (cURL)"
              language="bash"
              code={codeSnippets.interactions.curl}
              apiKey={apiKey}
            />
            <CodeSnippet
              title="Cargar interacciones (Python)"
              language="python"
              code={codeSnippets.interactions.python}
              apiKey={apiKey}
            />
            <CodeSnippet
              title="Cargar interacciones (JavaScript)"
              language="javascript"
              code={codeSnippets.interactions.javascript}
              apiKey={apiKey}
            />
          </div>
        )}

        {item.id === 'first_recommendation' && (
          <div className="space-y-2">
            <CodeSnippet
              title="Obtener recomendaciones (cURL)"
              language="bash"
              code={codeSnippets.recommendations.curl}
              apiKey={apiKey}
            />
            <CodeSnippet
              title="Obtener recomendaciones (Python)"
              language="python"
              code={codeSnippets.recommendations.python}
              apiKey={apiKey}
            />
            <CodeSnippet
              title="Obtener recomendaciones (JavaScript)"
              language="javascript"
              code={codeSnippets.recommendations.javascript}
              apiKey={apiKey}
            />
          </div>
        )}
      </div>
    </li>
  );
};

// Componente de icono de checklist
const ChecklistIcon = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <line x1="10" y1="6" x2="21" y2="6"></line>
    <line x1="10" y1="12" x2="21" y2="12"></line>
    <line x1="10" y1="18" x2="21" y2="18"></line>
    <polyline points="3 6 4 7 6 5"></polyline>
    <polyline points="3 12 4 13 6 11"></polyline>
    <polyline points="3 18 4 19 6 17"></polyline>
  </svg>
);

export default GettingStartedChecklist;
