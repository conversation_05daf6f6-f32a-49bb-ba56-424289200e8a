import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { Check, Star } from "lucide-react";
import { Container, Stack, IconText } from '@/components/ui/spacing-system';

export const metadata = generateSEOMetadata({
  title: 'Inversión Estratégica en Ventaja Competitiva - Planes Rayuela.ai',
  description: 'La decisión que separa líderes de seguidores. Planes diseñados para CTOs que entienden que la personalización IA no es un gasto, es poder de mercado.',
  path: '/pricing',
  keywords: ['inversión estratégica', 'ventaja competitiva', 'personalización enterprise', 'liderazgo tecnológico', 'poder de mercado'],
});

const plans = [
  {
    id: "sandbox",
    name: "Developer Sandbox",
    price: "Gratis",
    period: "para siempre",
    description: "Explorá el potencial de nuestra API sin costo y con tus propios datos",
    features: [
      "1,000 Llamadas API de Recomendación/mes",
      "10 MB de almacenamiento",
      "Modelos básicos de recomendación",
      "Entrenamiento manual (1/mes)",
      "Analíticas básicas",
      "Soporte por email estándar",
      "🧪 Ideal para validar el impacto",
      "🔄 Reset de datos incluido"
    ],
    cta: "Probar Gratis",
    href: "/register",
    popular: false
  },
  {
    id: "starter",
    name: "Starter",
    price: "ARS $49.000",
    period: "/mes",
    description: "Funcionalidades esenciales para comenzar a personalizar tu catálogo con IA",
    features: [
      "100,000 Llamadas API de Recomendación/mes",
      "1 GB de almacenamiento",
      "Modelos básicos e híbridos",
      "Entrenamiento mensual automático",
      "Analíticas básicas",
      "Webhooks y callbacks",
      "Soporte por email prioritario"
    ],
    cta: "Elegir Starter",
    href: "/register",
    popular: false
  },
  {
    id: "pro",
    name: "Pro",
    price: "ARS $199.000",
    period: "/mes",
    description: "Capacidades avanzadas para escalar personalización y análisis",
    features: [
      "500,000 Llamadas API de Recomendación/mes",
      "5 GB de almacenamiento",
      "Todos los modelos (LTR, XAI)",
      "Entrenamiento semanal automático",
      "Analíticas detalladas",
      "Streaming Data Ingest",
      "Soporte chat/email rápido"
    ],
    cta: "Escalar Ahora",
    href: "/register",
    popular: true
  },
  {
    id: "enterprise",
    name: "Enterprise",
    price: "Precio personalizado",
    period: "",
    description: "Arquitectura dedicada y soporte experto para requisitos empresariales",
    features: [
      "Llamadas API ilimitadas",
      "Almacenamiento ilimitado",
      "Todos los modelos + personalizados",
      "Entrenamiento bajo demanda/continuo",
      "Analíticas avanzadas + consultoría",
      "Infraestructura dedicada",
      "Integración personalizada",
      "Soporte dedicado 24/7",
      "SLA 99.99%"
    ],
    cta: "Contactar Ventas",
    href: "/contact-sales",
    popular: false
  }
];

// Helper functions for styling
const getPriceStyles = (plan: typeof plans[0]) => {
  if (plan.id === 'sandbox') return 'text-success text-3xl';
  if (plan.id === 'enterprise') return 'text-accent text-2xl';
  return 'text-primary text-4xl';
};

const getButtonStyles = (plan: typeof plans[0]) => {
  if (plan.popular) return 'bg-primary hover:bg-primary/90 shadow-glow-primary text-white';
  if (plan.id === 'sandbox') return 'bg-success hover:bg-success/90 text-white border-success';
  if (plan.id === 'enterprise') return 'bg-accent hover:bg-accent/90 text-white border-accent';
  return '';
};

const getButtonVariant = (plan: typeof plans[0]) => {
  if (plan.popular || plan.id === 'sandbox' || plan.id === 'enterprise') return 'default';
  return 'outline';
};

export default function PricingPage() {
  const offerSchema = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: 'Rayuela Recommendation API',
    description: 'Sistema de recomendaciones como servicio',
    offers: plans.map(plan => ({
      '@type': 'Offer',
      name: plan.name,
      description: plan.description,
      price: plan.price === 'Gratis' ? '0' : plan.price.replace('$', ''),
      priceCurrency: 'ARS',
      availability: 'https://schema.org/InStock',
    })),
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(offerSchema),
        }}
      />

      <div className="min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end">
        <Container spacing="loose" maxWidth="full">
          <Stack spacing="xl">
            {/* Hero Section */}
            <div className="text-center">
              <Stack spacing="lg" className="relative">
                <div className="inline-flex items-center gap-2 bg-primary/10 border border-primary/20 rounded-full px-6 py-3 mb-10">
                  <span className="text-primary font-semibold text-sm">
                    Planes flexibles para cada etapa
                  </span>
                </div>

                <h1 className="text-display-lg md:text-display-xl text-foreground leading-extra-tight">
                  <span className="font-extrabold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                    Planes que Crecen
                  </span>
                  <br />
                  <span className="font-light text-muted-foreground">
                    con tu Negocio
                  </span>
                </h1>

                <p className="text-body-lg text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                  <strong className="text-foreground">Desplegá personalización con IA en días, no meses.</strong>{' '}
                  Desde una prueba sin costo hasta una arquitectura dedicada, elegí exactamente lo que tu equipo necesita.
                </p>

                {/* Value Proposition */}
                <div className="bg-gradient-to-r from-primary/10 to-accent/10 rounded-lg p-6 max-w-4xl mx-auto mt-8">
                  <p className="text-body-medium text-foreground mb-4">
                    <strong>💡 Valor Cuantificable:</strong> Nuestras recomendaciones personalizadas en tiempo real aumentan la conversión,
                    el ticket promedio y el engagement de tus usuarios.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="text-center">
                      <div className="text-success font-semibold">📈 +15-30%</div>
                      <div className="text-muted-foreground">Conversión</div>
                    </div>
                    <div className="text-center">
                      <div className="text-success font-semibold">💰 +20-40%</div>
                      <div className="text-muted-foreground">Ticket Promedio</div>
                    </div>
                    <div className="text-center">
                      <div className="text-success font-semibold">⚡ +25-50%</div>
                      <div className="text-muted-foreground">Engagement</div>
                    </div>
                  </div>
                </div>

              </Stack>
            </div>

            {/* Pricing Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {plans.map((plan) => (
              <Card key={plan.id} className={`relative h-full transition-all duration-300 hover:scale-105 ${
                plan.popular
                  ? 'border-primary shadow-glow-primary scale-105 bg-gradient-to-br from-primary/5 to-accent/5'
                  : 'border-border hover:border-primary/50 hover:shadow-medium'
              }`}>
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                    <Badge className="bg-gradient-to-r from-primary to-accent text-white px-6 py-2 shadow-glow-primary">
                      <IconText icon={<Star className="w-4 h-4" />} size="sm">
                        🏆 Más Popular
                      </IconText>
                    </Badge>
                  </div>
                )}

                {plan.id === 'sandbox' && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                    <Badge className="bg-gradient-to-r from-gradient-semantic-success-start to-gradient-semantic-success-end text-white px-6 py-2 shadow-soft">
                      <IconText icon={<span>🚀</span>} size="sm">
                        Empezar Aquí
                      </IconText>
                    </Badge>
                  </div>
                )}

                {plan.id === 'enterprise' && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                    <Badge className="bg-gradient-to-r from-gradient-semantic-brand-start to-gradient-semantic-brand-end text-white px-6 py-2 shadow-soft">
                      <IconText icon={<span>👑</span>} size="sm">
                        Enterprise
                      </IconText>
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-6 pt-12">
                  <CardTitle className="text-heading-lg font-bold text-foreground mb-4">
                    {plan.name}
                  </CardTitle>
                  <div className="mb-4">
                    <div className="flex items-baseline justify-center gap-1">
                      <span className={`font-bold ${getPriceStyles(plan)}`}>
                        {plan.price}
                      </span>
                      {plan.period && (
                        <span className="text-muted-foreground text-sm">
                          {plan.period}
                        </span>
                      )}
                    </div>
                    {plan.id === 'sandbox' && (
                      <div className="text-xs text-success font-semibold mt-1">
                        Ideal para validar ROI
                      </div>
                    )}
                    {plan.id === 'pro' && (
                      <div className="text-xs text-primary font-semibold mt-1">
                        Para equipos en crecimiento
                      </div>
                    )}
                  </div>
                  <CardDescription className="text-muted-foreground leading-relaxed">
                    {plan.description}
                  </CardDescription>
                </CardHeader>

                <CardContent className="flex-1 pt-0">
                  <Stack spacing="sm" className="mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <IconText
                        key={`${plan.id}-feature-${featureIndex}`}
                        icon={<Check className="w-4 h-4 text-success flex-shrink-0" />}
                        align="start"
                        size="sm"
                      >
                        <span className="text-foreground text-sm leading-relaxed">{feature}</span>
                      </IconText>
                    ))}
                  </Stack>

                  <Button
                    asChild
                    className={`w-full font-semibold ${getButtonStyles(plan)} ${plan.id === 'sandbox' ? 'bg-success text-white' : ''}`}
                    variant={getButtonVariant(plan)}
                    size="lg"
                  >
                    <Link href={plan.href} className="flex items-center justify-center gap-2">
                      {plan.id === 'sandbox' && '🚀'}
                      {plan.id === 'pro' && '⚡'}
                      {plan.id === 'enterprise' && '👑'}
                      {plan.cta}
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Pricing FAQ */}
          <div className="bg-card rounded-lg p-8 shadow-lg">
            <h2 className="text-heading-large text-center text-foreground mb-8">
              Preguntas Frecuentes sobre Precios
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    ¿Qué sucede si excedo mi límite?
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    Recibirás notificaciones automáticas al alcanzar el 80% de tu límite. Si lo excedes,
                    las solicitudes se pausarán temporalmente hasta que actualices tu plan o el próximo ciclo de facturación.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    ¿Puedo cambiar de plan en cualquier momento?
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    Sí, puedes actualizar o degradar tu plan desde tu dashboard en cualquier momento.
                    Los cambios se aplican inmediatamente.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    ¿Qué métodos de pago aceptan?
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    Aceptamos todas las tarjetas de crédito y débito a través de Mercado Pago,
                    incluyendo transferencias bancarias y otros métodos locales.
                  </p>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    ¿Cómo se calculan los costos de almacenamiento?
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    El almacenamiento incluye todos tus datos: productos, usuarios, interacciones y modelos entrenados.
                    Se mide el total de datos almacenados en tu cuenta.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    ¿Qué tan seguido se entrenan los modelos?
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    Sandbox: Manual, Starter: Mensual, Pro: Semanal, Enterprise: Bajo demanda.
                    Entrenamientos más frecuentes mejoran la precisión de las recomendaciones.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    ¿Los precios pueden cambiar?
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    Los precios en ARS pueden ajustarse periódicamente debido a la inflación.
                    Te notificaremos con 30 días de anticipación sobre cualquier cambio.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Strategic Investment Section */}
          <div className="bg-card rounded-lg p-8 shadow-lg">
            <h2 className="text-heading-large text-center text-foreground mb-8">
              Decisiones Estratégicas Inteligentes
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-subheading text-gray-900 dark:text-white mb-2">
                  ¿Por qué no construir esto internamente?
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Desarrollar un motor de recomendaciones robusto suele demandar meses de trabajo y perfiles de machine learning difíciles de contratar. Rayuela te brinda esa capacidad lista para usar, de modo que tu equipo pueda centrarse en estrategia y experiencia de cliente.
                </p>
              </div>

              <div>
                <h3 className="text-subheading text-gray-900 dark:text-white mb-2">
                  ¿Qué ROI puedo esperar?
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  En proyectos recientes, las empresas que usan Rayuela han logrado incrementos de 25-40 % en conversión y 20-35 % en ticket promedio. El retorno estimado ronda 300-500 % en los primeros seis meses.
                </p>
              </div>

              <div>
                <h3 className="text-subheading text-gray-900 dark:text-white mb-2">
                  ¿Cómo me aseguro de que funcione para mi negocio?
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  El <strong>Developer Sandbox</strong> es gratis para siempre. Prueba con tus datos reales, ve el impacto, y luego escala. Los planes pagos incluyen 30 días de prueba sin riesgo. Validamos el poder antes de la inversión.
                </p>
              </div>

              <div>
                <h3 className="text-subheading text-gray-900 dark:text-white mb-2">
                  ¿Qué pasa si mis competidores también lo usan?
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Nuestro programa de early adopters está limitado para ofrecer acompañamiento cercano y aprendizaje compartido. Cada implementación pionera nos ayuda a mejorar la plataforma para toda la comunidad.
                </p>
              </div>
            </div>
          </div>
          </Stack>
        </Container>
      </div>
    </>
  );
}
