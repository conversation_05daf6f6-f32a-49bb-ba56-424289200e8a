import * as React from "react"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "./card"
import { <PERSON><PERSON> } from "./button"
import { Input } from "./input"
import { Badge } from "./badge"
import { Alert, AlertDescription, AlertTitle } from "./alert"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "./tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "./table"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "./accordion"
import { Skeleton } from "./skeleton"
import { CheckCircle, AlertTriangle, Info, Activity } from "lucide-react"

export function UIImprovementsShowcase() {
  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-display font-bold">
          UI Components Improvements
        </h1>
        <p className="text-body text-muted-foreground max-w-2xl mx-auto">
          Showcasing the implemented improvements for consistency, visual harmony, and better user experience.
        </p>
      </div>

      {/* Consistent Border Radius Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-success" />
            Consistent Border Radius
          </CardTitle>
          <CardDescription>
            Unified rounded-lg styling across all interactive components for visual harmony.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button>Primary Button</Button>
            <Button variant="outline">Outline Button</Button>
            <Button variant="secondary">Secondary</Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input placeholder="Input with rounded-lg" />
            <div className="flex gap-2">
              <Badge>Default</Badge>
              <Badge variant="success">Success</Badge>
              <Badge variant="warning">Warning</Badge>
            </div>
          </div>
          <Tabs defaultValue="tab1" className="w-full">
            <TabsList>
              <TabsTrigger value="tab1">Tab One</TabsTrigger>
              <TabsTrigger value="tab2">Tab Two</TabsTrigger>
            </TabsList>
            <TabsContent value="tab1">Content for tab one</TabsContent>
            <TabsContent value="tab2">Content for tab two</TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Enhanced Hover/Active States */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-info" />
            Enhanced Interactive States
          </CardTitle>
          <CardDescription>
            Improved hover and active states with subtle animations and better feedback.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Button variant="default">Hover me</Button>
            <Button variant="destructive">Delete</Button>
            <Badge variant="outline-success" className="cursor-pointer">Interactive Badge</Badge>
            <Badge variant="info" className="cursor-pointer">Info Badge</Badge>
          </div>
          <p className="text-caption text-muted-foreground">
            Try hovering and clicking the elements above to see the enhanced states.
          </p>
        </CardContent>
      </Card>

      {/* Semantic Alerts */}
      <div className="space-y-4">
        <h2 className="text-heading">Semantic Alert Components</h2>
        <div className="space-y-3">
          <Alert variant="success">
            <CheckCircle className="h-4 w-4" />
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>
              Your changes have been saved successfully with subtle hover effects.
            </AlertDescription>
          </Alert>
          
          <Alert variant="warning">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Warning</AlertTitle>
            <AlertDescription>
              Please review your input before continuing.
            </AlertDescription>
          </Alert>
          
          <Alert variant="info">
            <Info className="h-4 w-4" />
            <AlertTitle>Information</AlertTitle>
            <AlertDescription>
              This feature uses the harmonized oklch color palette.
            </AlertDescription>
          </Alert>
        </div>
      </div>

      {/* Improved Table */}
      <Card>
        <CardHeader>
          <CardTitle>Enhanced Table Design</CardTitle>
          <CardDescription>
            Subtle borders, better hover states, and improved visual hierarchy.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Feature</TableHead>
                <TableHead>Before</TableHead>
                <TableHead>After</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[
                { feature: "Border Radius", before: "Mixed (xl/md)", after: "Consistent (lg)" },
                { feature: "Hover States", before: "Basic", after: "Enhanced + Active" },
                { feature: "Borders", before: "Hard borders", after: "Subtle + Shadows" }
              ].map((item, index) => (
                <TableRow key={item.feature} index={index}>
                  <TableCell className="font-medium">{item.feature}</TableCell>
                  <TableCell>{item.before}</TableCell>
                  <TableCell>{item.after}</TableCell>
                  <TableCell><Badge variant="success">Improved</Badge></TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Improved Accordion */}
      <Card>
        <CardHeader>
          <CardTitle>Refined Accordion</CardTitle>
          <CardDescription>
            Better hover states and subtle visual improvements.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Accordion type="single" collapsible>
            <AccordionItem value="item-1">
              <AccordionTrigger>Improved Hover Effects</AccordionTrigger>
              <AccordionContent>
                The accordion now has subtle hover backgrounds and better focus states.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-2">
              <AccordionTrigger>Subtle Border Improvements</AccordionTrigger>
              <AccordionContent>
                Borders are now more subtle and the last item doesn't have a bottom border.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-3">
              <AccordionTrigger>Visual Hierarchy</AccordionTrigger>
              <AccordionContent>
                Better contrast between content and trigger states.
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>

      {/* Enhanced Skeletons */}
      <Card>
        <CardHeader>
          <CardTitle>Enhanced Loading Skeletons</CardTitle>
          <CardDescription>
            Improved animation with gradient shimmer effect and consistent styling.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-4 w-2/3" />
          </div>
          <div className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[200px]" />
              <Skeleton className="h-4 w-[150px]" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 
