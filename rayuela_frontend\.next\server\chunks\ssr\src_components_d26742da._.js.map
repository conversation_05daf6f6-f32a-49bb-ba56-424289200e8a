{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, elevation = \"soft\", ...props }: React.ComponentProps<\"div\"> & { elevation?: \"none\" | \"sm\" | \"soft\" | \"medium\" | \"glow\" }) {\n  const shadowMap: Record<string, string> = {\n    none: \"shadow-none\",\n    sm: \"shadow-sm\",\n    soft: \"shadow-soft\",\n    medium: \"shadow-medium\",\n    glow: \"shadow-glow\",\n  }\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-lg border\",\n        shadowMap[elevation] ?? \"shadow-soft\",\n        \"rayuela-card-gradient rayuela-card-hover\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, accent = false, ...props }: React.ComponentProps<\"div\"> & { accent?: boolean }) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"text-subheading\", accent ? \"rayuela-accent\" : \"text-foreground\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-caption\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,YAAY,MAAM,EAAE,GAAG,OAAiG;IACjJ,MAAM,YAAoC;QACxC,MAAM;QACN,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM;IACR;IACA,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA,SAAS,CAAC,UAAU,IAAI,eACxB,4CACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,SAAS,KAAK,EAAE,GAAG,OAA2D;IAC5G,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB,SAAS,mBAAmB,mBAAmB;QAC/E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC7B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-lg border bg-transparent px-3 py-1 text-base shadow-soft rayuela-interactive rayuela-focus-ring outline-none hover:border-ring/50 file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,meACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-lg border border-input bg-background px-3 py-2 text-sm shadow-soft rayuela-interactive rayuela-focus-ring hover:border-ring/50 ring-offset-background placeholder:text-muted-foreground rayuela-interactive rayuela-focus-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50 transition-transform group-data-[state=open]:rotate-180\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-lg border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-md py-1.5 pl-8 pr-2 text-sm outline-none transition-colors hover:bg-accent/50 focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8VACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kQACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all hover:bg-background/50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8ZACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 active:scale-95\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80 active:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 active:bg-destructive/90\",\n        success:\n          \"border-transparent bg-success text-success-foreground hover:bg-success/80 active:bg-success/90 dark:bg-success/20 dark:text-success dark:border-success/40\",\n        warning:\n          \"border-transparent bg-warning text-warning-foreground hover:bg-warning/80 active:bg-warning/90 dark:bg-warning/20 dark:text-warning dark:border-warning/40\",\n        info:\n          \"border-transparent bg-info text-info-foreground hover:bg-info/80 active:bg-info/90 dark:bg-info/20 dark:text-info dark:border-info/40\",\n        outline: \"text-foreground hover:bg-accent hover:text-accent-foreground\",\n        \"outline-success\": \"border-success/40 text-success hover:bg-success/15 active:bg-success/25 dark:border-success/50 dark:hover:bg-success/20\",\n        \"outline-warning\": \"border-warning/40 text-warning hover:bg-warning/15 active:bg-warning/25 dark:border-warning/50 dark:hover:bg-warning/20\",\n        \"outline-info\": \"border-info/40 text-info hover:bg-info/15 active:bg-info/25 dark:border-info/50 dark:hover:bg-info/20\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,uLACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;YACF,SACE;YACF,MACE;YACF,SAAS;YACT,mBAAmB;YACnB,mBAAmB;YACnB,gBAAgB;QAClB;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/api-explorer/CodeGenerator.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from \"@/components/ui/tabs\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { \n  Copy, \n  CheckCircle, \n  Download, \n  Code2, \n  FileText,\n  Zap,\n  Package\n} from \"lucide-react\";\n\ninterface CodeTemplate {\n  id: string;\n  name: string;\n  description: string;\n  language: string;\n  framework?: string;\n  category: 'basic' | 'integration' | 'production';\n}\n\ninterface GeneratorProps {\n  endpoint: any;\n  parameters: Record<string, any>;\n  apiKey: string;\n}\n\nconst CODE_TEMPLATES: CodeTemplate[] = [\n  // JavaScript Templates\n  {\n    id: 'js-basic',\n    name: 'Basic JavaScript',\n    description: 'Simple fetch request',\n    language: 'javascript',\n    category: 'basic'\n  },\n  {\n    id: 'js-sdk',\n    name: '<PERSON><PERSON><PERSON>',\n    description: 'Using official SDK (recommended)',\n    language: 'javascript',\n    category: 'basic'\n  },\n  {\n    id: 'js-react',\n    name: 'React Hook',\n    description: 'Custom React hook for recommendations',\n    language: 'javascript',\n    framework: 'react',\n    category: 'integration'\n  },\n  {\n    id: 'js-nextjs',\n    name: 'Next.js API Route',\n    description: 'Server-side API route',\n    language: 'javascript',\n    framework: 'nextjs',\n    category: 'integration'\n  },\n  {\n    id: 'js-production',\n    name: 'Production Ready',\n    description: 'With error handling, retry logic, and caching',\n    language: 'javascript',\n    category: 'production'\n  },\n\n  // Python Templates\n  {\n    id: 'py-basic',\n    name: 'Basic Python',\n    description: 'Simple requests call',\n    language: 'python',\n    category: 'basic'\n  },\n  {\n    id: 'py-sdk',\n    name: 'Rayuela SDK',\n    description: 'Using official Python SDK',\n    language: 'python',\n    category: 'basic'\n  },\n  {\n    id: 'py-fastapi',\n    name: 'FastAPI Integration',\n    description: 'FastAPI endpoint with recommendations',\n    language: 'python',\n    framework: 'fastapi',\n    category: 'integration'\n  },\n  {\n    id: 'py-django',\n    name: 'Django View',\n    description: 'Django view with recommendations',\n    language: 'python',\n    framework: 'django',\n    category: 'integration'\n  },\n  {\n    id: 'py-production',\n    name: 'Production Ready',\n    description: 'With async, error handling, and logging',\n    language: 'python',\n    category: 'production'\n  }\n];\n\nexport default function CodeGenerator({ endpoint, parameters, apiKey }: GeneratorProps) {\n  const [selectedTemplate, setSelectedTemplate] = useState<CodeTemplate>(CODE_TEMPLATES[1]); // Default to SDK\n  const [copiedCode, setCopiedCode] = useState<string | null>(null);\n\n  const generateCode = (template: CodeTemplate): string => {\n    const baseUrl = 'https://api.rayuela.ai';\n    const fullUrl = `${baseUrl}${endpoint.path}`;\n    \n    switch (template.id) {\n      case 'js-basic':\n        return generateJavaScriptBasic(fullUrl, endpoint, parameters, apiKey);\n      case 'js-sdk':\n        return generateJavaScriptSDK(endpoint, parameters, apiKey);\n      case 'js-react':\n        return generateReactHook(endpoint, parameters, apiKey);\n      case 'js-nextjs':\n        return generateNextJSRoute(endpoint, parameters, apiKey);\n      case 'js-production':\n        return generateJavaScriptProduction(fullUrl, endpoint, parameters, apiKey);\n      case 'py-basic':\n        return generatePythonBasic(fullUrl, endpoint, parameters, apiKey);\n      case 'py-sdk':\n        return generatePythonSDK(endpoint, parameters, apiKey);\n      case 'py-fastapi':\n        return generateFastAPIIntegration(endpoint, parameters, apiKey);\n      case 'py-django':\n        return generateDjangoView(endpoint, parameters, apiKey);\n      case 'py-production':\n        return generatePythonProduction(fullUrl, endpoint, parameters, apiKey);\n      default:\n        return '// Template not found';\n    }\n  };\n\n  const copyToClipboard = async (text: string, id: string) => {\n    try {\n      await navigator.clipboard.writeText(text);\n      setCopiedCode(id);\n      setTimeout(() => setCopiedCode(null), 2000);\n    } catch (err) {\n      console.error('Failed to copy: ', err);\n    }\n  };\n\n  const downloadCode = (code: string, template: CodeTemplate) => {\n    const extension = template.language === 'javascript' ? 'js' : 'py';\n    const filename = `rayuela-${template.id}.${extension}`;\n    \n    const blob = new Blob([code], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = filename;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  const filteredTemplates = CODE_TEMPLATES.filter(template => \n    template.language === selectedTemplate.language\n  );\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Code2 className=\"h-5 w-5\" />\n          Code Generator\n        </CardTitle>\n        <CardDescription>\n          Generate production-ready code for your application\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"space-y-4\">\n          {/* Language Selection */}\n          <div className=\"flex gap-2\">\n            <Button\n              variant={selectedTemplate.language === 'javascript' ? 'default' : 'outline'}\n              onClick={() => setSelectedTemplate(CODE_TEMPLATES.find(t => t.id === 'js-sdk')!)}\n            >\n              JavaScript\n            </Button>\n            <Button\n              variant={selectedTemplate.language === 'python' ? 'default' : 'outline'}\n              onClick={() => setSelectedTemplate(CODE_TEMPLATES.find(t => t.id === 'py-sdk')!)}\n            >\n              Python\n            </Button>\n          </div>\n\n          {/* Template Selection */}\n          <div>\n            <label className=\"text-sm font-medium mb-2 block\">Template</label>\n            <Select\n              value={selectedTemplate.id}\n              onValueChange={(value) => setSelectedTemplate(CODE_TEMPLATES.find(t => t.id === value)!)}\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                {filteredTemplates.map((template) => (\n                  <SelectItem key={template.id} value={template.id}>\n                    <div className=\"flex items-center gap-2\">\n                      <span>{template.name}</span>\n                      {template.framework && (\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          {template.framework}\n                        </Badge>\n                      )}\n                      <Badge \n                        variant={template.category === 'production' ? 'default' : 'secondary'}\n                        className=\"text-xs\"\n                      >\n                        {template.category}\n                      </Badge>\n                    </div>\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n            <p className=\"text-sm text-muted-foreground mt-1\">\n              {selectedTemplate.description}\n            </p>\n          </div>\n\n          {/* Generated Code */}\n          <div className=\"relative\">\n            <div className=\"flex items-center justify-between mb-2\">\n              <div className=\"flex items-center gap-2\">\n                <FileText className=\"h-4 w-4\" />\n                <span className=\"text-sm font-medium\">Generated Code</span>\n              </div>\n              <div className=\"flex gap-2\">\n                <Button\n                  size=\"sm\"\n                  variant=\"outline\"\n                  onClick={() => downloadCode(generateCode(selectedTemplate), selectedTemplate)}\n                >\n                  <Download className=\"h-4 w-4 mr-1\" />\n                  Download\n                </Button>\n                <Button\n                  size=\"sm\"\n                  variant=\"outline\"\n                  onClick={() => copyToClipboard(generateCode(selectedTemplate), selectedTemplate.id)}\n                >\n                  {copiedCode === selectedTemplate.id ? (\n                    <CheckCircle className=\"h-4 w-4\" />\n                  ) : (\n                    <Copy className=\"h-4 w-4\" />\n                  )}\n                </Button>\n              </div>\n            </div>\n            \n            <pre className=\"bg-muted p-4 rounded-lg overflow-x-auto text-sm max-h-96\">\n              <code>{generateCode(selectedTemplate)}</code>\n            </pre>\n          </div>\n\n          {/* Installation Instructions */}\n          {(selectedTemplate.id.includes('sdk') || selectedTemplate.category === 'production') && (\n            <Card className=\"bg-blue-50 border-blue-200\">\n              <CardContent className=\"pt-4\">\n                <div className=\"flex items-center gap-2 mb-2\">\n                  <Package className=\"h-4 w-4 text-blue-600\" />\n                  <span className=\"text-sm font-medium text-blue-800\">Installation Required</span>\n                </div>\n                <div className=\"space-y-2\">\n                  {selectedTemplate.language === 'javascript' && (\n                    <code className=\"text-sm bg-white px-2 py-1 rounded border\">\n                      npm install rayuela-sdk\n                    </code>\n                  )}\n                  {selectedTemplate.language === 'python' && (\n                    <code className=\"text-sm bg-white px-2 py-1 rounded border\">\n                      pip install rayuela-sdk\n                    </code>\n                  )}\n                  {selectedTemplate.category === 'production' && (\n                    <p className=\"text-sm text-blue-700 mt-2\">\n                      This template includes production-ready features like error handling, retry logic, and logging.\n                    </p>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Quick Actions */}\n          <div className=\"flex gap-2 pt-2\">\n            <Button\n              size=\"sm\"\n              variant=\"outline\"\n              onClick={() => setSelectedTemplate(CODE_TEMPLATES.find(t => t.id === `${selectedTemplate.language}-sdk`)!)}\n            >\n              <Zap className=\"h-4 w-4 mr-1\" />\n              Use SDK (Recommended)\n            </Button>\n            <Button\n              size=\"sm\"\n              variant=\"outline\"\n              onClick={() => setSelectedTemplate(CODE_TEMPLATES.find(t => t.id === `${selectedTemplate.language}-production`)!)}\n            >\n              Production Ready\n            </Button>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n\n// Code generation functions\nfunction generateJavaScriptBasic(url: string, endpoint: any, params: any, apiKey: string): string {\n  if (endpoint.method === 'POST') {\n    return `// Basic JavaScript with fetch\nasync function getRecommendations() {\n  try {\n    const response = await fetch('${url}', {\n      method: 'POST',\n      headers: {\n        'X-API-Key': '${apiKey}',\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(${JSON.stringify(params, null, 2)})\n    });\n    \n    if (!response.ok) {\n      throw new Error(\\`HTTP \\${response.status}: \\${response.statusText}\\`);\n    }\n    \n    const data = await response.json();\n    console.log('Recommendations:', data);\n    return data;\n  } catch (error) {\n    console.error('Error fetching recommendations:', error);\n    throw error;\n  }\n}\n\n// Usage\ngetRecommendations();`;\n  } else {\n    const queryParams = new URLSearchParams(params).toString();\n    return `// Basic JavaScript with fetch\nasync function getRecommendations() {\n  try {\n    const response = await fetch('${url}${queryParams ? '?' + queryParams : ''}', {\n      headers: {\n        'X-API-Key': '${apiKey}'\n      }\n    });\n    \n    if (!response.ok) {\n      throw new Error(\\`HTTP \\${response.status}: \\${response.statusText}\\`);\n    }\n    \n    const data = await response.json();\n    console.log('Data:', data);\n    return data;\n  } catch (error) {\n    console.error('Error fetching data:', error);\n    throw error;\n  }\n}\n\n// Usage\ngetRecommendations();`;\n  }\n}\n\nfunction generateJavaScriptSDK(endpoint: any, params: any, apiKey: string): string {\n  const isEcommerce = endpoint.id.includes('ecommerce');\n  const isAbTest = endpoint.id.includes('ab-test');\n  \n  if (isEcommerce) {\n    return `// Using Rayuela SDK (Recommended)\nimport Rayuela from 'rayuela-sdk';\n\nconst client = new Rayuela({ \n  apiKey: '${apiKey}',\n  debug: true // Enable for development\n});\n\nasync function getHomepageRecommendations() {\n  try {\n    const recommendations = await client.ecommerce('${params.external_user_id || 'user-123'}', {\n      page: 'homepage',\n      limit: ${params.limit || 10},\n      inStockOnly: true${params.category ? `,\\n      category: '${params.category}'` : ''}\n    });\n    \n    console.log(\\`Got \\${recommendations.items.length} recommendations\\`);\n    \n    // Display recommendations\n    recommendations.items.forEach((item, index) => {\n      console.log(\\`\\${index + 1}. \\${item.name} - Score: \\${item.score.toFixed(2)}\\`);\n    });\n    \n    return recommendations;\n  } catch (error) {\n    console.error('Error:', error.message);\n    if (error.code === 'INVALID_API_KEY') {\n      console.log('💡 Get your API key at: https://dashboard.rayuela.ai');\n    }\n  }\n}\n\n// Usage\ngetHomepageRecommendations();`;\n  } else if (isAbTest) {\n    return `// A/B Testing with Rayuela SDK\nimport Rayuela from 'rayuela-sdk';\n\nconst client = new Rayuela({ apiKey: '${apiKey}' });\n\nasync function runAbTest() {\n  try {\n    // Get recommendations with automatic A/B testing\n    const result = await client.abTest('${params.external_user_id || 'user-123'}', {\n      limit: ${params.limit || 10}\n    });\n    \n    console.log(\\`User assigned to: \\${result.variant} group\\`);\n    console.log(\\`Experiment ID: \\${result.experimentId}\\`);\n    console.log(\\`Got \\${result.items.length} recommendations\\`);\n    \n    // Track user interaction\n    if (result.items.length > 0) {\n      await client.trackAbTest({\n        experimentId: result.experimentId,\n        userId: '${params.external_user_id || 'user-123'}',\n        eventType: 'click',\n        productId: result.items[0].id.toString()\n      });\n      console.log('✅ Interaction tracked');\n    }\n    \n    // Get experiment results\n    const results = await client.getAbTestResults(result.experimentId);\n    console.log(\\`CTR Lift: \\${results.lifts.ctrLiftPercentage}\\`);\n    \n    return result;\n  } catch (error) {\n    console.error('Error:', error.message);\n  }\n}\n\n// Usage\nrunAbTest();`;\n  } else {\n    return `// Using Rayuela SDK\nimport Rayuela from 'rayuela-sdk';\n\nconst client = new Rayuela({ apiKey: '${apiKey}' });\n\nasync function getRecommendations() {\n  try {\n    const recommendations = await client.recommend('${params.external_user_id || 'user-123'}', {\n      limit: ${params.limit || 10},\n      strategy: 'balanced'\n    });\n    \n    console.log(\\`Got \\${recommendations.items.length} recommendations\\`);\n    return recommendations;\n  } catch (error) {\n    console.error('Error:', error.message);\n  }\n}\n\n// Usage\ngetRecommendations();`;\n  }\n}\n\nfunction generateReactHook(endpoint: any, params: any, apiKey: string): string {\n  return `// Custom React Hook for Rayuela Recommendations\nimport { useState, useEffect } from 'react';\nimport Rayuela from 'rayuela-sdk';\n\nconst client = new Rayuela({ apiKey: '${apiKey}' });\n\nexport function useRecommendations(userId, options = {}) {\n  const [recommendations, setRecommendations] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const fetchRecommendations = async () => {\n    if (!userId) return;\n    \n    setLoading(true);\n    setError(null);\n    \n    try {\n      const result = await client.recommend(userId, {\n        limit: 10,\n        ...options\n      });\n      setRecommendations(result.items);\n    } catch (err) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchRecommendations();\n  }, [userId, JSON.stringify(options)]);\n\n  const trackInteraction = async (productId, eventType = 'click') => {\n    try {\n      await client.track({\n        userId,\n        productId,\n        type: eventType\n      });\n    } catch (err) {\n      console.error('Tracking error:', err);\n    }\n  };\n\n  return {\n    recommendations,\n    loading,\n    error,\n    refetch: fetchRecommendations,\n    trackInteraction\n  };\n}\n\n// Usage in component:\n// const { recommendations, loading, trackInteraction } = useRecommendations('user-123');`;\n}\n\nfunction generateNextJSRoute(endpoint: any, params: any, apiKey: string): string {\n  return `// Next.js API Route (pages/api/recommendations.js)\nimport Rayuela from 'rayuela-sdk';\n\nconst client = new Rayuela({ apiKey: '${apiKey}' });\n\nexport default async function handler(req, res) {\n  if (req.method !== 'POST') {\n    return res.status(405).json({ error: 'Method not allowed' });\n  }\n\n  try {\n    const { userId, limit = 10, strategy = 'balanced' } = req.body;\n\n    if (!userId) {\n      return res.status(400).json({ error: 'userId is required' });\n    }\n\n    const recommendations = await client.recommend(userId, {\n      limit,\n      strategy\n    });\n\n    res.status(200).json({\n      success: true,\n      data: recommendations\n    });\n  } catch (error) {\n    console.error('Recommendations error:', error);\n    res.status(500).json({\n      success: false,\n      error: error.message\n    });\n  }\n}\n\n// Usage from frontend:\n// const response = await fetch('/api/recommendations', {\n//   method: 'POST',\n//   headers: { 'Content-Type': 'application/json' },\n//   body: JSON.stringify({ userId: 'user-123', limit: 10 })\n// });`;\n}\n\nfunction generateJavaScriptProduction(url: string, endpoint: any, params: any, apiKey: string): string {\n  return `// Production-ready JavaScript implementation\nclass RayuelaClient {\n  constructor(apiKey, options = {}) {\n    this.apiKey = apiKey;\n    this.baseUrl = options.baseUrl || 'https://api.rayuela.ai';\n    this.timeout = options.timeout || 30000;\n    this.retryAttempts = options.retryAttempts || 3;\n    this.cache = new Map();\n  }\n\n  async makeRequest(endpoint, options = {}) {\n    const url = \\`\\${this.baseUrl}\\${endpoint}\\`;\n    const cacheKey = \\`\\${options.method || 'GET'}:\\${url}:\\${JSON.stringify(options.body)}\\`;\n    \n    // Check cache for GET requests\n    if (!options.method || options.method === 'GET') {\n      const cached = this.cache.get(cacheKey);\n      if (cached && Date.now() - cached.timestamp < 300000) { // 5 min cache\n        return cached.data;\n      }\n    }\n\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), this.timeout);\n\n    try {\n      const response = await this.retryRequest(url, {\n        ...options,\n        headers: {\n          'X-API-Key': this.apiKey,\n          'Content-Type': 'application/json',\n          ...options.headers\n        },\n        signal: controller.signal\n      });\n\n      clearTimeout(timeoutId);\n\n      if (!response.ok) {\n        throw new Error(\\`HTTP \\${response.status}: \\${response.statusText}\\`);\n      }\n\n      const data = await response.json();\n      \n      // Cache successful GET responses\n      if (!options.method || options.method === 'GET') {\n        this.cache.set(cacheKey, { data, timestamp: Date.now() });\n      }\n\n      return data;\n    } catch (error) {\n      clearTimeout(timeoutId);\n      throw error;\n    }\n  }\n\n  async retryRequest(url, options, attempt = 1) {\n    try {\n      return await fetch(url, options);\n    } catch (error) {\n      if (attempt < this.retryAttempts && this.isRetryableError(error)) {\n        await this.delay(Math.pow(2, attempt) * 1000); // Exponential backoff\n        return this.retryRequest(url, options, attempt + 1);\n      }\n      throw error;\n    }\n  }\n\n  isRetryableError(error) {\n    return error.name === 'TypeError' || // Network error\n           (error.status >= 500 && error.status < 600); // Server error\n  }\n\n  delay(ms) {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n\n  async getRecommendations(userId, options = {}) {\n    return this.makeRequest('${endpoint.path}', {\n      method: '${endpoint.method}',\n      body: JSON.stringify({\n        external_user_id: userId,\n        ...options\n      })\n    });\n  }\n}\n\n// Usage\nconst client = new RayuelaClient('${apiKey}', {\n  timeout: 10000,\n  retryAttempts: 3\n});\n\nasync function main() {\n  try {\n    const recommendations = await client.getRecommendations('${params.external_user_id || 'user-123'}', ${JSON.stringify(params, null, 2)});\n    console.log('Success:', recommendations);\n  } catch (error) {\n    console.error('Error:', error.message);\n  }\n}\n\nmain();`;\n}\n\nfunction generatePythonBasic(url: string, endpoint: any, params: any, apiKey: string): string {\n  if (endpoint.method === 'POST') {\n    return `# Basic Python with requests\nimport requests\nimport json\n\ndef get_recommendations():\n    url = '${url}'\n    headers = {\n        'X-API-Key': '${apiKey}',\n        'Content-Type': 'application/json'\n    }\n    data = ${JSON.stringify(params, null, 2).replace(/\"/g, \"'\")}\n    \n    try:\n        response = requests.post(url, headers=headers, json=data)\n        response.raise_for_status()\n        \n        result = response.json()\n        print(f\"Got {len(result.get('items', []))} recommendations\")\n        return result\n    except requests.exceptions.RequestException as e:\n        print(f\"Error: {e}\")\n        raise\n\n# Usage\nif __name__ == \"__main__\":\n    recommendations = get_recommendations()`;\n  } else {\n    return `# Basic Python with requests\nimport requests\n\ndef get_data():\n    url = '${url}'\n    headers = {'X-API-Key': '${apiKey}'}\n    params = ${JSON.stringify(params, null, 2).replace(/\"/g, \"'\")}\n    \n    try:\n        response = requests.get(url, headers=headers, params=params)\n        response.raise_for_status()\n        \n        result = response.json()\n        print(\"Data retrieved successfully\")\n        return result\n    except requests.exceptions.RequestException as e:\n        print(f\"Error: {e}\")\n        raise\n\n# Usage\nif __name__ == \"__main__\":\n    data = get_data()`;\n  }\n}\n\nfunction generatePythonSDK(endpoint: any, params: any, apiKey: string): string {\n  return `# Using Rayuela Python SDK (Recommended)\nfrom rayuela import Rayuela\n\nclient = Rayuela(api_key='${apiKey}')\n\ndef get_recommendations():\n    try:\n        recommendations = client.recommend(\n            user_id='${params.external_user_id || 'user-123'}',\n            limit=${params.limit || 10},\n            strategy='balanced'\n        )\n        \n        print(f\"Got {len(recommendations.items)} recommendations\")\n        \n        for i, item in enumerate(recommendations.items, 1):\n            print(f\"{i}. {item.name} - Score: {item.score:.2f}\")\n        \n        return recommendations\n    except Exception as e:\n        print(f\"Error: {e}\")\n        if hasattr(e, 'code') and e.code == 'INVALID_API_KEY':\n            print(\"💡 Get your API key at: https://dashboard.rayuela.ai\")\n\nif __name__ == \"__main__\":\n    get_recommendations()`;\n}\n\nfunction generateFastAPIIntegration(endpoint: any, params: any, apiKey: string): string {\n  return `# FastAPI Integration\nfrom fastapi import FastAPI, HTTPException\nfrom pydantic import BaseModel\nfrom rayuela import Rayuela\nfrom typing import Optional\n\napp = FastAPI()\nclient = Rayuela(api_key='${apiKey}')\n\nclass RecommendationRequest(BaseModel):\n    user_id: str\n    limit: Optional[int] = 10\n    strategy: Optional[str] = 'balanced'\n\**********(\"/recommendations\")\nasync def get_recommendations(request: RecommendationRequest):\n    try:\n        recommendations = client.recommend(\n            user_id=request.user_id,\n            limit=request.limit,\n            strategy=request.strategy\n        )\n        \n        return {\n            \"success\": True,\n            \"data\": recommendations.dict(),\n            \"count\": len(recommendations.items)\n        }\n    except Exception as e:\n        raise HTTPException(status_code=500, detail=str(e))\n\**********(\"/track\")\nasync def track_interaction(user_id: str, product_id: str, event_type: str = \"click\"):\n    try:\n        client.track(\n            user_id=user_id,\n            product_id=product_id,\n            type=event_type\n        )\n        return {\"success\": True, \"message\": \"Interaction tracked\"}\n    except Exception as e:\n        raise HTTPException(status_code=500, detail=str(e))\n\n# Run with: uvicorn main:app --reload`;\n}\n\nfunction generateDjangoView(endpoint: any, params: any, apiKey: string): string {\n  return `# Django Views\nfrom django.http import JsonResponse\nfrom django.views.decorators.csrf import csrf_exempt\nfrom django.views.decorators.http import require_http_methods\nfrom rayuela import Rayuela\nimport json\n\nclient = Rayuela(api_key='${apiKey}')\n\n@csrf_exempt\n@require_http_methods([\"POST\"])\ndef get_recommendations(request):\n    try:\n        data = json.loads(request.body)\n        user_id = data.get('user_id')\n        \n        if not user_id:\n            return JsonResponse({'error': 'user_id is required'}, status=400)\n        \n        recommendations = client.recommend(\n            user_id=user_id,\n            limit=data.get('limit', 10),\n            strategy=data.get('strategy', 'balanced')\n        )\n        \n        return JsonResponse({\n            'success': True,\n            'data': recommendations.dict(),\n            'count': len(recommendations.items)\n        })\n    except Exception as e:\n        return JsonResponse({'error': str(e)}, status=500)\n\n@csrf_exempt\n@require_http_methods([\"POST\"])\ndef track_interaction(request):\n    try:\n        data = json.loads(request.body)\n        \n        client.track(\n            user_id=data['user_id'],\n            product_id=data['product_id'],\n            type=data.get('event_type', 'click')\n        )\n        \n        return JsonResponse({'success': True})\n    except Exception as e:\n        return JsonResponse({'error': str(e)}, status=500)\n\n# Add to urls.py:\n# path('recommendations/', views.get_recommendations, name='recommendations'),\n# path('track/', views.track_interaction, name='track'),`;\n}\n\nfunction generatePythonProduction(url: string, endpoint: any, params: any, apiKey: string): string {\n  return `# Production-ready Python implementation\nimport asyncio\nimport aiohttp\nimport logging\nfrom typing import Optional, Dict, Any\nfrom dataclasses import dataclass\nfrom datetime import datetime, timedelta\n\nlogging.basicConfig(level=logging.INFO)\nlogger = logging.getLogger(__name__)\n\n@dataclass\nclass RayuelaConfig:\n    api_key: str\n    base_url: str = 'https://api.rayuela.ai'\n    timeout: int = 30\n    retry_attempts: int = 3\n    cache_ttl: int = 300  # 5 minutes\n\nclass RayuelaClient:\n    def __init__(self, config: RayuelaConfig):\n        self.config = config\n        self.cache = {}\n        self.session = None\n    \n    async def __aenter__(self):\n        self.session = aiohttp.ClientSession(\n            timeout=aiohttp.ClientTimeout(total=self.config.timeout),\n            headers={'X-API-Key': self.config.api_key}\n        )\n        return self\n    \n    async def __aexit__(self, exc_type, exc_val, exc_tb):\n        if self.session:\n            await self.session.close()\n    \n    async def make_request(self, endpoint: str, method: str = 'GET', data: Optional[Dict] = None) -> Dict[str, Any]:\n        url = f\"{self.config.base_url}{endpoint}\"\n        cache_key = f\"{method}:{url}:{str(data)}\"\n        \n        # Check cache for GET requests\n        if method == 'GET' and cache_key in self.cache:\n            cached_data, timestamp = self.cache[cache_key]\n            if datetime.now() - timestamp < timedelta(seconds=self.config.cache_ttl):\n                logger.info(f\"Cache hit for {url}\")\n                return cached_data\n        \n        for attempt in range(self.config.retry_attempts):\n            try:\n                async with self.session.request(method, url, json=data) as response:\n                    if response.status == 200:\n                        result = await response.json()\n                        \n                        # Cache successful GET responses\n                        if method == 'GET':\n                            self.cache[cache_key] = (result, datetime.now())\n                        \n                        logger.info(f\"Request successful: {method} {url}\")\n                        return result\n                    else:\n                        error_text = await response.text()\n                        logger.error(f\"HTTP {response.status}: {error_text}\")\n                        \n                        if response.status < 500:  # Don't retry client errors\n                            break\n                            \n            except asyncio.TimeoutError:\n                logger.warning(f\"Timeout on attempt {attempt + 1}\")\n            except Exception as e:\n                logger.error(f\"Request failed on attempt {attempt + 1}: {e}\")\n            \n            if attempt < self.config.retry_attempts - 1:\n                wait_time = 2 ** attempt  # Exponential backoff\n                logger.info(f\"Retrying in {wait_time} seconds...\")\n                await asyncio.sleep(wait_time)\n        \n        raise Exception(f\"Failed to complete request after {self.config.retry_attempts} attempts\")\n    \n    async def get_recommendations(self, user_id: str, **options) -> Dict[str, Any]:\n        data = {\n            'external_user_id': user_id,\n            **options\n        }\n        return await self.make_request('${endpoint.path}', '${endpoint.method}', data)\n\n# Usage\nasync def main():\n    config = RayuelaConfig(api_key='${apiKey}')\n    \n    async with RayuelaClient(config) as client:\n        try:\n            recommendations = await client.get_recommendations(\n                '${params.external_user_id || 'user-123'}',\n                **${JSON.stringify(params, null, 2).replace(/\"/g, \"'\")}\n            )\n            logger.info(f\"Got {len(recommendations.get('items', []))} recommendations\")\n            return recommendations\n        except Exception as e:\n            logger.error(f\"Error: {e}\")\n\nif __name__ == \"__main__\":\n    asyncio.run(main())`;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;AAiCA,MAAM,iBAAiC;IACrC,uBAAuB;IACvB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;IACZ;IAEA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;IACZ;CACD;AAEc,SAAS,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAkB;IACpF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,cAAc,CAAC,EAAE,GAAG,iBAAiB;IAC5G,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,MAAM,eAAe,CAAC;QACpB,MAAM,UAAU;QAChB,MAAM,UAAU,GAAG,UAAU,SAAS,IAAI,EAAE;QAE5C,OAAQ,SAAS,EAAE;YACjB,KAAK;gBACH,OAAO,wBAAwB,SAAS,UAAU,YAAY;YAChE,KAAK;gBACH,OAAO,sBAAsB,UAAU,YAAY;YACrD,KAAK;gBACH,OAAO,kBAAkB,UAAU,YAAY;YACjD,KAAK;gBACH,OAAO,oBAAoB,UAAU,YAAY;YACnD,KAAK;gBACH,OAAO,6BAA6B,SAAS,UAAU,YAAY;YACrE,KAAK;gBACH,OAAO,oBAAoB,SAAS,UAAU,YAAY;YAC5D,KAAK;gBACH,OAAO,kBAAkB,UAAU,YAAY;YACjD,KAAK;gBACH,OAAO,2BAA2B,UAAU,YAAY;YAC1D,KAAK;gBACH,OAAO,mBAAmB,UAAU,YAAY;YAClD,KAAK;gBACH,OAAO,yBAAyB,SAAS,UAAU,YAAY;YACjE;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,OAAO,MAAc;QAC3C,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,cAAc;YACd,WAAW,IAAM,cAAc,OAAO;QACxC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oBAAoB;QACpC;IACF;IAEA,MAAM,eAAe,CAAC,MAAc;QAClC,MAAM,YAAY,SAAS,QAAQ,KAAK,eAAe,OAAO;QAC9D,MAAM,WAAW,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,WAAW;QAEtD,MAAM,OAAO,IAAI,KAAK;YAAC;SAAK,EAAE;YAAE,MAAM;QAAa;QACnD,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG;QACb,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,oBAAoB,eAAe,MAAM,CAAC,CAAA,WAC9C,SAAS,QAAQ,KAAK,iBAAiB,QAAQ;IAGjD,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,0MAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAG/B,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,iBAAiB,QAAQ,KAAK,eAAe,YAAY;oCAClE,SAAS,IAAM,oBAAoB,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;8CACtE;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,iBAAiB,QAAQ,KAAK,WAAW,YAAY;oCAC9D,SAAS,IAAM,oBAAoB,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;8CACtE;;;;;;;;;;;;sCAMH,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAiC;;;;;;8CAClD,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,iBAAiB,EAAE;oCAC1B,eAAe,CAAC,QAAU,oBAAoB,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;sDAEhF,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,8OAAC,kIAAA,CAAA,gBAAa;sDACX,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC,kIAAA,CAAA,aAAU;oDAAmB,OAAO,SAAS,EAAE;8DAC9C,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAM,SAAS,IAAI;;;;;;4DACnB,SAAS,SAAS,kBACjB,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAChC,SAAS,SAAS;;;;;;0EAGvB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,SAAS,SAAS,QAAQ,KAAK,eAAe,YAAY;gEAC1D,WAAU;0EAET,SAAS,QAAQ;;;;;;;;;;;;mDAZP,SAAS,EAAE;;;;;;;;;;;;;;;;8CAmBlC,8OAAC;oCAAE,WAAU;8CACV,iBAAiB,WAAW;;;;;;;;;;;;sCAKjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;;;;;;;sDAExC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,aAAa,aAAa,mBAAmB;;sEAE5D,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGvC,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,gBAAgB,aAAa,mBAAmB,iBAAiB,EAAE;8DAEjF,eAAe,iBAAiB,EAAE,iBACjC,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAMxB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;kDAAM,aAAa;;;;;;;;;;;;;;;;;wBAKvB,CAAC,iBAAiB,EAAE,CAAC,QAAQ,CAAC,UAAU,iBAAiB,QAAQ,KAAK,YAAY,mBACjF,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,8OAAC;wCAAI,WAAU;;4CACZ,iBAAiB,QAAQ,KAAK,8BAC7B,8OAAC;gDAAK,WAAU;0DAA4C;;;;;;4CAI7D,iBAAiB,QAAQ,KAAK,0BAC7B,8OAAC;gDAAK,WAAU;0DAA4C;;;;;;4CAI7D,iBAAiB,QAAQ,KAAK,8BAC7B,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;sCAUpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,GAAG,iBAAiB,QAAQ,CAAC,IAAI,CAAC;;sDAEvG,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGlC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,GAAG,iBAAiB,QAAQ,CAAC,WAAW,CAAC;8CAC/G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;AAEA,4BAA4B;AAC5B,SAAS,wBAAwB,GAAW,EAAE,QAAa,EAAE,MAAW,EAAE,MAAc;IACtF,IAAI,SAAS,MAAM,KAAK,QAAQ;QAC9B,OAAO,CAAC;;;kCAGsB,EAAE,IAAI;;;sBAGlB,EAAE,OAAO;;;2BAGJ,EAAE,KAAK,SAAS,CAAC,QAAQ,MAAM,GAAG;;;;;;;;;;;;;;;;;qBAiBxC,CAAC;IACpB,OAAO;QACL,MAAM,cAAc,IAAI,gBAAgB,QAAQ,QAAQ;QACxD,OAAO,CAAC;;;kCAGsB,EAAE,MAAM,cAAc,MAAM,cAAc,GAAG;;sBAEzD,EAAE,OAAO;;;;;;;;;;;;;;;;;;qBAkBV,CAAC;IACpB;AACF;AAEA,SAAS,sBAAsB,QAAa,EAAE,MAAW,EAAE,MAAc;IACvE,MAAM,cAAc,SAAS,EAAE,CAAC,QAAQ,CAAC;IACzC,MAAM,WAAW,SAAS,EAAE,CAAC,QAAQ,CAAC;IAEtC,IAAI,aAAa;QACf,OAAO,CAAC;;;;WAID,EAAE,OAAO;;;;;;oDAMgC,EAAE,OAAO,gBAAgB,IAAI,WAAW;;aAE/E,EAAE,OAAO,KAAK,IAAI,GAAG;uBACX,EAAE,OAAO,QAAQ,GAAG,CAAC,oBAAoB,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG;;;;;;;;;;;;;;;;;;;;6BAoB7D,CAAC;IAC5B,OAAO,IAAI,UAAU;QACnB,OAAO,CAAC;;;sCAG0B,EAAE,OAAO;;;;;wCAKP,EAAE,OAAO,gBAAgB,IAAI,WAAW;aACnE,EAAE,OAAO,KAAK,IAAI,GAAG;;;;;;;;;;;iBAWjB,EAAE,OAAO,gBAAgB,IAAI,WAAW;;;;;;;;;;;;;;;;;;YAkB7C,CAAC;IACX,OAAO;QACL,OAAO,CAAC;;;sCAG0B,EAAE,OAAO;;;;oDAIK,EAAE,OAAO,gBAAgB,IAAI,WAAW;aAC/E,EAAE,OAAO,KAAK,IAAI,GAAG;;;;;;;;;;;;qBAYb,CAAC;IACpB;AACF;AAEA,SAAS,kBAAkB,QAAa,EAAE,MAAW,EAAE,MAAc;IACnE,OAAO,CAAC;;;;sCAI4B,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yFAoD0C,CAAC;AAC1F;AAEA,SAAS,oBAAoB,QAAa,EAAE,MAAW,EAAE,MAAc;IACrE,OAAO,CAAC;;;sCAG4B,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAqCzC,CAAC;AACP;AAEA,SAAS,6BAA6B,GAAW,EAAE,QAAa,EAAE,MAAW,EAAE,MAAc;IAC3F,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6BA8EmB,EAAE,SAAS,IAAI,CAAC;eAC9B,EAAE,SAAS,MAAM,CAAC;;;;;;;;;;kCAUC,EAAE,OAAO;;;;;;;6DAOkB,EAAE,OAAO,gBAAgB,IAAI,WAAW,GAAG,EAAE,KAAK,SAAS,CAAC,QAAQ,MAAM,GAAG;;;;;;;OAOnI,CAAC;AACR;AAEA,SAAS,oBAAoB,GAAW,EAAE,QAAa,EAAE,MAAW,EAAE,MAAc;IAClF,IAAI,SAAS,MAAM,KAAK,QAAQ;QAC9B,OAAO,CAAC;;;;;WAKD,EAAE,IAAI;;sBAEK,EAAE,OAAO;;;WAGpB,EAAE,KAAK,SAAS,CAAC,QAAQ,MAAM,GAAG,OAAO,CAAC,MAAM,KAAK;;;;;;;;;;;;;;;2CAerB,CAAC;IAC1C,OAAO;QACL,OAAO,CAAC;;;;WAID,EAAE,IAAI;6BACY,EAAE,OAAO;aACzB,EAAE,KAAK,SAAS,CAAC,QAAQ,MAAM,GAAG,OAAO,CAAC,MAAM,KAAK;;;;;;;;;;;;;;;qBAe7C,CAAC;IACpB;AACF;AAEA,SAAS,kBAAkB,QAAa,EAAE,MAAW,EAAE,MAAc;IACnE,OAAO,CAAC;;;0BAGgB,EAAE,OAAO;;;;;qBAKd,EAAE,OAAO,gBAAgB,IAAI,WAAW;kBAC3C,EAAE,OAAO,KAAK,IAAI,GAAG;;;;;;;;;;;;;;;;yBAgBd,CAAC;AAC1B;AAEA,SAAS,2BAA2B,QAAa,EAAE,MAAW,EAAE,MAAc;IAC5E,OAAO,CAAC;;;;;;;0BAOgB,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qCAoCE,CAAC;AACtC;AAEA,SAAS,mBAAmB,QAAa,EAAE,MAAW,EAAE,MAAc;IACpE,OAAO,CAAC;;;;;;;0BAOgB,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wDA4CqB,CAAC;AACzD;AAEA,SAAS,yBAAyB,GAAW,EAAE,QAAa,EAAE,MAAW,EAAE,MAAc;IACvF,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAmF8B,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,SAAS,MAAM,CAAC;;;;oCAI1C,EAAE,OAAO;;;;;iBAK5B,EAAE,OAAO,gBAAgB,IAAI,WAAW;kBACvC,EAAE,KAAK,SAAS,CAAC,QAAQ,MAAM,GAAG,OAAO,CAAC,MAAM,KAAK;;;;;;;;uBAQhD,CAAC;AACxB", "debugId": null}}, {"offset": {"line": 1669, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1708, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/api-explorer/ResponseVisualizer.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"@/components/ui/tabs\";\nimport { \n  Eye, \n  BarChart3, \n  TrendingUp, \n  Star, \n  DollarSign, \n  Target,\n  Lightbulb,\n  Zap,\n  Users,\n  ShoppingCart,\n  Clock,\n  Award,\n  Info\n} from \"lucide-react\";\n\ninterface ResponseVisualizerProps {\n  response: any;\n  endpoint: any;\n}\n\ninterface RecommendationItem {\n  id: number;\n  name: string;\n  category?: string;\n  price?: number;\n  score: number;\n  explanation?: string;\n  source?: string;\n  averageRating?: number;\n  metadata?: any;\n}\n\nexport default function ResponseVisualizer({ response, endpoint }: ResponseVisualizerProps) {\n  const [selectedItem, setSelectedItem] = useState<RecommendationItem | null>(null);\n  const [viewMode, setViewMode] = useState<'cards' | 'table' | 'metrics'>('cards');\n\n  if (!response || !response.data) {\n    return (\n      <Card>\n        <CardContent className=\"pt-6\">\n          <div className=\"text-center text-muted-foreground\">\n            <Eye className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n            <p>Execute a request to see the response visualization</p>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const data = response.data;\n  const isRecommendations = data.items && Array.isArray(data.items);\n  const isMetrics = data.ctr_lift !== undefined || data.cvr_lift !== undefined;\n  const isAbTest = data.meta?.ab_testing || data.variant;\n\n  const getStatusColor = (status: number) => {\n    if (status >= 200 && status < 300) return 'bg-green-100 text-green-800 border-green-200';\n    if (status >= 400 && status < 500) return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n    if (status >= 500) return 'bg-red-100 text-red-800 border-red-200';\n    return 'bg-muted text-foreground border-border';\n  };\n\n  const formatPrice = (price: number | undefined) => {\n    if (!price) return 'N/A';\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  };\n\n  const getScoreColor = (score: number) => {\n    if (score >= 0.8) return 'text-green-600';\n    if (score >= 0.6) return 'text-blue-600';\n    if (score >= 0.4) return 'text-yellow-600';\n    return 'text-muted-foreground';\n  };\n\n  const renderRecommendationCards = () => (\n    <div className=\"space-y-4\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center gap-2\">\n          <h3 className=\"text-lg font-semibold\">\n            {data.items.length} Recommendations\n          </h3>\n          {isAbTest && (\n            <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n              <Zap className=\"h-3 w-3\" />\n              A/B Test: {data.meta.variant} group\n            </Badge>\n          )}\n        </div>\n        <div className=\"flex gap-2\">\n          <Button\n            size=\"sm\"\n            variant={viewMode === 'cards' ? 'default' : 'outline'}\n            onClick={() => setViewMode('cards')}\n          >\n            Cards\n          </Button>\n          <Button\n            size=\"sm\"\n            variant={viewMode === 'table' ? 'default' : 'outline'}\n            onClick={() => setViewMode('table')}\n          >\n            Table\n          </Button>\n          <Button\n            size=\"sm\"\n            variant={viewMode === 'metrics' ? 'default' : 'outline'}\n            onClick={() => setViewMode('metrics')}\n          >\n            Metrics\n          </Button>\n        </div>\n      </div>\n\n      {viewMode === 'cards' && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto\">\n          {data.items.map((item: RecommendationItem, index: number) => (\n            <Card \n              key={item.id} \n              className={`cursor-pointer transition-all hover:shadow-md ${\n                selectedItem?.id === item.id ? 'ring-2 ring-blue-500' : ''\n              }`}\n              onClick={() => setSelectedItem(item)}\n            >\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-start justify-between mb-2\">\n                  <div className=\"flex-1\">\n                    <h4 className=\"font-medium text-sm mb-1\">{item.name}</h4>\n                    <div className=\"flex items-center gap-2 text-xs text-muted-foreground\">\n                      <span>{item.category}</span>\n                      {item.price && (\n                        <>\n                          <span>•</span>\n                          <span>{formatPrice(item.price)}</span>\n                        </>\n                      )}\n                      {item.averageRating && (\n                        <>\n                          <span>•</span>\n                          <div className=\"flex items-center gap-1\">\n                            <Star className=\"h-3 w-3 fill-yellow-400 text-yellow-400\" />\n                            <span>{item.averageRating.toFixed(1)}</span>\n                          </div>\n                        </>\n                      )}\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <Badge variant=\"outline\" className={getScoreColor(item.score)}>\n                      {(item.score * 100).toFixed(0)}%\n                    </Badge>\n                    <div className=\"text-xs text-muted-foreground mt-1\">\n                      #{index + 1}\n                    </div>\n                  </div>\n                </div>\n                \n                {item.explanation && (\n                  <div className=\"flex items-start gap-2 mt-2 p-2 bg-blue-50 rounded text-xs\">\n                    <Lightbulb className=\"h-3 w-3 text-blue-600 mt-0.5 flex-shrink-0\" />\n                    <span className=\"text-blue-700\">{item.explanation}</span>\n                  </div>\n                )}\n                \n                <div className=\"flex items-center justify-between mt-2\">\n                  <Badge variant=\"secondary\" className=\"text-xs\">\n                    {item.source || 'rayuela'}\n                  </Badge>\n                  <Progress value={item.score * 100} className=\"w-16 h-1\" />\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      )}\n\n      {viewMode === 'table' && (\n        <div className=\"border rounded-lg overflow-hidden\">\n          <div className=\"overflow-x-auto max-h-96\">\n            <table className=\"w-full text-sm\">\n              <thead className=\"bg-muted\">\n                <tr>\n                  <th className=\"text-left p-3 font-medium\">#</th>\n                  <th className=\"text-left p-3 font-medium\">Product</th>\n                  <th className=\"text-left p-3 font-medium\">Category</th>\n                  <th className=\"text-left p-3 font-medium\">Price</th>\n                  <th className=\"text-left p-3 font-medium\">Score</th>\n                  <th className=\"text-left p-3 font-medium\">Source</th>\n                </tr>\n              </thead>\n              <tbody>\n                {data.items.map((item: RecommendationItem, index: number) => (\n                  <tr \n                    key={item.id} \n                    className=\"border-t hover:bg-muted cursor-pointer\"\n                    onClick={() => setSelectedItem(item)}\n                  >\n                    <td className=\"p-3\">{index + 1}</td>\n                    <td className=\"p-3\">\n                      <div>\n                        <div className=\"font-medium\">{item.name}</div>\n                        {item.averageRating && (\n                          <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n                            <Star className=\"h-3 w-3 fill-yellow-400 text-yellow-400\" />\n                            <span>{item.averageRating.toFixed(1)}</span>\n                          </div>\n                        )}\n                      </div>\n                    </td>\n                    <td className=\"p-3\">{item.category}</td>\n                    <td className=\"p-3\">{formatPrice(item.price)}</td>\n                    <td className=\"p-3\">\n                      <Badge variant=\"outline\" className={getScoreColor(item.score)}>\n                        {(item.score * 100).toFixed(0)}%\n                      </Badge>\n                    </td>\n                    <td className=\"p-3\">\n                      <Badge variant=\"secondary\" className=\"text-xs\">\n                        {item.source || 'rayuela'}\n                      </Badge>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      )}\n\n      {viewMode === 'metrics' && (\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center gap-2 mb-2\">\n                <Target className=\"h-4 w-4 text-blue-500\" />\n                <span className=\"text-sm font-medium\">Avg Score</span>\n              </div>\n              <div className=\"text-2xl font-bold\">\n                {(data.items.reduce((sum: number, item: RecommendationItem) => sum + item.score, 0) / data.items.length * 100).toFixed(1)}%\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center gap-2 mb-2\">\n                <DollarSign className=\"h-4 w-4 text-green-500\" />\n                <span className=\"text-sm font-medium\">Avg Price</span>\n              </div>\n              <div className=\"text-2xl font-bold\">\n                {formatPrice(\n                  data.items\n                    .filter((item: RecommendationItem) => item.price)\n                    .reduce((sum: number, item: RecommendationItem) => sum + (item.price || 0), 0) / \n                  data.items.filter((item: RecommendationItem) => item.price).length\n                )}\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center gap-2 mb-2\">\n                <Award className=\"h-4 w-4 text-purple-500\" />\n                <span className=\"text-sm font-medium\">Categories</span>\n              </div>\n              <div className=\"text-2xl font-bold\">\n                {new Set(data.items.map((item: RecommendationItem) => item.category)).size}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderBusinessMetrics = () => (\n    <div className=\"space-y-6\">\n      <h3 className=\"text-lg font-semibold\">Business Impact Metrics</h3>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-2 mb-2\">\n              <TrendingUp className=\"h-4 w-4 text-green-500\" />\n              <span className=\"text-sm font-medium\">CTR Lift</span>\n            </div>\n            <div className=\"text-2xl font-bold text-green-600\">\n              {((data.ctr_lift || 0) * 100).toFixed(1)}%\n            </div>\n            <p className=\"text-xs text-muted-foreground\">vs baseline</p>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-2 mb-2\">\n              <ShoppingCart className=\"h-4 w-4 text-blue-500\" />\n              <span className=\"text-sm font-medium\">CVR Lift</span>\n            </div>\n            <div className=\"text-2xl font-bold text-blue-600\">\n              {((data.cvr_lift || 0) * 100).toFixed(1)}%\n            </div>\n            <p className=\"text-xs text-muted-foreground\">vs baseline</p>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-2 mb-2\">\n              <DollarSign className=\"h-4 w-4 text-green-500\" />\n              <span className=\"text-sm font-medium\">Revenue</span>\n            </div>\n            <div className=\"text-2xl font-bold\">\n              {formatPrice(data.revenue_attribution || 0)}\n            </div>\n            <p className=\"text-xs text-muted-foreground\">attributed</p>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-2 mb-2\">\n              <Users className=\"h-4 w-4 text-purple-500\" />\n              <span className=\"text-sm font-medium\">Engagement</span>\n            </div>\n            <div className=\"text-2xl font-bold\">\n              {((data.engagement_score || 0) * 100).toFixed(0)}\n            </div>\n            <p className=\"text-xs text-muted-foreground\">score</p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {data.recommendations?.next_steps && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-base\">Recommended Actions</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <ul className=\"space-y-2\">\n              {data.recommendations.next_steps.map((step: string, index: number) => (\n                <li key={index} className=\"flex items-start gap-2\">\n                  <span className=\"flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mt-0.5\">\n                    {index + 1}\n                  </span>\n                  <span className=\"text-sm\">{step}</span>\n                </li>\n              ))}\n            </ul>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n\n  const renderAbTestResults = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center gap-2\">\n        <h3 className=\"text-lg font-semibold\">A/B Test Results</h3>\n        <Badge variant=\"secondary\">\n          {data.variant || 'Unknown'} Group\n        </Badge>\n      </div>\n      \n      {data.control && data.treatment ? (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-base\">Control (Baseline)</CardTitle>\n              <CardDescription>Popular products baseline</CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm\">Users:</span>\n                <span className=\"font-medium\">{data.control.users}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm\">CTR:</span>\n                <span className=\"font-medium\">{(data.control.ctr * 100).toFixed(2)}%</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm\">CVR:</span>\n                <span className=\"font-medium\">{(data.control.cvr * 100).toFixed(2)}%</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm\">Revenue:</span>\n                <span className=\"font-medium\">{formatPrice(data.control.revenue)}</span>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-base\">Treatment (Rayuela)</CardTitle>\n              <CardDescription>Personalized recommendations</CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm\">Users:</span>\n                <span className=\"font-medium\">{data.treatment.users}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm\">CTR:</span>\n                <span className=\"font-medium\">{(data.treatment.ctr * 100).toFixed(2)}%</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm\">CVR:</span>\n                <span className=\"font-medium\">{(data.treatment.cvr * 100).toFixed(2)}%</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm\">Revenue:</span>\n                <span className=\"font-medium\">{formatPrice(data.treatment.revenue)}</span>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      ) : (\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"text-center text-muted-foreground\">\n              <BarChart3 className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n              <p>A/B test results will appear here once enough data is collected</p>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {data.lifts && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-base\">Performance Lift</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-green-600\">\n                  {data.lifts.ctrLiftPercentage || '+0%'}\n                </div>\n                <p className=\"text-sm text-muted-foreground\">CTR Improvement</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-blue-600\">\n                  {data.lifts.cvrLiftPercentage || '+0%'}\n                </div>\n                <p className=\"text-sm text-muted-foreground\">CVR Improvement</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n\n  return (\n    <Card>\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <CardTitle className=\"flex items-center gap-2\">\n            <Eye className=\"h-5 w-5\" />\n            Response Visualization\n          </CardTitle>\n          <div className=\"flex items-center gap-2\">\n            <Badge className={getStatusColor(response.status)}>\n              {response.status}\n            </Badge>\n            <Badge variant=\"outline\" className=\"flex items-center gap-1\">\n              <Clock className=\"h-3 w-3\" />\n              {response.responseTime}ms\n            </Badge>\n          </div>\n        </div>\n        <CardDescription>\n          Interactive visualization of API response data\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <Tabs defaultValue=\"visualization\">\n          <TabsList className=\"grid w-full grid-cols-2\">\n            <TabsTrigger value=\"visualization\">Visualization</TabsTrigger>\n            <TabsTrigger value=\"raw\">Raw Data</TabsTrigger>\n          </TabsList>\n          \n          <TabsContent value=\"visualization\" className=\"mt-6\">\n            {isRecommendations && renderRecommendationCards()}\n            {isMetrics && renderBusinessMetrics()}\n            {isAbTest && data.control && renderAbTestResults()}\n            \n            {!isRecommendations && !isMetrics && !isAbTest && (\n              <div className=\"text-center text-muted-foreground\">\n                <Info className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n                <p>This response type doesn't have a custom visualization yet</p>\n                <p className=\"text-sm\">Check the Raw Data tab to see the full response</p>\n              </div>\n            )}\n          </TabsContent>\n          \n          <TabsContent value=\"raw\" className=\"mt-6\">\n            <pre className=\"bg-muted p-4 rounded-lg overflow-x-auto text-sm max-h-96\">\n              {JSON.stringify(data, null, 2)}\n            </pre>\n          </TabsContent>\n        </Tabs>\n\n        {/* Selected Item Details */}\n        {selectedItem && (\n          <Card className=\"mt-6 border-blue-200 bg-blue-50\">\n            <CardHeader>\n              <CardTitle className=\"text-base\">Selected Item Details</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <h4 className=\"font-medium mb-2\">{selectedItem.name}</h4>\n                  <div className=\"space-y-1 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span>Category:</span>\n                      <span>{selectedItem.category}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Price:</span>\n                      <span>{formatPrice(selectedItem.price)}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Score:</span>\n                      <span className={getScoreColor(selectedItem.score)}>\n                        {(selectedItem.score * 100).toFixed(1)}%\n                      </span>\n                    </div>\n                    {selectedItem.averageRating && (\n                      <div className=\"flex justify-between\">\n                        <span>Rating:</span>\n                        <div className=\"flex items-center gap-1\">\n                          <Star className=\"h-3 w-3 fill-yellow-400 text-yellow-400\" />\n                          <span>{selectedItem.averageRating.toFixed(1)}</span>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n                <div>\n                  {selectedItem.explanation && (\n                    <div>\n                      <h5 className=\"font-medium mb-2 flex items-center gap-1\">\n                        <Lightbulb className=\"h-4 w-4\" />\n                        Why recommended?\n                      </h5>\n                      <p className=\"text-sm text-blue-700\">{selectedItem.explanation}</p>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAyCe,SAAS,mBAAmB,EAAE,QAAQ,EAAE,QAAQ,EAA2B;IACxF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAC5E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC;IAExE,IAAI,CAAC,YAAY,CAAC,SAAS,IAAI,EAAE;QAC/B,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,MAAM,OAAO,SAAS,IAAI;IAC1B,MAAM,oBAAoB,KAAK,KAAK,IAAI,MAAM,OAAO,CAAC,KAAK,KAAK;IAChE,MAAM,YAAY,KAAK,QAAQ,KAAK,aAAa,KAAK,QAAQ,KAAK;IACnE,MAAM,WAAW,KAAK,IAAI,EAAE,cAAc,KAAK,OAAO;IAEtD,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,OAAO,SAAS,KAAK,OAAO;QAC1C,IAAI,UAAU,OAAO,SAAS,KAAK,OAAO;QAC1C,IAAI,UAAU,KAAK,OAAO;QAC1B,OAAO;IACT;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,OAAO,OAAO;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,KAAK,OAAO;QACzB,OAAO;IACT;IAEA,MAAM,4BAA4B,kBAChC,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCACX,KAAK,KAAK,CAAC,MAAM;wCAAC;;;;;;;gCAEpB,0BACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;sDACnC,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;wCAChB,KAAK,IAAI,CAAC,OAAO;wCAAC;;;;;;;;;;;;;sCAInC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS,aAAa,UAAU,YAAY;oCAC5C,SAAS,IAAM,YAAY;8CAC5B;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS,aAAa,UAAU,YAAY;oCAC5C,SAAS,IAAM,YAAY;8CAC5B;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS,aAAa,YAAY,YAAY;oCAC9C,SAAS,IAAM,YAAY;8CAC5B;;;;;;;;;;;;;;;;;;gBAMJ,aAAa,yBACZ,8OAAC;oBAAI,WAAU;8BACZ,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAA0B,sBACzC,8OAAC,gIAAA,CAAA,OAAI;4BAEH,WAAW,CAAC,8CAA8C,EACxD,cAAc,OAAO,KAAK,EAAE,GAAG,yBAAyB,IACxD;4BACF,SAAS,IAAM,gBAAgB;sCAE/B,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA4B,KAAK,IAAI;;;;;;kEACnD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAM,KAAK,QAAQ;;;;;;4DACnB,KAAK,KAAK,kBACT;;kFACE,8OAAC;kFAAK;;;;;;kFACN,8OAAC;kFAAM,YAAY,KAAK,KAAK;;;;;;;;4DAGhC,KAAK,aAAa,kBACjB;;kFACE,8OAAC;kFAAK;;;;;;kFACN,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,8OAAC;0FAAM,KAAK,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAW,cAAc,KAAK,KAAK;;4DACzD,CAAC,KAAK,KAAK,GAAG,GAAG,EAAE,OAAO,CAAC;4DAAG;;;;;;;kEAEjC,8OAAC;wDAAI,WAAU;;4DAAqC;4DAChD,QAAQ;;;;;;;;;;;;;;;;;;;oCAKf,KAAK,WAAW,kBACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAK,WAAU;0DAAiB,KAAK,WAAW;;;;;;;;;;;;kDAIrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAClC,KAAK,MAAM,IAAI;;;;;;0DAElB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,OAAO,KAAK,KAAK,GAAG;gDAAK,WAAU;;;;;;;;;;;;;;;;;;2BAlD5C,KAAK,EAAE;;;;;;;;;;gBA0DnB,aAAa,yBACZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAM,WAAU;8CACf,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4B;;;;;;0DAC1C,8OAAC;gDAAG,WAAU;0DAA4B;;;;;;0DAC1C,8OAAC;gDAAG,WAAU;0DAA4B;;;;;;0DAC1C,8OAAC;gDAAG,WAAU;0DAA4B;;;;;;0DAC1C,8OAAC;gDAAG,WAAU;0DAA4B;;;;;;0DAC1C,8OAAC;gDAAG,WAAU;0DAA4B;;;;;;;;;;;;;;;;;8CAG9C,8OAAC;8CACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAA0B,sBACzC,8OAAC;4CAEC,WAAU;4CACV,SAAS,IAAM,gBAAgB;;8DAE/B,8OAAC;oDAAG,WAAU;8DAAO,QAAQ;;;;;;8DAC7B,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EAAe,KAAK,IAAI;;;;;;4DACtC,KAAK,aAAa,kBACjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;kFAAM,KAAK,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;8DAK1C,8OAAC;oDAAG,WAAU;8DAAO,KAAK,QAAQ;;;;;;8DAClC,8OAAC;oDAAG,WAAU;8DAAO,YAAY,KAAK,KAAK;;;;;;8DAC3C,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAW,cAAc,KAAK,KAAK;;4DACzD,CAAC,KAAK,KAAK,GAAG,GAAG,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;8DAGnC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;kEAClC,KAAK,MAAM,IAAI;;;;;;;;;;;;2CAzBf,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;gBAoCzB,aAAa,2BACZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,8OAAC;wCAAI,WAAU;;4CACZ,CAAC,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,KAAa,OAA6B,MAAM,KAAK,KAAK,EAAE,KAAK,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;;;;;sCAKhI,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,8OAAC;wCAAI,WAAU;kDACZ,YACC,KAAK,KAAK,CACP,MAAM,CAAC,CAAC,OAA6B,KAAK,KAAK,EAC/C,MAAM,CAAC,CAAC,KAAa,OAA6B,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG,KAC9E,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,OAA6B,KAAK,KAAK,EAAE,MAAM;;;;;;;;;;;;;;;;;sCAM1E,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,8OAAC;wCAAI,WAAU;kDACZ,IAAI,IAAI,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,OAA6B,KAAK,QAAQ,GAAG,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASxF,MAAM,wBAAwB,kBAC5B,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAwB;;;;;;8BAEtC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,8OAAC;wCAAI,WAAU;;4CACZ,CAAC,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;kDAE3C,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;sCAIjD,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,8OAAC;wCAAI,WAAU;;4CACZ,CAAC,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;kDAE3C,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;sCAIjD,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,8OAAC;wCAAI,WAAU;kDACZ,YAAY,KAAK,mBAAmB,IAAI;;;;;;kDAE3C,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;sCAIjD,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,8OAAC;wCAAI,WAAU;kDACZ,CAAC,CAAC,KAAK,gBAAgB,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC;;;;;;kDAEhD,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;gBAKlD,KAAK,eAAe,EAAE,4BACrB,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAY;;;;;;;;;;;sCAEnC,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAG,WAAU;0CACX,KAAK,eAAe,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,MAAc,sBAClD,8OAAC;wCAAe,WAAU;;0DACxB,8OAAC;gDAAK,WAAU;0DACb,QAAQ;;;;;;0DAEX,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;uCAJpB;;;;;;;;;;;;;;;;;;;;;;;;;;;IAcvB,MAAM,sBAAsB,kBAC1B,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwB;;;;;;sCACtC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;gCACZ,KAAK,OAAO,IAAI;gCAAU;;;;;;;;;;;;;gBAI9B,KAAK,OAAO,IAAI,KAAK,SAAS,iBAC7B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAY;;;;;;sDACjC,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAK,WAAU;8DAAe,KAAK,OAAO,CAAC,KAAK;;;;;;;;;;;;sDAEnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAK,WAAU;;wDAAe,CAAC,KAAK,OAAO,CAAC,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAErE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAK,WAAU;;wDAAe,CAAC,KAAK,OAAO,CAAC,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAErE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAK,WAAU;8DAAe,YAAY,KAAK,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;sCAKrE,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAY;;;;;;sDACjC,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAK,WAAU;8DAAe,KAAK,SAAS,CAAC,KAAK;;;;;;;;;;;;sDAErD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAK,WAAU;;wDAAe,CAAC,KAAK,SAAS,CAAC,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAEvE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAK,WAAU;;wDAAe,CAAC,KAAK,SAAS,CAAC,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAEvE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAK,WAAU;8DAAe,YAAY,KAAK,SAAS,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yCAMzE,8OAAC,gIAAA,CAAA,OAAI;8BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;gBAMV,KAAK,KAAK,kBACT,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAY;;;;;;;;;;;sCAEnC,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK,CAAC,iBAAiB,IAAI;;;;;;0DAEnC,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;kDAE/C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK,CAAC,iBAAiB,IAAI;;;;;;0DAEnC,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS3D,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG7B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAW,eAAe,SAAS,MAAM;kDAC7C,SAAS,MAAM;;;;;;kDAElB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;0DACjC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB,SAAS,YAAY;4CAAC;;;;;;;;;;;;;;;;;;;kCAI7B,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,cAAW;;kCACV,8OAAC,gIAAA,CAAA,OAAI;wBAAC,cAAa;;0CACjB,8OAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAgB;;;;;;kDACnC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAM;;;;;;;;;;;;0CAG3B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAgB,WAAU;;oCAC1C,qBAAqB;oCACrB,aAAa;oCACb,YAAY,KAAK,OAAO,IAAI;oCAE5B,CAAC,qBAAqB,CAAC,aAAa,CAAC,0BACpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAE;;;;;;0DACH,8OAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;;;;;;;0CAK7B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAM,WAAU;0CACjC,cAAA,8OAAC;oCAAI,WAAU;8CACZ,KAAK,SAAS,CAAC,MAAM,MAAM;;;;;;;;;;;;;;;;;oBAMjC,8BACC,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAY;;;;;;;;;;;0CAEnC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoB,aAAa,IAAI;;;;;;8DACnD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAM,aAAa,QAAQ;;;;;;;;;;;;sEAE9B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAM,YAAY,aAAa,KAAK;;;;;;;;;;;;sEAEvC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;oEAAK,WAAW,cAAc,aAAa,KAAK;;wEAC9C,CAAC,aAAa,KAAK,GAAG,GAAG,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;;wDAG1C,aAAa,aAAa,kBACzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;sFAAM,aAAa,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAMpD,8OAAC;sDACE,aAAa,WAAW,kBACvB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,4MAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGnC,8OAAC;wDAAE,WAAU;kEAAyB,aAAa,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWpF", "debugId": null}}, {"offset": {"line": 3641, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/api-explorer/IntegrationTemplates.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"@/components/ui/tabs\";\nimport {\n  Copy,\n  CheckCircle,\n  Download,\n  Rocket,\n  Code,\n  Package,\n  ShoppingCart,\n  FileText,\n  Globe\n} from \"lucide-react\";\n\ninterface IntegrationTemplate {\n  id: string;\n  name: string;\n  description: string;\n  category: 'ecommerce' | 'content' | 'saas' | 'marketplace';\n  framework: string;\n  difficulty: 'beginner' | 'intermediate' | 'advanced';\n  features: string[];\n  files: {\n    name: string;\n    content: string;\n    language: string;\n  }[];\n}\n\nconst INTEGRATION_TEMPLATES: IntegrationTemplate[] = [\n  {\n    id: 'react-ecommerce',\n    name: 'React E-commerce Store',\n    description: 'Complete e-commerce integration with homepage, product pages, and cart recommendations',\n    category: 'ecommerce',\n    framework: 'React',\n    difficulty: 'intermediate',\n    features: ['Homepage recommendations', 'Product page cross-sell', 'Cart upsells', 'A/B testing', 'Analytics tracking'],\n    files: [\n      {\n        name: 'components/RecommendationWidget.tsx',\n        language: 'typescript',\n        content: 'import React, { useState, useEffect } from \"react\";\\nimport Rayuela from \"rayuela-sdk\";\\n\\n// React component for recommendations\\nexport default function RecommendationWidget() {\\n  // Component implementation here\\n  return <div>Recommendations</div>;\\n}'\n      },\n      {\n        name: 'hooks/useRecommendations.ts',\n        language: 'typescript',\n        content: 'import { useState, useEffect } from \"react\";\\nimport Rayuela from \"rayuela-sdk\";\\n\\n// Custom hook for recommendations\\nexport function useRecommendations(userId: string) {\\n  // Hook implementation here\\n  return { recommendations: [], loading: false };\\n}'\n      }\n    ]\n  },\n  {\n    id: 'nextjs-api',\n    name: 'Next.js API Integration',\n    description: 'Server-side API routes with caching and error handling',\n    category: 'ecommerce',\n    framework: 'Next.js',\n    difficulty: 'intermediate',\n    features: ['Server-side rendering', 'API caching', 'Error handling', 'Rate limiting'],\n    files: [\n      {\n        name: 'pages/api/recommendations/[userId].ts',\n        language: 'typescript',\n        content: 'import type { NextApiRequest, NextApiResponse } from \"next\";\\nimport Rayuela from \"rayuela-sdk\";\\n\\n// Next.js API route\\nexport default async function handler(req: NextApiRequest, res: NextApiResponse) {\\n  // API implementation here\\n  res.json({ success: true });\\n}'\n      }\n    ]\n  },\n  {\n    id: 'python-flask',\n    name: 'Python Flask Application',\n    description: 'Flask web application with recommendations and analytics',\n    category: 'ecommerce',\n    framework: 'Flask',\n    difficulty: 'intermediate',\n    features: ['Flask routes', 'Template integration', 'Error handling', 'Analytics'],\n    files: [\n      {\n        name: 'app.py',\n        language: 'python',\n        content: 'from flask import Flask, render_template\\nfrom rayuela import Rayuela\\n\\n# Flask application\\napp = Flask(__name__)\\nclient = Rayuela(api_key=\"your-api-key\")\\n\\<EMAIL>(\"/\")\\ndef homepage():\\n    return render_template(\"homepage.html\")'\n      }\n    ]\n  }\n];\n\nfunction IntegrationTemplates() {\n  const [selectedTemplate, setSelectedTemplate] = useState<IntegrationTemplate>(INTEGRATION_TEMPLATES[0]);\n  const [selectedFile, setSelectedFile] = useState(0);\n  const [copiedCode, setCopiedCode] = useState<string | null>(null);\n\n  const copyToClipboard = async (text: string, id: string) => {\n    try {\n      await navigator.clipboard.writeText(text);\n      setCopiedCode(id);\n      setTimeout(() => setCopiedCode(null), 2000);\n    } catch (err) {\n      console.error('Failed to copy: ', err);\n    }\n  };\n\n  const downloadTemplate = () => {\n    const zip = selectedTemplate.files.map(file => ({\n      name: file.name,\n      content: file.content\n    }));\n\n    // Create a simple text file with all files\n    const allFiles = zip.map(file =>\n      `// File: ${file.name}\\n${file.content}\\n\\n${'='.repeat(80)}\\n\\n`\n    ).join('');\n\n    const blob = new Blob([allFiles], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `${selectedTemplate.id}-template.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  const getDifficultyColor = (difficulty: string) => {\n    switch (difficulty) {\n      case 'beginner': return 'bg-green-100 text-green-800';\n      case 'intermediate': return 'bg-yellow-100 text-yellow-800';\n      case 'advanced': return 'bg-red-100 text-red-800';\n      default: return 'bg-muted text-foreground';\n    }\n  };\n\n  const getCategoryIcon = (category: string) => {\n    switch (category) {\n      case 'ecommerce': return <ShoppingCart className=\"h-4 w-4\" />;\n      case 'content': return <FileText className=\"h-4 w-4\" />;\n      case 'saas': return <Globe className=\"h-4 w-4\" />;\n      case 'marketplace': return <Package className=\"h-4 w-4\" />;\n      default: return <Code className=\"h-4 w-4\" />;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"text-center\">\n        <h1 className=\"text-3xl font-bold mb-2\">Integration Templates</h1>\n        <p className=\"text-muted-foreground\">\n          Production-ready code templates you can copy and paste into your project\n        </p>\n      </div>\n\n      {/* Template Selection */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Rocket className=\"h-5 w-5\" />\n            Choose Your Template\n          </CardTitle>\n          <CardDescription>\n            Select a template that matches your technology stack\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {INTEGRATION_TEMPLATES.map((template) => (\n              <Card\n                key={template.id}\n                className={`cursor-pointer transition-all hover:shadow-md ${\n                  selectedTemplate.id === template.id ? 'ring-2 ring-blue-500' : ''\n                }`}\n                onClick={() => {\n                  setSelectedTemplate(template);\n                  setSelectedFile(0);\n                }}\n              >\n                <CardContent className=\"p-4\">\n                  <div className=\"flex items-center gap-2 mb-2\">\n                    {getCategoryIcon(template.category)}\n                    <h3 className=\"font-semibold\">{template.name}</h3>\n                  </div>\n                  <p className=\"text-sm text-muted-foreground mb-3\">{template.description}</p>\n                  <div className=\"flex items-center justify-between\">\n                    <Badge variant=\"outline\">{template.framework}</Badge>\n                    <Badge className={getDifficultyColor(template.difficulty)}>\n                      {template.difficulty}\n                    </Badge>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Template Details */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <CardTitle className=\"flex items-center gap-2\">\n                {getCategoryIcon(selectedTemplate.category)}\n                {selectedTemplate.name}\n              </CardTitle>\n              <CardDescription>{selectedTemplate.description}</CardDescription>\n            </div>\n            <div className=\"flex gap-2\">\n              <Button onClick={downloadTemplate} variant=\"outline\">\n                <Download className=\"h-4 w-4 mr-2\" />\n                Download All\n              </Button>\n            </div>\n          </div>\n        </CardHeader>\n        <CardContent>\n          {/* Features */}\n          <div className=\"mb-6\">\n            <h4 className=\"font-semibold mb-3\">Features Included</h4>\n            <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n              {selectedTemplate.features.map((feature, index) => (\n                <div key={index} className=\"flex items-center gap-2\">\n                  <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                  <span className=\"text-sm\">{feature}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* File Tabs */}\n          <Tabs value={selectedFile.toString()} onValueChange={(value) => setSelectedFile(Number(value))}>\n            <TabsList className=\"grid w-full grid-cols-auto overflow-x-auto\">\n              {selectedTemplate.files.map((file, index) => (\n                <TabsTrigger key={index} value={index.toString()} className=\"text-xs\">\n                  {file.name}\n                </TabsTrigger>\n              ))}\n            </TabsList>\n\n            {selectedTemplate.files.map((file, index) => (\n              <TabsContent key={index} value={index.toString()} className=\"mt-4\">\n                <div className=\"relative\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <div className=\"flex items-center gap-2\">\n                      <Code className=\"h-4 w-4\" />\n                      <span className=\"font-medium\">{file.name}</span>\n                      <Badge variant=\"secondary\" className=\"text-xs\">\n                        {file.language}\n                      </Badge>\n                    </div>\n                    <Button\n                      size=\"sm\"\n                      variant=\"outline\"\n                      onClick={() => copyToClipboard(file.content, `${selectedTemplate.id}-${index}`)}\n                    >\n                      {copiedCode === `${selectedTemplate.id}-${index}` ? (\n                        <CheckCircle className=\"h-4 w-4\" />\n                      ) : (\n                        <Copy className=\"h-4 w-4\" />\n                      )}\n                    </Button>\n                  </div>\n                  <pre className=\"bg-muted p-4 rounded-lg overflow-x-auto text-sm\">\n                    <code>{file.content}</code>\n                  </pre>\n                </div>\n              </TabsContent>\n            ))}\n          </Tabs>\n\n          {/* Setup Instructions */}\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n            <h4 className=\"font-semibold mb-2 flex items-center gap-2\">\n              <Rocket className=\"h-4 w-4\" />\n              Quick Setup\n            </h4>\n            <div className=\"space-y-2 text-sm\">\n              <div>\n                <strong>1. Install dependencies:</strong>\n                <code className=\"block bg-white px-2 py-1 rounded border mt-1\">\n                  {selectedTemplate.framework === 'React' && 'npm install rayuela-sdk'}\n                  {selectedTemplate.framework === 'Next.js' && 'npm install rayuela-sdk'}\n                  {selectedTemplate.framework === 'Flask' && 'pip install rayuela-sdk'}\n                </code>\n              </div>\n              <div>\n                <strong>2. Set up environment variables:</strong>\n                <code className=\"block bg-white px-2 py-1 rounded border mt-1\">\n                  {selectedTemplate.framework.includes('React') || selectedTemplate.framework === 'Next.js'\n                    ? 'NEXT_PUBLIC_RAYUELA_API_KEY=ray_your_api_key_here'\n                    : 'RAYUELA_API_KEY=ray_your_api_key_here'\n                  }\n                </code>\n              </div>\n              <div>\n                <strong>3. Copy the files above into your project</strong>\n              </div>\n              <div>\n                <strong>4. Start using recommendations!</strong>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n\nexport default IntegrationTemplates;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAkCA,MAAM,wBAA+C;IACnD;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,WAAW;QACX,YAAY;QACZ,UAAU;YAAC;YAA4B;YAA2B;YAAgB;YAAe;SAAqB;QACtH,OAAO;YACL;gBACE,MAAM;gBACN,UAAU;gBACV,SAAS;YACX;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,SAAS;YACX;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,WAAW;QACX,YAAY;QACZ,UAAU;YAAC;YAAyB;YAAe;YAAkB;SAAgB;QACrF,OAAO;YACL;gBACE,MAAM;gBACN,UAAU;gBACV,SAAS;YACX;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,WAAW;QACX,YAAY;QACZ,UAAU;YAAC;YAAgB;YAAwB;YAAkB;SAAY;QACjF,OAAO;YACL;gBACE,MAAM;gBACN,UAAU;gBACV,SAAS;YACX;SACD;IACH;CACD;AAED,SAAS;IACP,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,qBAAqB,CAAC,EAAE;IACtG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,MAAM,kBAAkB,OAAO,MAAc;QAC3C,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,cAAc;YACd,WAAW,IAAM,cAAc,OAAO;QACxC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oBAAoB;QACpC;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,MAAM,iBAAiB,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC9C,MAAM,KAAK,IAAI;gBACf,SAAS,KAAK,OAAO;YACvB,CAAC;QAED,2CAA2C;QAC3C,MAAM,WAAW,IAAI,GAAG,CAAC,CAAA,OACvB,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,OAAO,CAAC,IAAI,EAAE,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,EACjE,IAAI,CAAC;QAEP,MAAM,OAAO,IAAI,KAAK;YAAC;SAAS,EAAE;YAAE,MAAM;QAAa;QACvD,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,GAAG,iBAAiB,EAAE,CAAC,aAAa,CAAC;QAClD,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAa,qBAAO,8OAAC,sNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACjD,KAAK;gBAAW,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC3C,KAAK;gBAAQ,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACrC,KAAK;gBAAe,qBAAO,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC9C;gBAAS,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAMvC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGhC,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,sBAAsB,GAAG,CAAC,CAAC,yBAC1B,8OAAC,gIAAA,CAAA,OAAI;oCAEH,WAAW,CAAC,8CAA8C,EACxD,iBAAiB,EAAE,KAAK,SAAS,EAAE,GAAG,yBAAyB,IAC/D;oCACF,SAAS;wCACP,oBAAoB;wCACpB,gBAAgB;oCAClB;8CAEA,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;oDACZ,gBAAgB,SAAS,QAAQ;kEAClC,8OAAC;wDAAG,WAAU;kEAAiB,SAAS,IAAI;;;;;;;;;;;;0DAE9C,8OAAC;gDAAE,WAAU;0DAAsC,SAAS,WAAW;;;;;;0DACvE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW,SAAS,SAAS;;;;;;kEAC5C,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAW,mBAAmB,SAAS,UAAU;kEACrD,SAAS,UAAU;;;;;;;;;;;;;;;;;;mCAlBrB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;0BA6B1B,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;gDAClB,gBAAgB,iBAAiB,QAAQ;gDACzC,iBAAiB,IAAI;;;;;;;sDAExB,8OAAC,gIAAA,CAAA,kBAAe;sDAAE,iBAAiB,WAAW;;;;;;;;;;;;8CAEhD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAkB,SAAQ;;0DACzC,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,8OAAC,gIAAA,CAAA,cAAW;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAI,WAAU;kDACZ,iBAAiB,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACvC,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;+CAFnB;;;;;;;;;;;;;;;;0CAShB,8OAAC,gIAAA,CAAA,OAAI;gCAAC,OAAO,aAAa,QAAQ;gCAAI,eAAe,CAAC,QAAU,gBAAgB,OAAO;;kDACrF,8OAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAU;kDACjB,iBAAiB,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACjC,8OAAC,gIAAA,CAAA,cAAW;gDAAa,OAAO,MAAM,QAAQ;gDAAI,WAAU;0DACzD,KAAK,IAAI;+CADM;;;;;;;;;;oCAMrB,iBAAiB,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACjC,8OAAC,gIAAA,CAAA,cAAW;4CAAa,OAAO,MAAM,QAAQ;4CAAI,WAAU;sDAC1D,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;wEAAK,WAAU;kFAAe,KAAK,IAAI;;;;;;kFACxC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAY,WAAU;kFAClC,KAAK,QAAQ;;;;;;;;;;;;0EAGlB,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,gBAAgB,KAAK,OAAO,EAAE,GAAG,iBAAiB,EAAE,CAAC,CAAC,EAAE,OAAO;0EAE7E,eAAe,GAAG,iBAAiB,EAAE,CAAC,CAAC,EAAE,OAAO,iBAC/C,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;yFAEvB,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAItB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;sEAAM,KAAK,OAAO;;;;;;;;;;;;;;;;;2CAvBP;;;;;;;;;;;0CA+BtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;kEACR,8OAAC;wDAAK,WAAU;;4DACb,iBAAiB,SAAS,KAAK,WAAW;4DAC1C,iBAAiB,SAAS,KAAK,aAAa;4DAC5C,iBAAiB,SAAS,KAAK,WAAW;;;;;;;;;;;;;0DAG/C,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;kEACR,8OAAC;wDAAK,WAAU;kEACb,iBAAiB,SAAS,CAAC,QAAQ,CAAC,YAAY,iBAAiB,SAAS,KAAK,YAC5E,sDACA;;;;;;;;;;;;0DAIR,8OAAC;0DACC,cAAA,8OAAC;8DAAO;;;;;;;;;;;0DAEV,8OAAC;0DACC,cAAA,8OAAC;8DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxB;uCAEe", "debugId": null}}, {"offset": {"line": 4356, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/api-explorer/ApiExplorer.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\nimport { Badge } from \"@/components/ui/badge\";\nimport {\n  Play,\n  Settings,\n  RefreshCw,\n  Zap\n} from \"lucide-react\";\nimport CodeGenerator from \"./CodeGenerator\";\nimport ResponseVisualizer from \"./ResponseVisualizer\";\nimport IntegrationTemplates from \"./IntegrationTemplates\";\n\ninterface ApiEndpoint {\n  id: string;\n  name: string;\n  method: string;\n  path: string;\n  description: string;\n  category: string;\n  parameters: {\n    name: string;\n    type: string;\n    required: boolean;\n    description: string;\n    default?: any;\n    options?: string[];\n  }[];\n}\n\ninterface ApiResponse {\n  status: number;\n  data: any;\n  headers: Record<string, string>;\n  responseTime: number;\n}\n\nconst API_ENDPOINTS: ApiEndpoint[] = [\n  {\n    id: \"ecommerce-homepage\",\n    name: \"E-commerce Homepage\",\n    method: \"POST\",\n    path: \"/api/v1/recommendations/ecommerce/homepage\",\n    description: \"Get optimized recommendations for e-commerce homepage\",\n    category: \"E-commerce\",\n    parameters: [\n      { name: \"external_user_id\", type: \"string\", required: true, description: \"User identifier\", default: \"demo-user-001\" },\n      { name: \"limit\", type: \"number\", required: false, description: \"Number of recommendations\", default: 10, options: [\"5\", \"10\", \"15\", \"20\"] },\n      { name: \"category\", type: \"string\", required: false, description: \"Filter by category\", options: [\"electronics\", \"books\", \"clothing\", \"home\"] },\n      { name: \"min_rating\", type: \"number\", required: false, description: \"Minimum product rating\", options: [\"3.0\", \"4.0\", \"4.5\"] }\n    ]\n  },\n  {\n    id: \"quick-start-demo\",\n    name: \"Quick Start Demo\",\n    method: \"GET\",\n    path: \"/api/v1/sandbox/quick-start/demo\",\n    description: \"Get instant demo recommendations with sample data\",\n    category: \"Quick Start\",\n    parameters: [\n      { name: \"user_id\", type: \"string\", required: false, description: \"Demo user ID\", default: \"demo-user-001\" },\n      { name: \"template\", type: \"string\", required: false, description: \"Template type\", default: \"ecommerce\", options: [\"ecommerce\", \"content\", \"marketplace\"] }\n    ]\n  },\n  {\n    id: \"ab-test-recommendations\",\n    name: \"A/B Test Recommendations\",\n    method: \"POST\",\n    path: \"/api/v1/recommendations/ab-test/recommendations\",\n    description: \"Get recommendations with automatic A/B testing vs baseline\",\n    category: \"A/B Testing\",\n    parameters: [\n      { name: \"external_user_id\", type: \"string\", required: true, description: \"User identifier\", default: \"test-user-001\" },\n      { name: \"limit\", type: \"number\", required: false, description: \"Number of recommendations\", default: 10 },\n      { name: \"experiment_id\", type: \"string\", required: false, description: \"Experiment ID (optional)\" }\n    ]\n  },\n  {\n    id: \"business-metrics\",\n    name: \"Business Metrics\",\n    method: \"GET\",\n    path: \"/api/v1/analytics/business-metrics\",\n    description: \"Get real-time business impact metrics\",\n    category: \"Analytics\",\n    parameters: []\n  }\n];\n\nexport default function ApiExplorer() {\n  const [selectedEndpoint, setSelectedEndpoint] = useState<ApiEndpoint>(API_ENDPOINTS[0]);\n  const [parameters, setParameters] = useState<Record<string, any>>({});\n  const [apiKey, setApiKey] = useState(\"ray_demo_key_for_testing\");\n  const [response, setResponse] = useState<ApiResponse | null>(null);\n  const [loading, setLoading] = useState(false);\n  // Initialize parameters with defaults\n  useEffect(() => {\n    const defaultParams: Record<string, any> = {};\n    selectedEndpoint.parameters.forEach(param => {\n      if (param.default !== undefined) {\n        defaultParams[param.name] = param.default;\n      }\n    });\n    setParameters(defaultParams);\n  }, [selectedEndpoint]);\n\n  const executeRequest = async () => {\n    setLoading(true);\n    setResponse(null);\n\n    try {\n      const startTime = Date.now();\n      \n      // Build request\n      const url = `http://localhost:8001${selectedEndpoint.path}`;\n      const headers: Record<string, string> = {\n        'Content-Type': 'application/json',\n        'X-API-Key': apiKey\n      };\n\n      let requestOptions: RequestInit = {\n        method: selectedEndpoint.method,\n        headers\n      };\n\n      // Add body for POST requests\n      if (selectedEndpoint.method === 'POST') {\n        requestOptions.body = JSON.stringify(parameters);\n      }\n\n      // Add query parameters for GET requests\n      let finalUrl = url;\n      if (selectedEndpoint.method === 'GET' && Object.keys(parameters).length > 0) {\n        const queryParams = new URLSearchParams();\n        Object.entries(parameters).forEach(([key, value]) => {\n          if (value !== undefined && value !== '') {\n            queryParams.append(key, value.toString());\n          }\n        });\n        finalUrl = `${url}?${queryParams.toString()}`;\n      }\n\n      const response = await fetch(finalUrl, requestOptions);\n      const responseTime = Date.now() - startTime;\n      const data = await response.json();\n\n      setResponse({\n        status: response.status,\n        data,\n        headers: Object.fromEntries(response.headers.entries()),\n        responseTime\n      });\n\n    } catch (error) {\n      setResponse({\n        status: 0,\n        data: { error: error instanceof Error ? error.message : 'Unknown error' },\n        headers: {},\n        responseTime: 0\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"text-center\">\n        <h1 className=\"text-3xl font-bold mb-2\">Interactive API Explorer</h1>\n        <p className=\"text-muted-foreground\">\n          Test Rayuela API endpoints in real-time and generate code automatically\n        </p>\n      </div>\n\n      <Tabs defaultValue=\"explorer\" className=\"w-full\">\n        <TabsList className=\"grid w-full grid-cols-3\">\n          <TabsTrigger value=\"explorer\">API Explorer</TabsTrigger>\n          <TabsTrigger value=\"templates\">Integration Templates</TabsTrigger>\n          <TabsTrigger value=\"docs\">Documentation</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"explorer\" className=\"mt-6\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Left Panel - Request Configuration */}\n        <div className=\"space-y-6\">\n          {/* API Key */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Settings className=\"h-5 w-5\" />\n                Configuration\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div>\n                  <Label htmlFor=\"apiKey\">API Key</Label>\n                  <Input\n                    id=\"apiKey\"\n                    value={apiKey}\n                    onChange={(e) => setApiKey(e.target.value)}\n                    placeholder=\"ray_your_api_key_here\"\n                    className=\"font-mono\"\n                  />\n                  <p className=\"text-sm text-muted-foreground mt-1\">\n                    Get your API key at <a href=\"https://dashboard.rayuela.ai\" className=\"text-blue-600 hover:underline\">dashboard.rayuela.ai</a>\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Endpoint Selection */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Zap className=\"h-5 w-5\" />\n                Select Endpoint\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {API_ENDPOINTS.map((endpoint) => (\n                  <div\n                    key={endpoint.id}\n                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${\n                      selectedEndpoint.id === endpoint.id\n                        ? 'border-blue-500 bg-blue-50'\n                        : 'border-border hover:border-border'\n                    }`}\n                    onClick={() => setSelectedEndpoint(endpoint)}\n                  >\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <div className=\"flex items-center gap-2\">\n                        <Badge variant={endpoint.method === 'GET' ? 'secondary' : 'default'}>\n                          {endpoint.method}\n                        </Badge>\n                        <span className=\"font-medium\">{endpoint.name}</span>\n                      </div>\n                      <Badge variant=\"outline\">{endpoint.category}</Badge>\n                    </div>\n                    <p className=\"text-sm text-muted-foreground\">{endpoint.description}</p>\n                    <code className=\"text-xs text-muted-foreground mt-1 block\">{endpoint.path}</code>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Parameters */}\n          {selectedEndpoint.parameters.length > 0 && (\n            <Card>\n              <CardHeader>\n                <CardTitle>Parameters</CardTitle>\n                <CardDescription>\n                  Configure the parameters for {selectedEndpoint.name}\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {selectedEndpoint.parameters.map((param) => (\n                    <div key={param.name}>\n                      <Label htmlFor={param.name} className=\"flex items-center gap-2\">\n                        {param.name}\n                        {param.required && <span className=\"text-red-500\">*</span>}\n                      </Label>\n                      {param.options ? (\n                        <Select\n                          value={parameters[param.name]?.toString() || ''}\n                          onValueChange={(value) => setParameters(prev => ({ ...prev, [param.name]: value }))}\n                        >\n                          <SelectTrigger>\n                            <SelectValue placeholder={`Select ${param.name}`} />\n                          </SelectTrigger>\n                          <SelectContent>\n                            {param.options.map((option) => (\n                              <SelectItem key={option} value={option}>\n                                {option}\n                              </SelectItem>\n                            ))}\n                          </SelectContent>\n                        </Select>\n                      ) : (\n                        <Input\n                          id={param.name}\n                          type={param.type === 'number' ? 'number' : 'text'}\n                          value={parameters[param.name] || ''}\n                          onChange={(e) => setParameters(prev => ({ \n                            ...prev, \n                            [param.name]: param.type === 'number' ? Number(e.target.value) : e.target.value \n                          }))}\n                          placeholder={param.description}\n                        />\n                      )}\n                      <p className=\"text-sm text-muted-foreground mt-1\">{param.description}</p>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Execute Button */}\n          <Button \n            onClick={executeRequest} \n            disabled={loading}\n            className=\"w-full\"\n            size=\"lg\"\n          >\n            {loading ? (\n              <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />\n            ) : (\n              <Play className=\"h-4 w-4 mr-2\" />\n            )}\n            {loading ? 'Executing...' : 'Execute Request'}\n          </Button>\n        </div>\n\n        {/* Right Panel - Response and Code */}\n        <div className=\"space-y-6\">\n          {/* Response Visualization */}\n          <ResponseVisualizer response={response} endpoint={selectedEndpoint} />\n\n          {/* Code Generation */}\n          <CodeGenerator\n            endpoint={selectedEndpoint}\n            parameters={parameters}\n            apiKey={apiKey}\n          />\n        </div>\n      </div>\n        </TabsContent>\n\n        <TabsContent value=\"templates\" className=\"mt-6\">\n          <IntegrationTemplates />\n        </TabsContent>\n\n        <TabsContent value=\"docs\" className=\"mt-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>API Documentation</CardTitle>\n              <CardDescription>\n                Comprehensive guide to using Rayuela API\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-6\">\n                <div>\n                  <h3 className=\"text-lg font-semibold mb-3\">Quick Start Guide</h3>\n                  <div className=\"space-y-4\">\n                    <div className=\"p-4 border rounded-lg\">\n                      <h4 className=\"font-medium mb-2\">1. Get Your API Key</h4>\n                      <p className=\"text-sm text-muted-foreground\">\n                        Sign up at <a href=\"https://dashboard.rayuela.ai\" className=\"text-blue-600 hover:underline\">dashboard.rayuela.ai</a> to get your API key.\n                      </p>\n                    </div>\n                    <div className=\"p-4 border rounded-lg\">\n                      <h4 className=\"font-medium mb-2\">2. Install the SDK</h4>\n                      <code className=\"block bg-muted px-2 py-1 rounded text-sm\">\n                        npm install rayuela-sdk\n                      </code>\n                    </div>\n                    <div className=\"p-4 border rounded-lg\">\n                      <h4 className=\"font-medium mb-2\">3. Start Getting Recommendations</h4>\n                      <pre className=\"bg-muted p-2 rounded text-sm overflow-x-auto\">\n{`import Rayuela from 'rayuela-sdk';\n\nconst client = new Rayuela({ apiKey: 'your-api-key' });\nconst recs = await client.recommend('user-123');`}\n                      </pre>\n                    </div>\n                  </div>\n                </div>\n\n                <div>\n                  <h3 className=\"text-lg font-semibold mb-3\">Key Features</h3>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div className=\"p-4 border rounded-lg\">\n                      <h4 className=\"font-medium mb-2\">🎯 Personalized Recommendations</h4>\n                      <p className=\"text-sm text-muted-foreground\">\n                        AI-powered recommendations that adapt to user behavior and preferences.\n                      </p>\n                    </div>\n                    <div className=\"p-4 border rounded-lg\">\n                      <h4 className=\"font-medium mb-2\">🧪 A/B Testing</h4>\n                      <p className=\"text-sm text-muted-foreground\">\n                        Automatic comparison vs baseline with statistical significance testing.\n                      </p>\n                    </div>\n                    <div className=\"p-4 border rounded-lg\">\n                      <h4 className=\"font-medium mb-2\">📊 Real-time Analytics</h4>\n                      <p className=\"text-sm text-muted-foreground\">\n                        Track CTR, CVR, and revenue attribution in real-time.\n                      </p>\n                    </div>\n                    <div className=\"p-4 border rounded-lg\">\n                      <h4 className=\"font-medium mb-2\">🚀 Quick Integration</h4>\n                      <p className=\"text-sm text-muted-foreground\">\n                        Get started in minutes with our SDKs and templates.\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAMA;AACA;AACA;AAlBA;;;;;;;;;;;;;;AA4CA,MAAM,gBAA+B;IACnC;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM;QACN,aAAa;QACb,UAAU;QACV,YAAY;YACV;gBAAE,MAAM;gBAAoB,MAAM;gBAAU,UAAU;gBAAM,aAAa;gBAAmB,SAAS;YAAgB;YACrH;gBAAE,MAAM;gBAAS,MAAM;gBAAU,UAAU;gBAAO,aAAa;gBAA6B,SAAS;gBAAI,SAAS;oBAAC;oBAAK;oBAAM;oBAAM;iBAAK;YAAC;YAC1I;gBAAE,MAAM;gBAAY,MAAM;gBAAU,UAAU;gBAAO,aAAa;gBAAsB,SAAS;oBAAC;oBAAe;oBAAS;oBAAY;iBAAO;YAAC;YAC9I;gBAAE,MAAM;gBAAc,MAAM;gBAAU,UAAU;gBAAO,aAAa;gBAA0B,SAAS;oBAAC;oBAAO;oBAAO;iBAAM;YAAC;SAC9H;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM;QACN,aAAa;QACb,UAAU;QACV,YAAY;YACV;gBAAE,MAAM;gBAAW,MAAM;gBAAU,UAAU;gBAAO,aAAa;gBAAgB,SAAS;YAAgB;YAC1G;gBAAE,MAAM;gBAAY,MAAM;gBAAU,UAAU;gBAAO,aAAa;gBAAiB,SAAS;gBAAa,SAAS;oBAAC;oBAAa;oBAAW;iBAAc;YAAC;SAC3J;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM;QACN,aAAa;QACb,UAAU;QACV,YAAY;YACV;gBAAE,MAAM;gBAAoB,MAAM;gBAAU,UAAU;gBAAM,aAAa;gBAAmB,SAAS;YAAgB;YACrH;gBAAE,MAAM;gBAAS,MAAM;gBAAU,UAAU;gBAAO,aAAa;gBAA6B,SAAS;YAAG;YACxG;gBAAE,MAAM;gBAAiB,MAAM;gBAAU,UAAU;gBAAO,aAAa;YAA2B;SACnG;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM;QACN,aAAa;QACb,UAAU;QACV,YAAY,EAAE;IAChB;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,aAAa,CAAC,EAAE;IACtF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IACnE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAqC,CAAC;QAC5C,iBAAiB,UAAU,CAAC,OAAO,CAAC,CAAA;YAClC,IAAI,MAAM,OAAO,KAAK,WAAW;gBAC/B,aAAa,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,OAAO;YAC3C;QACF;QACA,cAAc;IAChB,GAAG;QAAC;KAAiB;IAErB,MAAM,iBAAiB;QACrB,WAAW;QACX,YAAY;QAEZ,IAAI;YACF,MAAM,YAAY,KAAK,GAAG;YAE1B,gBAAgB;YAChB,MAAM,MAAM,CAAC,qBAAqB,EAAE,iBAAiB,IAAI,EAAE;YAC3D,MAAM,UAAkC;gBACtC,gBAAgB;gBAChB,aAAa;YACf;YAEA,IAAI,iBAA8B;gBAChC,QAAQ,iBAAiB,MAAM;gBAC/B;YACF;YAEA,6BAA6B;YAC7B,IAAI,iBAAiB,MAAM,KAAK,QAAQ;gBACtC,eAAe,IAAI,GAAG,KAAK,SAAS,CAAC;YACvC;YAEA,wCAAwC;YACxC,IAAI,WAAW;YACf,IAAI,iBAAiB,MAAM,KAAK,SAAS,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,GAAG;gBAC3E,MAAM,cAAc,IAAI;gBACxB,OAAO,OAAO,CAAC,YAAY,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;oBAC9C,IAAI,UAAU,aAAa,UAAU,IAAI;wBACvC,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;oBACxC;gBACF;gBACA,WAAW,GAAG,IAAI,CAAC,EAAE,YAAY,QAAQ,IAAI;YAC/C;YAEA,MAAM,WAAW,MAAM,MAAM,UAAU;YACvC,MAAM,eAAe,KAAK,GAAG,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,YAAY;gBACV,QAAQ,SAAS,MAAM;gBACvB;gBACA,SAAS,OAAO,WAAW,CAAC,SAAS,OAAO,CAAC,OAAO;gBACpD;YACF;QAEF,EAAE,OAAO,OAAO;YACd,YAAY;gBACV,QAAQ;gBACR,MAAM;oBAAE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAAgB;gBACxE,SAAS,CAAC;gBACV,cAAc;YAChB;QACF,SAAU;YACR,WAAW;QACb;IACF;IAIA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKvC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAW,WAAU;;kCACtC,8OAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;0CAC9B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAY;;;;;;0CAC/B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAO;;;;;;;;;;;;kCAG5B,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;kCACtC,cAAA,8OAAC;4BAAI,WAAU;;8CAEjB,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;8DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;8DAIpC,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;;8EACC,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAS;;;;;;8EACxB,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,OAAO;oEACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oEACzC,aAAY;oEACZ,WAAU;;;;;;8EAEZ,8OAAC;oEAAE,WAAU;;wEAAqC;sFAC5B,8OAAC;4EAAE,MAAK;4EAA+B,WAAU;sFAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQ/G,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;8DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;8DAI/B,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAI,WAAU;kEACZ,cAAc,GAAG,CAAC,CAAC,yBAClB,8OAAC;gEAEC,WAAW,CAAC,uDAAuD,EACjE,iBAAiB,EAAE,KAAK,SAAS,EAAE,GAC/B,+BACA,qCACJ;gEACF,SAAS,IAAM,oBAAoB;;kFAEnC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,iIAAA,CAAA,QAAK;wFAAC,SAAS,SAAS,MAAM,KAAK,QAAQ,cAAc;kGACvD,SAAS,MAAM;;;;;;kGAElB,8OAAC;wFAAK,WAAU;kGAAe,SAAS,IAAI;;;;;;;;;;;;0FAE9C,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAW,SAAS,QAAQ;;;;;;;;;;;;kFAE7C,8OAAC;wEAAE,WAAU;kFAAiC,SAAS,WAAW;;;;;;kFAClE,8OAAC;wEAAK,WAAU;kFAA4C,SAAS,IAAI;;;;;;;+DAlBpE,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;wCA0BzB,iBAAiB,UAAU,CAAC,MAAM,GAAG,mBACpC,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;;sEACT,8OAAC,gIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,gIAAA,CAAA,kBAAe;;gEAAC;gEACe,iBAAiB,IAAI;;;;;;;;;;;;;8DAGvD,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAI,WAAU;kEACZ,iBAAiB,UAAU,CAAC,GAAG,CAAC,CAAC,sBAChC,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAS,MAAM,IAAI;wEAAE,WAAU;;4EACnC,MAAM,IAAI;4EACV,MAAM,QAAQ,kBAAI,8OAAC;gFAAK,WAAU;0FAAe;;;;;;;;;;;;oEAEnD,MAAM,OAAO,iBACZ,8OAAC,kIAAA,CAAA,SAAM;wEACL,OAAO,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE,cAAc;wEAC7C,eAAe,CAAC,QAAU,cAAc,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,CAAC,MAAM,IAAI,CAAC,EAAE;gFAAM,CAAC;;0FAEjF,8OAAC,kIAAA,CAAA,gBAAa;0FACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oFAAC,aAAa,CAAC,OAAO,EAAE,MAAM,IAAI,EAAE;;;;;;;;;;;0FAElD,8OAAC,kIAAA,CAAA,gBAAa;0FACX,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,uBAClB,8OAAC,kIAAA,CAAA,aAAU;wFAAc,OAAO;kGAC7B;uFADc;;;;;;;;;;;;;;;6FAOvB,8OAAC,iIAAA,CAAA,QAAK;wEACJ,IAAI,MAAM,IAAI;wEACd,MAAM,MAAM,IAAI,KAAK,WAAW,WAAW;wEAC3C,OAAO,UAAU,CAAC,MAAM,IAAI,CAAC,IAAI;wEACjC,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;oFACtC,GAAG,IAAI;oFACP,CAAC,MAAM,IAAI,CAAC,EAAE,MAAM,IAAI,KAAK,WAAW,OAAO,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK;gFACjF,CAAC;wEACD,aAAa,MAAM,WAAW;;;;;;kFAGlC,8OAAC;wEAAE,WAAU;kFAAsC,MAAM,WAAW;;;;;;;+DAjC5D,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;sDA0C9B,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU;4CACV,WAAU;4CACV,MAAK;;gDAEJ,wBACC,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;yEAErB,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAEjB,UAAU,iBAAiB;;;;;;;;;;;;;8CAKhC,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,2JAAA,CAAA,UAAkB;4CAAC,UAAU;4CAAU,UAAU;;;;;;sDAGlD,8OAAC,sJAAA,CAAA,UAAa;4CACZ,UAAU;4CACV,YAAY;4CACZ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAMZ,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAY,WAAU;kCACvC,cAAA,8OAAC,6JAAA,CAAA,UAAoB;;;;;;;;;;kCAGvB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAO,WAAU;kCAClC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6B;;;;;;kEAC3C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAmB;;;;;;kFACjC,8OAAC;wEAAE,WAAU;;4EAAgC;0FAChC,8OAAC;gFAAE,MAAK;gFAA+B,WAAU;0FAAgC;;;;;;4EAAwB;;;;;;;;;;;;;0EAGxH,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAmB;;;;;;kFACjC,8OAAC;wEAAK,WAAU;kFAA2C;;;;;;;;;;;;0EAI7D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAmB;;;;;;kFACjC,8OAAC;wEAAI,WAAU;kFACpC,CAAC;;;gDAG8C,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0DAMjC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6B;;;;;;kEAC3C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAmB;;;;;;kFACjC,8OAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAI/C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAmB;;;;;;kFACjC,8OAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAI/C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAmB;;;;;;kFACjC,8OAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAI/C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAmB;;;;;;kFACjC,8OAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAanE", "debugId": null}}]}