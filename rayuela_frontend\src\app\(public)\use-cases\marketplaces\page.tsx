import React from "react";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowLeft, Search, Users, Zap } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'Marketplaces - Casos de Uso | Rayuela.ai',
  description: 'Optimiza la conexión entre oferta y demanda en tu marketplace. Reduce tiempo de búsqueda 25% y aumenta la satisfacción de compradores y vendedores.',
  path: '/use-cases/marketplaces',
  keywords: ['marketplace', 'búsqueda', 'matching', 'oferta', 'demanda', 'discovery'],
});

export default function MarketplacesUseCasePage() {
  return (
    <main className="bg-white">
      {/* Breadcrumb */}
      <section className="py-6 border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Link href="/" className="hover:text-purple-600 transition-colors flex items-center gap-1">
              <ArrowLeft className="w-4 h-4" />
              Inicio
            </Link>
            <span>/</span>
            <span className="text-foreground font-medium">Marketplaces</span>
          </div>
        </div>
      </section>

      {/* Hero Section */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-purple-50 text-purple-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <span>🏪</span>{" "}
              Caso de Uso: Marketplaces
            </div>
            <h1 className="text-4xl md:text-5xl font-bold leading-tight mb-6">
              Conecta oferta y demanda de forma inteligente
            </h1>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto mb-8">
              Transforma tu marketplace en una plataforma que encuentra automáticamente lo que cada usuario busca,
              reduciendo fricción y aumentando transacciones.
            </p>
          </div>

          {/* Oportunidad de Negocio */}
          <div className="card max-w-5xl mx-auto">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-purple-700 rounded-xl flex items-center justify-center flex-shrink-0">
                <span className="text-white text-2xl">💡</span>
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-bold text-foreground mb-3">
                  Oportunidad: Marketplaces pueden reducir tiempo de búsqueda 40% y aumentar GMV hasta 25%
                </h3>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  <strong>Marketplaces de servicios</strong> con 10,000+ proveedores y 50,000+ búsquedas mensuales
                  pueden implementar Rayuela para mejorar el matching entre clientes y profesionales. Potencial en 90 días:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-green-600 mb-1">+25%</div>
                    <div className="text-sm text-muted-foreground">GMV total</div>
                  </div>
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600 mb-1">-40%</div>
                    <div className="text-sm text-muted-foreground">Tiempo búsqueda</div>
                  </div>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600 mb-1">+60%</div>
                    <div className="text-sm text-muted-foreground">Matches exitosos</div>
                  </div>
                </div>
                <blockquote className="pl-4 border-l-4 border-purple-200 italic text-muted-foreground bg-muted p-4 rounded-r-lg">
                  "Rayuela transformó cómo nuestros usuarios encuentran servicios. Ahora conectamos automáticamente 
                  clientes con los profesionales más relevantes, no solo los mejor posicionados."
                  <footer className="mt-2 text-sm not-italic">
                    — <strong className="text-foreground">Roberto Silva</strong>, CTO de ServiceHub
                  </footer>
                </blockquote>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Metrics */}
      <section className="section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid gap-6 md:grid-cols-3">
            <div className="card text-center">
              <Search className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-green-600 mb-2">-25%</div>
              <p className="text-muted-foreground">Reducción en tiempo de búsqueda</p>
            </div>
            <div className="card text-center">
              <Zap className="w-12 h-12 text-purple-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-purple-600 mb-2">+35%</div>
              <p className="text-muted-foreground">Mejora en matching accuracy</p>
            </div>
            <div className="card text-center">
              <Users className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-blue-600 mb-2">+40%</div>
              <p className="text-muted-foreground">Incremento en transacciones</p>
            </div>
          </div>
        </div>
      </section>

      {/* Problem & Solution */}
      <section className="py-14 md:py-16 bg-muted">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid gap-8 md:grid-cols-2">
            <div className="card border-red-200 bg-red-50">
              <h3 className="text-xl font-bold text-red-700 mb-4">El Problema</h3>
              <ul className="space-y-3 text-muted-foreground">
                <li>• <strong>Búsquedas ineficientes:</strong> Usuarios no encuentran lo que necesitan</li>
                <li>• <strong>Matching básico:</strong> Solo por categorías o palabras clave</li>
                <li>• <strong>Proveedores invisibles:</strong> Buenos servicios quedan ocultos</li>
                <li>• <strong>Abandono alto:</strong> Frustración por resultados irrelevantes</li>
                <li>• <strong>Competencia por ranking:</strong> Solo los primeros reciben tráfico</li>
              </ul>
            </div>

            <div className="card border-green-200 bg-green-50">
              <h3 className="text-xl font-bold text-green-700 mb-4">La Solución Rayuela</h3>
              <ul className="space-y-3 text-muted-foreground">
                <li>• <strong>Discovery inteligente:</strong> Encuentra servicios por intención, no solo keywords</li>
                <li>• <strong>Matching semántico:</strong> Conecta necesidades con capacidades reales</li>
                <li>• <strong>Visibilidad equitativa:</strong> Todos los proveedores tienen oportunidades</li>
                <li>• <strong>Experiencia fluida:</strong> Resultados relevantes desde la primera búsqueda</li>
                <li>• <strong>Optimización continua:</strong> Aprende de cada interacción exitosa</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Tipos de Matching */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Tipos de matching inteligente</h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Diferentes algoritmos para conectar oferta y demanda según el contexto
            </p>
          </div>
          
          <div className="card max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Matching por Intención */}
              <div>
                <h4 className="font-bold text-lg mb-4">Matching por Intención</h4>
                <div className="space-y-4">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="font-semibold text-blue-700 mb-2">Búsqueda: "Necesito renovar mi cocina"</div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <div>✓ Arquitectos especializados en cocinas</div>
                      <div>✓ Contratistas con portfolio de renovaciones</div>
                      <div>✓ Diseñadores de interiores con experiencia</div>
                      <div>✓ Proveedores de electrodomésticos</div>
                    </div>
                  </div>
                  
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="font-semibold text-green-700 mb-2">Resultado</div>
                    <div className="text-sm text-muted-foreground">
                      Conecta automáticamente con profesionales que han completado proyectos similares,
                      no solo los que tienen "cocina" en su descripción.
                    </div>
                  </div>
                </div>
              </div>

              {/* Matching por Contexto */}
              <div>
                <h4 className="font-bold text-lg mb-4">Matching por Contexto</h4>
                <div className="space-y-4">
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div className="font-semibold text-purple-700 mb-2">Contexto del Usuario</div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <div>• Ubicación: Madrid centro</div>
                      <div>• Presupuesto: €5,000-€15,000</div>
                      <div>• Urgencia: 2-3 semanas</div>
                      <div>• Historial: Proyectos premium</div>
                    </div>
                  </div>
                  
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div className="font-semibold text-orange-700 mb-2">Proveedores Recomendados</div>
                    <div className="text-sm text-muted-foreground">
                      Filtra automáticamente por disponibilidad, rango de precios, 
                      proximidad geográfica y calidad de trabajos anteriores.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* ROI Calculator */}
      <section className="py-14 md:py-16 section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Calculadora de ROI para Marketplaces</h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Estima el impacto de mejorar el matching en tu plataforma
            </p>
          </div>
          
          <div className="card max-w-5xl mx-auto">
            <div className="grid gap-8 md:grid-cols-2">
              <div className="space-y-4">
                <h4 className="font-bold text-lg">Ejemplo: Marketplace con 1,000 transacciones/mes</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between p-3 bg-muted rounded">
                    <span>Transacciones mensuales:</span>
                    <span className="font-mono font-semibold">1,000</span>
                  </div>
                  <div className="flex justify-between p-3 bg-muted rounded">
                    <span>Comisión promedio:</span>
                    <span className="font-mono font-semibold">8%</span>
                  </div>
                  <div className="flex justify-between p-3 bg-muted rounded">
                    <span>Ticket promedio:</span>
                    <span className="font-mono font-semibold">€250</span>
                  </div>
                  <div className="flex justify-between p-3 bg-muted rounded border-l-4 border-gray-400">
                    <span className="font-semibold">Revenue mensual actual:</span>
                    <span className="font-mono font-bold">€20,000</span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <h4 className="font-bold text-lg text-green-700">Con Rayuela (+35% transacciones):</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between p-3 bg-green-50 rounded">
                    <span>Nuevas transacciones:</span>
                    <span className="font-mono font-semibold text-green-700">1,350</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-50 rounded">
                    <span>Mejor matching = mayor ticket:</span>
                    <span className="font-mono font-semibold text-green-700">€275</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-50 rounded">
                    <span>Revenue mensual nuevo:</span>
                    <span className="font-mono font-semibold text-green-700">€29,700</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-100 rounded border-l-4 border-green-500">
                    <span className="font-semibold">Incremento mensual:</span>
                    <span className="font-mono font-bold text-green-700">+€9,700</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-200 rounded border-l-4 border-green-600">
                    <span className="font-bold">ROI anual:</span>
                    <span className="font-mono font-bold text-green-800 text-lg">+€116,400</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-8 p-6 bg-purple-50 rounded-lg border border-purple-200">
              <div className="text-center">
                <div className="text-purple-700 font-semibold mb-2">💡 Costo de Rayuela: €199/mes</div>
                <div className="text-purple-600 text-sm">
                  ROI de <strong>4,870%</strong> en el primer año • Recuperas la inversión en <strong>6 días</strong>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonios */}
      <section className="py-14 md:py-16 bg-muted">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Lo que dicen nuestros clientes de marketplaces</h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Testimonios reales de plataformas que ya están usando Rayuela
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div className="card hover:shadow-lg transition-all duration-300">
              <blockquote className="text-muted-foreground mb-4 leading-relaxed italic">
                "Nuestros usuarios ahora encuentran exactamente lo que buscan. El matching inteligente redujo las búsquedas fallidas 60%."
              </blockquote>
              <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xl">👨‍💼</span>
                </div>
                <div>
                  <div className="font-bold text-foreground">Roberto Silva</div>
                  <div className="text-sm text-muted-foreground">CTO</div>
                  <div className="text-xs text-muted-foreground">ServiceHub</div>
                </div>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg px-3 py-2">
                <span className="text-xs font-semibold text-green-700">+25% GMV, -40% tiempo búsqueda</span>
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <blockquote className="text-muted-foreground mb-4 leading-relaxed italic">
                "Los proveedores pequeños ahora tienen las mismas oportunidades. El algoritmo es justo y efectivo."
              </blockquote>
              <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xl">👩‍💻</span>
                </div>
                <div>
                  <div className="font-bold text-foreground">Carmen López</div>
                  <div className="text-sm text-muted-foreground">Head of Product</div>
                  <div className="text-xs text-muted-foreground">TalentConnect</div>
                </div>
              </div>
              <div className="bg-blue-50 border border-blue-200 rounded-lg px-3 py-2">
                <span className="text-xs font-semibold text-blue-700">+45% proveedores activos</span>
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <blockquote className="text-muted-foreground mb-4 leading-relaxed italic">
                "La satisfacción de nuestros usuarios subió 35%. Encuentran servicios que realmente necesitan."
              </blockquote>
              <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xl">👨‍🚀</span>
                </div>
                <div>
                  <div className="font-bold text-foreground">Miguel Torres</div>
                  <div className="text-sm text-muted-foreground">CEO</div>
                  <div className="text-xs text-muted-foreground">LocalMarket</div>
                </div>
              </div>
              <div className="bg-orange-50 border border-orange-200 rounded-lg px-3 py-2">
                <span className="text-xs font-semibold text-orange-700">+35% satisfacción, +28% retention</span>
              </div>
            </div>
          </div>

          {/* Tipos de Marketplace */}
          <div className="text-center">
            <p className="text-sm font-semibold text-purple-600 mb-6">FUNCIONA EN CUALQUIER TIPO DE MARKETPLACE</p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 items-center justify-items-center opacity-60 hover:opacity-80 transition-opacity">
              <div className="flex flex-col items-center justify-center p-3 bg-white border border-border rounded-lg hover:shadow-md transition-all group">
                <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">🔧</span>
                <span className="text-xs font-medium text-muted-foreground text-center">Servicios</span>
              </div>
              <div className="flex flex-col items-center justify-center p-3 bg-white border border-border rounded-lg hover:shadow-md transition-all group">
                <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">💼</span>
                <span className="text-xs font-medium text-muted-foreground text-center">Freelancers</span>
              </div>
              <div className="flex flex-col items-center justify-center p-3 bg-white border border-border rounded-lg hover:shadow-md transition-all group">
                <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">🏠</span>
                <span className="text-xs font-medium text-muted-foreground text-center">Inmobiliario</span>
              </div>
              <div className="flex flex-col items-center justify-center p-3 bg-white border border-border rounded-lg hover:shadow-md transition-all group">
                <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">🚗</span>
                <span className="text-xs font-medium text-muted-foreground text-center">Vehículos</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Integración Técnica */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Integración técnica para marketplaces</h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Implementación específica para plataformas de matching
            </p>
          </div>

          <div className="card max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Flujo de datos */}
              <div>
                <h4 className="font-bold text-lg mb-4">Flujo de Datos del Marketplace</h4>
                <div className="bg-muted rounded-xl p-6 mb-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="bg-blue-100 rounded-lg p-3 text-center flex-1 mr-2">
                        <div className="text-sm font-semibold">Proveedores</div>
                        <div className="text-xs text-muted-foreground">Servicios + Capacidades</div>
                      </div>
                      <div className="text-purple-600 font-bold">→</div>
                      <div className="bg-purple-100 rounded-lg p-3 text-center flex-1 ml-2">
                        <div className="text-sm font-semibold">Rayuela</div>
                        <div className="text-xs text-muted-foreground">Matching Engine</div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="bg-green-100 rounded-lg p-3 text-center flex-1 mr-2">
                        <div className="text-sm font-semibold">Compradores</div>
                        <div className="text-xs text-muted-foreground">Búsquedas + Intenciones</div>
                      </div>
                      <div className="text-purple-600 font-bold">→</div>
                      <div className="bg-orange-100 rounded-lg p-3 text-center flex-1 ml-2">
                        <div className="text-sm font-semibold">Matches</div>
                        <div className="text-xs text-muted-foreground">Recomendaciones</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <span className="w-5 h-5 text-green-600">✓</span>
                    <span className="text-sm">Matching semántico avanzado</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="w-5 h-5 text-green-600">✓</span>
                    <span className="text-sm">Filtros contextuales automáticos</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="w-5 h-5 text-green-600">✓</span>
                    <span className="text-sm">Ranking dinámico por relevancia</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="w-5 h-5 text-green-600">✓</span>
                    <span className="text-sm">Analytics de matching en tiempo real</span>
                  </div>
                </div>
              </div>

              {/* Ejemplo API */}
              <div>
                <h4 className="font-bold text-lg mb-4">Ejemplo API para Búsqueda</h4>
                <div className="space-y-4">
                  <div>
                    <h5 className="text-sm font-semibold mb-2 text-muted-foreground">REQUEST</h5>
                    <div className="bg-gray-900 rounded-lg p-4 text-sm font-mono text-gray-100 overflow-x-auto">
                      <pre>{`POST /api/v1/marketplace/search
{
  "query": "necesito renovar mi cocina",
  "location": {
    "city": "Madrid",
    "radius": 25
  },
  "budget": {
    "min": 5000,
    "max": 15000
  },
  "urgency": "2-3 weeks",
  "preferences": {
    "rating_min": 4.0,
    "verified_only": true
  }
}`}</pre>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-semibold mb-2 text-muted-foreground">RESPONSE</h5>
                    <div className="bg-gray-900 rounded-lg p-4 text-sm font-mono text-gray-100 overflow-x-auto">
                      <pre>{`{
  "matches": [
    {
      "providerId": "prov_123",
      "score": 0.94,
      "matchReasons": [
        "kitchen_renovation_specialist",
        "budget_compatible",
        "available_timeframe"
      ],
      "estimatedPrice": 12500
    }
  ],
  "totalResults": 47,
  "searchTime": "32ms"
}`}</pre>
                    </div>
                  </div>
                </div>

                <div className="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-green-600">⚡</span>
                    <span className="font-bold text-green-700">Implementación marketplace</span>
                  </div>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <div>• <strong>Setup inicial:</strong> 3-5 horas</div>
                    <div>• <strong>Integración búsqueda:</strong> 6-8 horas</div>
                    <div>• <strong>Testing y optimización:</strong> 4-6 horas</div>
                    <div className="pt-2 border-t border-green-200">
                      <strong className="text-green-700">Total: 2-3 días de desarrollo</strong>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Final */}
      <section className="py-14 md:py-16 section-gradient-strong">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold mb-4">
            ¿Listo para optimizar tu marketplace como ServiceHub?
          </h2>
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
            Únete a decenas de marketplaces que ya están mejorando su matching 
            y aumentando transacciones con Rayuela.
          </p>
          <div className="flex flex-wrap gap-4 justify-center">
            <Link href="/register?utm_source=marketplaces-final" className="btn-primary">
              Empezar gratis ahora
            </Link>
            <Link href="/contact-sales?utm_source=marketplaces-final&industry=marketplace" className="btn-secondary">
              Hablar con experto en marketplaces
            </Link>
          </div>
          <p className="text-sm text-muted-foreground mt-6">
            Sin compromiso • Integración en 48 horas • Matching mejorado en 7 días
          </p>
        </div>
      </section>
    </main>
  );
}
