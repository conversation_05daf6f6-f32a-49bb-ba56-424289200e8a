"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Progress } from "@/components/ui/progress";
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Target, 
  Users, 
  BarChart3,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  ArrowUp,
  ArrowDown
} from "lucide-react";

interface BusinessMetrics {
  ctr_lift: number;
  cvr_lift: number;
  revenue_attribution: number;
  catalog_coverage: number;
  engagement_score: number;
  raw_metrics: {
    ctr: number;
    cvr: number;
    baseline_ctr: number;
    baseline_cvr: number;
    total_recommendations: number;
    total_conversions: number;
    avg_order_value: number;
  };
  performance_indicators: {
    ctr_status: string;
    cvr_status: string;
    coverage_status: string;
    engagement_status: string;
  };
  recommendations: {
    next_steps: string[];
    estimated_monthly_impact: string;
  };
  last_updated?: string;
  error?: string;
}

interface BusinessMetricsDashboardProps {
  apiKey: string;
}

export default function BusinessMetricsDashboard({ apiKey }: BusinessMetricsDashboardProps) {
  const [metrics, setMetrics] = useState<BusinessMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/v1/analytics/business-metrics', {
        headers: {
          'X-API-Key': apiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setMetrics(data);
      setLastRefresh(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch metrics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (apiKey) {
      fetchMetrics();
      // Refresh every 5 minutes
      const interval = setInterval(fetchMetrics, 5 * 60 * 1000);
      return () => clearInterval(interval);
    }
  }, [apiKey]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600 bg-green-50';
      case 'good': return 'text-blue-600 bg-blue-50';
      case 'improving': return 'text-yellow-600 bg-yellow-50';
      default: return 'text-muted-foreground bg-muted';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent': return <CheckCircle className="h-4 w-4" />;
      case 'good': return <TrendingUp className="h-4 w-4" />;
      case 'improving': return <ArrowUp className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  if (loading && !metrics) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Business Impact</h2>
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-4 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error && !metrics) {
    return (
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            Error Loading Metrics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchMetrics} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Business Impact</h2>
          <p className="text-muted-foreground">
            Real-time metrics showing the value of your recommendations
          </p>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">
            Last updated: {lastRefresh.toLocaleTimeString()}
          </span>
          <Button 
            onClick={fetchMetrics} 
            variant="outline" 
            size="sm"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* CTR Lift */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Target className="h-4 w-4 text-blue-500" />
              CTR Lift
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-2xl font-bold">
                {formatPercentage(metrics?.ctr_lift || 0)}
              </span>
              <Badge className={getStatusColor(metrics?.performance_indicators.ctr_status || 'baseline')}>
                {getStatusIcon(metrics?.performance_indicators.ctr_status || 'baseline')}
                {metrics?.performance_indicators.ctr_status || 'baseline'}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              vs {formatPercentage(metrics?.raw_metrics.baseline_ctr || 0.02)} baseline
            </p>
          </CardContent>
        </Card>

        {/* CVR Lift */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-green-500" />
              CVR Lift
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-2xl font-bold">
                {formatPercentage(metrics?.cvr_lift || 0)}
              </span>
              <Badge className={getStatusColor(metrics?.performance_indicators.cvr_status || 'baseline')}>
                {getStatusIcon(metrics?.performance_indicators.cvr_status || 'baseline')}
                {metrics?.performance_indicators.cvr_status || 'baseline'}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              vs {formatPercentage(metrics?.raw_metrics.baseline_cvr || 0.01)} baseline
            </p>
          </CardContent>
        </Card>

        {/* Revenue Attribution */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-green-500" />
              Revenue Attribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-2">
              <span className="text-2xl font-bold">
                {formatCurrency(metrics?.revenue_attribution || 0)}
              </span>
            </div>
            <p className="text-sm text-muted-foreground">
              {metrics?.recommendations.estimated_monthly_impact || 'No data yet'}
            </p>
          </CardContent>
        </Card>

        {/* Engagement Score */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users className="h-4 w-4 text-purple-500" />
              Engagement Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-2xl font-bold">
                {((metrics?.engagement_score || 0) * 100).toFixed(0)}
              </span>
              <Badge className={getStatusColor(metrics?.performance_indicators.engagement_status || 'low')}>
                {getStatusIcon(metrics?.performance_indicators.engagement_status || 'low')}
                {metrics?.performance_indicators.engagement_status || 'low'}
              </Badge>
            </div>
            <Progress 
              value={(metrics?.engagement_score || 0) * 100} 
              className="h-2"
            />
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Performance Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm">Click-through Rate</span>
              <span className="font-medium">{formatPercentage(metrics?.raw_metrics.ctr || 0)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Conversion Rate</span>
              <span className="font-medium">{formatPercentage(metrics?.raw_metrics.cvr || 0)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Catalog Coverage</span>
              <span className="font-medium">{formatPercentage(metrics?.catalog_coverage || 0)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Total Recommendations</span>
              <span className="font-medium">{(metrics?.raw_metrics.total_recommendations || 0).toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Total Conversions</span>
              <span className="font-medium">{(metrics?.raw_metrics.total_conversions || 0).toLocaleString()}</span>
            </div>
          </CardContent>
        </Card>

        {/* Recommendations */}
        <Card>
          <CardHeader>
            <CardTitle>Next Steps</CardTitle>
            <CardDescription>
              Actionable recommendations to improve performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3">
              {(metrics?.recommendations.next_steps || []).map((step, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">
                    {index + 1}
                  </span>
                  <span className="text-sm">{step}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Error Display */}
      {metrics?.error && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-yellow-800">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">
                Some metrics may be incomplete: {metrics.error}
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
