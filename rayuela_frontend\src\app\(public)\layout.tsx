import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import <PERSON><PERSON><PERSON><PERSON>ogo from '@/components/ui/RayuelaLogo';
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';

export const metadata = generateSEOMetadata({
  title: 'Rayuela.ai – Personalización Inteligente para E-commerce',
  description:
    'Convierte visitantes en compradores con recomendaciones basadas en IA. Rayuela.ai es la API que potencia a los líderes del e-commerce en LATAM.',
  path: '/',
  keywords: [
    'recomendaciones personalizadas',
    'inteligencia artificial e-commerce',
    'API personalización',
    'machine learning ventas',
    'Rayuela.ai',
  ],
});

export default function PublicLayout({ children }: { readonly children: React.ReactNode }) {
  return (
    <div>
      <header className="sticky top-0 z-50 bg-background/80 backdrop-blur-md border-b border-border/50 shadow-soft">
        <nav className="container mx-auto flex items-center justify-between py-4 px-4">
          <RayuelaLogo size="md" href="/" showText={true} widthPx={28} heightPx={9} textClassName="text-[1.05rem] md:text-[1.1rem]" />

          <div className="flex items-center gap-4">
            {/* Navigation links */}
            <div className="hidden md:flex items-center gap-6 text-sm">
              <Link href="/pricing" className="text-muted-foreground hover:text-primary transition-colors font-medium">
                Precios
              </Link>
              <Link href="/docs" className="text-muted-foreground hover:text-primary transition-colors font-medium">
                Docs
              </Link>
              <Link href="/login" className="text-muted-foreground hover:text-primary transition-colors font-medium">
                Iniciar sesión
              </Link>
            </div>

            {/* CTA Button */}
            <Button asChild size="sm" className="bg-primary hover:bg-primary/90 text-white shadow-glow-primary font-semibold">
              <Link href="/register?utm_campaign=nav-cta" aria-label="Probar Rayuela gratis">
                Probar Gratis
              </Link>
            </Button>
          </div>
        </nav>
      </header>
      <main>{children}</main>
    </div>
  );
}
