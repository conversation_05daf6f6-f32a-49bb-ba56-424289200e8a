// Ruta: src/app/(dashboard)/page.tsx
"use client";

import { useState, useEffect } from 'react';
import { useAuth, useAccountInfo, useUsageSummary } from '@/lib/hooks';
import { getMyAccount } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { KeyIcon, BookOpenIcon, XIcon, BarChart3Icon } from 'lucide-react';
import Link from 'next/link';
import RayuelaLogo from '@/components/ui/RayuelaLogo';

import { ApiStatus } from '@/components/dashboard/ApiStatus';
// PostModalHighlight eliminado - funcionalidad integrada en GettingStartedChecklist
import GettingStartedChecklist from '@/components/dashboard/GettingStartedChecklist';
import { SandboxResetButton } from '@/components/dashboard/SandboxResetButton';
import DashboardWidgets from '@/components/dashboard/DashboardWidgets';
import BusinessMetricsDashboard from '@/components/dashboard/BusinessMetricsDashboard';



export default function DashboardPage() {
  const { token, apiKey } = useAuth();
  const [isNewUser, setIsNewUser] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [dismissedOnboarding, setDismissedOnboarding] = useState(false);

  useEffect(() => {
    // Verificar si es un nuevo usuario basado en la fecha de creación de la API Key
    const checkIfNewUser = async () => {
      if (!token || !apiKey) {
        setIsLoading(false);
        return;
      }

      try {
        const accountData = await getMyAccount();

        // Verificar si la cuenta es reciente (menos de 24 horas)
        const accountCreatedAt = new Date(accountData.createdAt);
        const now = new Date();
        const hoursSinceCreation = (now.getTime() - accountCreatedAt.getTime()) / (1000 * 60 * 60);

        setIsNewUser(hoursSinceCreation < 24);

        // PostModalHighlight eliminado - funcionalidad integrada en GettingStartedChecklist
      } catch (error) {
        console.error("Error al obtener datos de la cuenta:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkIfNewUser();

    // Verificar si el usuario ya ha descartado el onboarding
    const hasUserDismissedOnboarding = localStorage.getItem('dismissedOnboarding');
    if (hasUserDismissedOnboarding === 'true') {
      setDismissedOnboarding(true);
    }
  }, [token, apiKey]);

  const handleDismissOnboarding = () => {
    setDismissedOnboarding(true);
    localStorage.setItem('dismissedOnboarding', 'true');
  };

  // PostModalHighlight eliminado - funcionalidad integrada en GettingStartedChecklist

  // Obtener datos de la cuenta usando el hook personalizado
  const {
    accountData,
    error: accountError,
    isLoading: isAccountLoading
  } = useAccountInfo();

  // Obtener datos de uso usando el hook personalizado
  const {
    data: usageData,
    error: usageError,
    isLoading: isUsageLoading
  } = useUsageSummary();

  // Estado de carga combinado
  const isDataLoading = isAccountLoading || isUsageLoading;
  return (
    <div className="rayuela-fade-in">
      {/* PostModalHighlight eliminado - funcionalidad integrada en GettingStartedChecklist */}

      {/* Checklist de Primeros Pasos */}
      <div className="rayuela-scale-in rayuela-stagger-1">
        <GettingStartedChecklist />
      </div>

      {/* Sandbox Tools para usuarios FREE */}
      <div className="rayuela-scale-in rayuela-stagger-2">
        <SandboxResetButton />
      </div>

      {/* Banner de Onboarding para nuevos usuarios */}
      {isNewUser && !dismissedOnboarding && !isLoading && (
        <div className="rayuela-slide-up rayuela-stagger-2">
          <Alert className="mb-6" variant="info">
          <div className="flex justify-between items-start">
            <div>
              <AlertTitle className="flex items-center">
                ¡Bienvenido a Rayuela!
              </AlertTitle>
              <AlertDescription className="mt-3">
                Para comenzar a utilizar nuestra API, te recomendamos seguir estos pasos:
                <ol className="list-none mt-4 space-y-5">
                  <li className="flex items-start gap-3">
                    <span className="flex items-center justify-center bg-primary text-primary-foreground rounded-full w-6 h-6 flex-shrink-0 font-bold text-sm mt-0.5">1</span>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 min-w-0">
                      <Link href="/api-keys" className="text-primary hover:text-primary/80 font-medium underline underline-offset-2 flex-shrink-0">
                        Gestiona tus API Keys
                      </Link>
                      <span className="text-muted-foreground text-sm sm:text-base">para generar o ver tus claves de acceso</span>
                    </div>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="flex items-center justify-center bg-primary text-primary-foreground rounded-full w-6 h-6 flex-shrink-0 font-bold text-sm mt-0.5">2</span>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 min-w-0">
                      <a
                        href="https://docs.rayuela.ai/quickstart"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:text-primary/80 font-medium underline underline-offset-2 flex-shrink-0"
                      >
                        Consulta nuestra guía de inicio rápido
                      </a>
                      <span className="text-muted-foreground text-sm sm:text-base">para aprender a integrar la API</span>
                    </div>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="flex items-center justify-center bg-primary text-primary-foreground rounded-full w-6 h-6 flex-shrink-0 font-bold text-sm mt-0.5">3</span>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 min-w-0">
                      <Link href="/usage" className="text-primary hover:text-primary/80 font-medium underline underline-offset-2 flex-shrink-0">
                        Monitorea tu uso
                      </Link>
                      <span className="text-muted-foreground text-sm sm:text-base">para ver estadísticas y límites de tu cuenta</span>
                    </div>
                  </li>
                </ol>
              </AlertDescription>
              <div className="mt-6 flex flex-wrap gap-3">
                <Button asChild size="sm">
                  <Link href="/api-keys" className="flex items-center">
                    <KeyIcon className="mr-2 h-4 w-4" />
                    Gestionar API Keys
                  </Link>
                </Button>
                <Button asChild variant="outline" size="sm">
                  <a
                    href="https://docs.rayuela.ai/quickstart"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center"
                  >
                    <BookOpenIcon className="mr-2 h-4 w-4" />
                    Ver Guía de Inicio
                  </a>
                </Button>
                <Button asChild variant="outline" size="sm">
                  <Link href="/usage" className="flex items-center">
                    <BarChart3Icon className="mr-2 h-4 w-4" />
                    Ver Uso
                  </Link>
                </Button>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="text-muted-foreground hover:bg-muted hover:text-foreground"
              onClick={handleDismissOnboarding}
            >
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
        </Alert>
        </div>
      )}

      <div className="flex justify-between items-start mb-8">
        <div>
          <div className="flex items-center gap-4 mb-3">
            <RayuelaLogo size="sm" showText={true} href={undefined} widthPx={110} heightPx={36} />
            <h1 className="text-display-md font-bold text-foreground">
              Dashboard
            </h1>
          </div>
          <p className="text-body-lg text-muted-foreground max-w-2xl leading-relaxed">
            <strong className="text-foreground">Bienvenido a tu centro de control.</strong> Aquí encontrarás una vista completa de tu uso de API, información de facturación y gestión de claves.
          </p>
        </div>
        <ApiStatus className="hidden lg:flex" />
      </div>

      {/* Widgets del Dashboard */}
      <DashboardWidgets
        usageData={usageData}
        usageError={usageError}
        accountData={accountData}
        accountError={accountError}
        apiKey={apiKey}
        isDataLoading={isDataLoading}
      />

      {/* Business Metrics Dashboard */}
      {apiKey && (
        <div className="mt-8">
          <BusinessMetricsDashboard apiKey={apiKey} />
        </div>
      )}

      {/* Dashboard Principal */}
      {/* TODO: Import UsageDashboard component */}
      {/* <div className="rayuela-fade-in rayuela-stagger-3">
        <UsageDashboard />
      </div> */}
    </div>
  );
}
