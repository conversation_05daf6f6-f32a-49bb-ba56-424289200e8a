import { HelpCircle } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface TooltipHelperProps {
  content: string;
  className?: string;
}

export function TooltipHelper({ content, className = "" }: TooltipHelperProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <HelpCircle className={`h-4 w-4 text-muted-foreground hover:text-foreground cursor-help ${className}`} />
        </TooltipTrigger>
        <TooltipContent>
          <p className="max-w-xs">{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

/**
 * Componente para mostrar una descripción detallada de una métrica
 * @param title Título de la métrica
 * @param description Descripción de la métrica
 * @param formula Fórmula de cálculo (opcional)
 * @param example Ejemplo de interpretación (opcional)
 * @param range Rango de valores (opcional)
 */
export function MetricDescription({
  title,
  description,
  formula,
  example,
  range,
}: {
  title: string;
  description: string;
  formula?: string;
  example?: string;
  range?: string;
}) {
  return (
    <div className="space-y-2">
      <h4 className="font-semibold text-foreground dark:text-gray-100">{title}</h4>
      <p className="text-foreground">{description}</p>
      {formula && (
        <div className="mt-1">
          <span className="font-medium text-foreground dark:text-gray-200">Fórmula: </span>
          <span className="text-foreground font-mono text-xs">{formula}</span>
        </div>
      )}
      {range && (
        <div className="mt-1">
          <span className="font-medium text-foreground dark:text-gray-200">Rango: </span>
          <span className="text-foreground">{range}</span>
        </div>
      )}
      {example && (
        <div className="mt-1">
          <span className="font-medium text-foreground dark:text-gray-200">Ejemplo: </span>
          <span className="text-foreground">{example}</span>
        </div>
      )}
    </div>
  );
}

// Definiciones de métricas para reutilizar en toda la aplicación
export const metricDefinitions = {
  precision: {
    title: "Precisión",
    description: "Mide la proporción de recomendaciones relevantes entre todas las recomendaciones realizadas.",
    formula: "Precisión = (# recomendaciones relevantes) / (# total de recomendaciones)",
    range: "0-100%, donde valores más altos son mejores",
    example: "Una precisión del 80% significa que 8 de cada 10 recomendaciones fueron relevantes para los usuarios."
  },
  recall: {
    title: "Recall",
    description: "Mide la proporción de ítems relevantes que fueron efectivamente recomendados.",
    formula: "Recall = (# ítems relevantes recomendados) / (# total de ítems relevantes)",
    range: "0-100%, donde valores más altos son mejores",
    example: "Un recall del 70% significa que el sistema recomendó el 70% de todos los ítems que podrían ser relevantes para el usuario."
  },
  ndcg: {
    title: "NDCG (Normalized Discounted Cumulative Gain)",
    description: "Mide la calidad del ranking de recomendaciones, dando más peso a los aciertos en las primeras posiciones.",
    formula: "NDCG = DCG / IDCG, donde DCG suma las relevancias con descuento por posición",
    range: "0-100%, donde valores más altos son mejores",
    example: "Un NDCG del 85% indica que el orden de las recomendaciones es casi óptimo, con los ítems más relevantes al principio."
  },
  map: {
    title: "MAP (Mean Average Precision)",
    description: "Promedio de la precisión calculada en cada punto de recall, evaluando la calidad del ranking.",
    formula: "MAP = promedio de precisiones en cada posición relevante",
    range: "0-100%, donde valores más altos son mejores",
    example: "Un MAP del 75% indica un buen balance entre precisión y recall a lo largo de toda la lista de recomendaciones."
  },
  catalog_coverage: {
    title: "Cobertura del Catálogo",
    description: "Porcentaje del catálogo total que el sistema es capaz de recomendar.",
    formula: "Cobertura = (# ítems únicos recomendados) / (# total de ítems en el catálogo)",
    range: "0-100%, donde valores más altos indican mayor diversidad",
    example: "Una cobertura del 60% significa que el sistema recomienda el 60% de los productos disponibles."
  },
  diversity: {
    title: "Diversidad",
    description: "Mide qué tan variadas son las recomendaciones en términos de categorías, atributos o características.",
    formula: "Basada en la distancia promedio entre ítems recomendados",
    range: "0-100%, donde valores más altos indican mayor variedad",
    example: "Una diversidad del 80% indica que las recomendaciones abarcan una amplia variedad de categorías y tipos de productos."
  },
  novelty: {
    title: "Novedad",
    description: "Mide qué tan poco comunes o populares son los ítems recomendados.",
    formula: "Basada en la popularidad inversa de los ítems recomendados",
    range: "0-100%, donde valores más altos indican ítems menos populares",
    example: "Una novedad del 70% indica que el sistema recomienda ítems relativamente poco conocidos o populares."
  },
  serendipity: {
    title: "Serendipia",
    description: "Mide la capacidad del sistema para recomendar ítems relevantes pero inesperados o sorprendentes.",
    formula: "Combinación de relevancia y distancia a las preferencias explícitas del usuario",
    range: "0-100%, donde valores más altos indican mayor sorpresa positiva",
    example: "Una serendipia del 65% indica que el sistema recomienda ítems que el usuario probablemente no habría descubierto por sí mismo."
  },
  confidence: {
    title: "Confianza",
    description: "Nivel de certeza del sistema sobre la relevancia de una recomendación para un usuario específico.",
    formula: "Combinación de múltiples factores como similitud, historial de usuario, popularidad del ítem, etc.",
    range: "0-100%, donde valores más altos indican mayor certeza",
    example: "Una confianza del 90% indica que el sistema está muy seguro de que la recomendación será relevante para el usuario."
  },
  user_history_size: {
    title: "Tamaño del Historial de Usuario",
    description: "Impacto del número de interacciones previas del usuario en la confianza de las recomendaciones.",
    example: "Un valor alto indica que el historial extenso del usuario contribuye significativamente a la confianza en las recomendaciones."
  },
  item_popularity: {
    title: "Popularidad del Ítem",
    description: "Impacto de la popularidad general del ítem en la confianza de las recomendaciones.",
    example: "Un valor alto indica que la popularidad del ítem contribuye significativamente a la confianza en las recomendaciones."
  },
  category_strength: {
    title: "Fuerza de Categoría",
    description: "Impacto de la afinidad del usuario por la categoría del ítem en la confianza de las recomendaciones.",
    example: "Un valor alto indica que la afinidad del usuario por la categoría contribuye significativamente a la confianza."
  },
  model_type: {
    title: "Tipo de Modelo",
    description: "Impacto del tipo de modelo utilizado (colaborativo, contenido, híbrido) en la confianza de las recomendaciones.",
    example: "Un valor alto indica que el tipo de modelo utilizado contribuye significativamente a la confianza."
  }
};
