from pydantic import BaseModel, Field, EmailStr
from typing import Optional
from .base import CamelCaseModel


class RegisterRequest(CamelCaseModel):
    """Schema para el registro completo de una cuenta con usuario administrador."""

    account_name: str = Field(..., min_length=1, description="Nombre de la cuenta")
    email: EmailStr = Field(..., description="Email del usuario administrador")
    password: str = Field(
        ..., min_length=8, description="Contraseña del usuario administrador"
    )


class LoginRequest(CamelCaseModel):
    """Schema para el login de usuarios con JSON payload."""

    email: EmailStr = Field(..., description="Email del usuario")
    password: str = Field(..., min_length=1, description="Contraseña del usuario")


class Token(CamelCaseModel):
    access_token: str
    token_type: str


class TokenPayload(CamelCaseModel):
    sub: Optional[int] = None


class TokenData(CamelCaseModel):
    sub: str
    account_id: int
    is_admin: bool = False


# DEPRECATED SCHEMAS - These schemas are no longer used and will be removed in a future version

class FirstApiKeyRequest(BaseModel):
    """DEPRECATED: This schema is no longer used. Use the /api/v1/api-keys/ endpoint instead."""
    email: str = Field(
        ..., description="Email de la cuenta para la que se generará la primera API Key"
    )


class FirstApiKeyResponse(BaseModel):
    """DEPRECATED: This schema is no longer used. Use NewApiKeyResponse from api_key.py instead."""
    api_key: str = Field(..., description="La API Key generada")
    message: str = Field(
        ..., description="Mensaje informativo sobre el uso de la API Key"
    )


class LoginResponse(CamelCaseModel):
    """Login response schema"""
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field(..., description="Token type (bearer)")
    account_id: int = Field(..., description="Account ID")
    is_admin: bool = Field(..., description="Whether user is admin")


class RegisterResponse(CamelCaseModel):
    """Registration response schema"""
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field(..., description="Token type (bearer)")
    account_id: int = Field(..., description="Account ID")
    user_id: int = Field(..., description="User ID")
    is_admin: bool = Field(..., description="Whether user is admin")
    api_key: str = Field(..., description="Initial API key for the user")
    message: str = Field(..., description="Success message")


class TokenResponse(BaseModel):
    """Schema para respuestas de token con información adicional (DEPRECATED - usar LoginResponse o RegisterResponse)."""
    access_token: str = Field(..., description="Token JWT de acceso")
    token_type: str = Field(..., description="Tipo de token (siempre 'bearer')")
    account_id: int = Field(..., description="ID de la cuenta")
    is_admin: bool = Field(..., description="Indica si el usuario es administrador")
    api_key: str | None = Field(
        None,
        description="API Key generada (solo se incluye en el registro o primera generación)",
    )
    message: str | None = Field(
        None, description="Mensaje informativo sobre la API Key"
    )
