// src/components/ui/RayuelaLogo.tsx
"use client";

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

interface RayuelaLogoProps {
  variant?: 'default' | 'white' | 'dark';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showText?: boolean;
  href?: string;
  className?: string;
  priority?: boolean;
  widthPx?: number;
  heightPx?: number;
  textClassName?: string;
}

const RayuelaLogo: React.FC<RayuelaLogoProps> = ({
  variant = 'default',
  size = 'md',
  showText = false,
  href, // no default to avoid nested <a> when parent wraps it
  className = '',
  priority = false,
  widthPx,
  heightPx,
  textClassName,
}) => {
  // Tamaños del logo (manteniendo aspect ratio de la imagen original)
  const logoSizes = {
    sm: { width: 100, height: 32, text: 'text-lg' },
    md: { width: 125, height: 40, text: 'text-xl' },
    lg: { width: 150, height: 48, text: 'text-2xl' },
    xl: { width: 200, height: 64, text: 'text-3xl' }
  };

  // Colores según variante para el texto adicional (si se usa)
  const colorClasses = {
    default: {
      text: 'text-foreground',
      accent: 'text-accent'
    },
    white: {
      text: 'text-white',
      accent: 'text-white/80'
    },
    dark: {
      text: 'text-foreground',
      accent: 'text-foreground'
    }
  };

  const { width, height, text: textSize } = logoSizes[size];
  const finalWidth = widthPx ?? width;
  const finalHeight = heightPx ?? height;
  const colors = colorClasses[variant];

  const logoContent = (
    <div className={`flex items-center ${className}`}>
      <Image
        src="/logo-rayuela.png"
        alt="Rayuela.ai"
        width={finalWidth}
        height={finalHeight}
        priority={priority}
        className="object-contain"
      />
      {showText && (
        <div className={`font-bold ${textClassName ?? textSize} ${colors.text} tracking-tight ml-3`}>
          Rayuela<span className={colors.accent}>.ai</span>
        </div>
      )}
    </div>
  );

  if (href) {
    return (
      <Link
        href={href}
        className="group transition-transform duration-200 hover:scale-105 inline-flex"
      >
        {logoContent}
      </Link>
    );
  }

  return logoContent;
};

export default RayuelaLogo;
