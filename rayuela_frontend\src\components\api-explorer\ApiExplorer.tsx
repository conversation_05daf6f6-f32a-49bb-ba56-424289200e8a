"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Play,
  Settings,
  RefreshCw,
  Zap
} from "lucide-react";
import CodeGenerator from "./CodeGenerator";
import ResponseVisualizer from "./ResponseVisualizer";
import IntegrationTemplates from "./IntegrationTemplates";

interface ApiEndpoint {
  id: string;
  name: string;
  method: string;
  path: string;
  description: string;
  category: string;
  parameters: {
    name: string;
    type: string;
    required: boolean;
    description: string;
    default?: any;
    options?: string[];
  }[];
}

interface ApiResponse {
  status: number;
  data: any;
  headers: Record<string, string>;
  responseTime: number;
}

const API_ENDPOINTS: ApiEndpoint[] = [
  {
    id: "ecommerce-homepage",
    name: "E-commerce Homepage",
    method: "POST",
    path: "/api/v1/recommendations/ecommerce/homepage",
    description: "Get optimized recommendations for e-commerce homepage",
    category: "E-commerce",
    parameters: [
      { name: "external_user_id", type: "string", required: true, description: "User identifier", default: "demo-user-001" },
      { name: "limit", type: "number", required: false, description: "Number of recommendations", default: 10, options: ["5", "10", "15", "20"] },
      { name: "category", type: "string", required: false, description: "Filter by category", options: ["electronics", "books", "clothing", "home"] },
      { name: "min_rating", type: "number", required: false, description: "Minimum product rating", options: ["3.0", "4.0", "4.5"] }
    ]
  },
  {
    id: "quick-start-demo",
    name: "Quick Start Demo",
    method: "GET",
    path: "/api/v1/sandbox/quick-start/demo",
    description: "Get instant demo recommendations with sample data",
    category: "Quick Start",
    parameters: [
      { name: "user_id", type: "string", required: false, description: "Demo user ID", default: "demo-user-001" },
      { name: "template", type: "string", required: false, description: "Template type", default: "ecommerce", options: ["ecommerce", "content", "marketplace"] }
    ]
  },
  {
    id: "ab-test-recommendations",
    name: "A/B Test Recommendations",
    method: "POST",
    path: "/api/v1/recommendations/ab-test/recommendations",
    description: "Get recommendations with automatic A/B testing vs baseline",
    category: "A/B Testing",
    parameters: [
      { name: "external_user_id", type: "string", required: true, description: "User identifier", default: "test-user-001" },
      { name: "limit", type: "number", required: false, description: "Number of recommendations", default: 10 },
      { name: "experiment_id", type: "string", required: false, description: "Experiment ID (optional)" }
    ]
  },
  {
    id: "business-metrics",
    name: "Business Metrics",
    method: "GET",
    path: "/api/v1/analytics/business-metrics",
    description: "Get real-time business impact metrics",
    category: "Analytics",
    parameters: []
  }
];

export default function ApiExplorer() {
  const [selectedEndpoint, setSelectedEndpoint] = useState<ApiEndpoint>(API_ENDPOINTS[0]);
  const [parameters, setParameters] = useState<Record<string, any>>({});
  const [apiKey, setApiKey] = useState("ray_demo_key_for_testing");
  const [response, setResponse] = useState<ApiResponse | null>(null);
  const [loading, setLoading] = useState(false);
  // Initialize parameters with defaults
  useEffect(() => {
    const defaultParams: Record<string, any> = {};
    selectedEndpoint.parameters.forEach(param => {
      if (param.default !== undefined) {
        defaultParams[param.name] = param.default;
      }
    });
    setParameters(defaultParams);
  }, [selectedEndpoint]);

  const executeRequest = async () => {
    setLoading(true);
    setResponse(null);

    try {
      const startTime = Date.now();
      
      // Build request
      const url = `http://localhost:8001${selectedEndpoint.path}`;
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'X-API-Key': apiKey
      };

      let requestOptions: RequestInit = {
        method: selectedEndpoint.method,
        headers
      };

      // Add body for POST requests
      if (selectedEndpoint.method === 'POST') {
        requestOptions.body = JSON.stringify(parameters);
      }

      // Add query parameters for GET requests
      let finalUrl = url;
      if (selectedEndpoint.method === 'GET' && Object.keys(parameters).length > 0) {
        const queryParams = new URLSearchParams();
        Object.entries(parameters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            queryParams.append(key, value.toString());
          }
        });
        finalUrl = `${url}?${queryParams.toString()}`;
      }

      const response = await fetch(finalUrl, requestOptions);
      const responseTime = Date.now() - startTime;
      const data = await response.json();

      setResponse({
        status: response.status,
        data,
        headers: Object.fromEntries(response.headers.entries()),
        responseTime
      });

    } catch (error) {
      setResponse({
        status: 0,
        data: { error: error instanceof Error ? error.message : 'Unknown error' },
        headers: {},
        responseTime: 0
      });
    } finally {
      setLoading(false);
    }
  };



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Interactive API Explorer</h1>
        <p className="text-muted-foreground">
          Test Rayuela API endpoints in real-time and generate code automatically
        </p>
      </div>

      <Tabs defaultValue="explorer" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="explorer">API Explorer</TabsTrigger>
          <TabsTrigger value="templates">Integration Templates</TabsTrigger>
          <TabsTrigger value="docs">Documentation</TabsTrigger>
        </TabsList>

        <TabsContent value="explorer" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Panel - Request Configuration */}
        <div className="space-y-6">
          {/* API Key */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Configuration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="apiKey">API Key</Label>
                  <Input
                    id="apiKey"
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    placeholder="ray_your_api_key_here"
                    className="font-mono"
                  />
                  <p className="text-sm text-muted-foreground mt-1">
                    Get your API key at <a href="https://dashboard.rayuela.ai" className="text-blue-600 hover:underline">dashboard.rayuela.ai</a>
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Endpoint Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Select Endpoint
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {API_ENDPOINTS.map((endpoint) => (
                  <div
                    key={endpoint.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedEndpoint.id === endpoint.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-border hover:border-border'
                    }`}
                    onClick={() => setSelectedEndpoint(endpoint)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Badge variant={endpoint.method === 'GET' ? 'secondary' : 'default'}>
                          {endpoint.method}
                        </Badge>
                        <span className="font-medium">{endpoint.name}</span>
                      </div>
                      <Badge variant="outline">{endpoint.category}</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{endpoint.description}</p>
                    <code className="text-xs text-muted-foreground mt-1 block">{endpoint.path}</code>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Parameters */}
          {selectedEndpoint.parameters.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Parameters</CardTitle>
                <CardDescription>
                  Configure the parameters for {selectedEndpoint.name}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {selectedEndpoint.parameters.map((param) => (
                    <div key={param.name}>
                      <Label htmlFor={param.name} className="flex items-center gap-2">
                        {param.name}
                        {param.required && <span className="text-red-500">*</span>}
                      </Label>
                      {param.options ? (
                        <Select
                          value={parameters[param.name]?.toString() || ''}
                          onValueChange={(value) => setParameters(prev => ({ ...prev, [param.name]: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder={`Select ${param.name}`} />
                          </SelectTrigger>
                          <SelectContent>
                            {param.options.map((option) => (
                              <SelectItem key={option} value={option}>
                                {option}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      ) : (
                        <Input
                          id={param.name}
                          type={param.type === 'number' ? 'number' : 'text'}
                          value={parameters[param.name] || ''}
                          onChange={(e) => setParameters(prev => ({ 
                            ...prev, 
                            [param.name]: param.type === 'number' ? Number(e.target.value) : e.target.value 
                          }))}
                          placeholder={param.description}
                        />
                      )}
                      <p className="text-sm text-muted-foreground mt-1">{param.description}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Execute Button */}
          <Button 
            onClick={executeRequest} 
            disabled={loading}
            className="w-full"
            size="lg"
          >
            {loading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            {loading ? 'Executing...' : 'Execute Request'}
          </Button>
        </div>

        {/* Right Panel - Response and Code */}
        <div className="space-y-6">
          {/* Response Visualization */}
          <ResponseVisualizer response={response} endpoint={selectedEndpoint} />

          {/* Code Generation */}
          <CodeGenerator
            endpoint={selectedEndpoint}
            parameters={parameters}
            apiKey={apiKey}
          />
        </div>
      </div>
        </TabsContent>

        <TabsContent value="templates" className="mt-6">
          <IntegrationTemplates />
        </TabsContent>

        <TabsContent value="docs" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>API Documentation</CardTitle>
              <CardDescription>
                Comprehensive guide to using Rayuela API
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3">Quick Start Guide</h3>
                  <div className="space-y-4">
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium mb-2">1. Get Your API Key</h4>
                      <p className="text-sm text-muted-foreground">
                        Sign up at <a href="https://dashboard.rayuela.ai" className="text-blue-600 hover:underline">dashboard.rayuela.ai</a> to get your API key.
                      </p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium mb-2">2. Install the SDK</h4>
                      <code className="block bg-muted px-2 py-1 rounded text-sm">
                        npm install rayuela-sdk
                      </code>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium mb-2">3. Start Getting Recommendations</h4>
                      <pre className="bg-muted p-2 rounded text-sm overflow-x-auto">
{`import Rayuela from 'rayuela-sdk';

const client = new Rayuela({ apiKey: 'your-api-key' });
const recs = await client.recommend('user-123');`}
                      </pre>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-3">Key Features</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium mb-2">🎯 Personalized Recommendations</h4>
                      <p className="text-sm text-muted-foreground">
                        AI-powered recommendations that adapt to user behavior and preferences.
                      </p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium mb-2">🧪 A/B Testing</h4>
                      <p className="text-sm text-muted-foreground">
                        Automatic comparison vs baseline with statistical significance testing.
                      </p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium mb-2">📊 Real-time Analytics</h4>
                      <p className="text-sm text-muted-foreground">
                        Track CTR, CVR, and revenue attribution in real-time.
                      </p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium mb-2">🚀 Quick Integration</h4>
                      <p className="text-sm text-muted-foreground">
                        Get started in minutes with our SDKs and templates.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
