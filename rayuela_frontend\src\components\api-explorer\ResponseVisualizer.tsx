"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  Eye, 
  BarChart3, 
  TrendingUp, 
  Star, 
  DollarSign, 
  Target,
  Lightbulb,
  Zap,
  Users,
  ShoppingCart,
  Clock,
  Award,
  Info
} from "lucide-react";

interface ResponseVisualizerProps {
  response: any;
  endpoint: any;
}

interface RecommendationItem {
  id: number;
  name: string;
  category?: string;
  price?: number;
  score: number;
  explanation?: string;
  source?: string;
  averageRating?: number;
  metadata?: any;
}

export default function ResponseVisualizer({ response, endpoint }: ResponseVisualizerProps) {
  const [selectedItem, setSelectedItem] = useState<RecommendationItem | null>(null);
  const [viewMode, setViewMode] = useState<'cards' | 'table' | 'metrics'>('cards');

  if (!response || !response.data) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Execute a request to see the response visualization</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const data = response.data;
  const isRecommendations = data.items && Array.isArray(data.items);
  const isMetrics = data.ctr_lift !== undefined || data.cvr_lift !== undefined;
  const isAbTest = data.meta?.ab_testing || data.variant;

  const getStatusColor = (status: number) => {
    if (status >= 200 && status < 300) return 'bg-green-100 text-green-800 border-green-200';
    if (status >= 400 && status < 500) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    if (status >= 500) return 'bg-red-100 text-red-800 border-red-200';
    return 'bg-muted text-foreground border-border';
  };

  const formatPrice = (price: number | undefined) => {
    if (!price) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-blue-600';
    if (score >= 0.4) return 'text-yellow-600';
    return 'text-muted-foreground';
  };

  const renderRecommendationCards = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-semibold">
            {data.items.length} Recommendations
          </h3>
          {isAbTest && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <Zap className="h-3 w-3" />
              A/B Test: {data.meta.variant} group
            </Badge>
          )}
        </div>
        <div className="flex gap-2">
          <Button
            size="sm"
            variant={viewMode === 'cards' ? 'default' : 'outline'}
            onClick={() => setViewMode('cards')}
          >
            Cards
          </Button>
          <Button
            size="sm"
            variant={viewMode === 'table' ? 'default' : 'outline'}
            onClick={() => setViewMode('table')}
          >
            Table
          </Button>
          <Button
            size="sm"
            variant={viewMode === 'metrics' ? 'default' : 'outline'}
            onClick={() => setViewMode('metrics')}
          >
            Metrics
          </Button>
        </div>
      </div>

      {viewMode === 'cards' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
          {data.items.map((item: RecommendationItem, index: number) => (
            <Card 
              key={item.id} 
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedItem?.id === item.id ? 'ring-2 ring-blue-500' : ''
              }`}
              onClick={() => setSelectedItem(item)}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <h4 className="font-medium text-sm mb-1">{item.name}</h4>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>{item.category}</span>
                      {item.price && (
                        <>
                          <span>•</span>
                          <span>{formatPrice(item.price)}</span>
                        </>
                      )}
                      {item.averageRating && (
                        <>
                          <span>•</span>
                          <div className="flex items-center gap-1">
                            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                            <span>{item.averageRating.toFixed(1)}</span>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant="outline" className={getScoreColor(item.score)}>
                      {(item.score * 100).toFixed(0)}%
                    </Badge>
                    <div className="text-xs text-muted-foreground mt-1">
                      #{index + 1}
                    </div>
                  </div>
                </div>
                
                {item.explanation && (
                  <div className="flex items-start gap-2 mt-2 p-2 bg-blue-50 rounded text-xs">
                    <Lightbulb className="h-3 w-3 text-blue-600 mt-0.5 flex-shrink-0" />
                    <span className="text-blue-700">{item.explanation}</span>
                  </div>
                )}
                
                <div className="flex items-center justify-between mt-2">
                  <Badge variant="secondary" className="text-xs">
                    {item.source || 'rayuela'}
                  </Badge>
                  <Progress value={item.score * 100} className="w-16 h-1" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {viewMode === 'table' && (
        <div className="border rounded-lg overflow-hidden">
          <div className="overflow-x-auto max-h-96">
            <table className="w-full text-sm">
              <thead className="bg-muted">
                <tr>
                  <th className="text-left p-3 font-medium">#</th>
                  <th className="text-left p-3 font-medium">Product</th>
                  <th className="text-left p-3 font-medium">Category</th>
                  <th className="text-left p-3 font-medium">Price</th>
                  <th className="text-left p-3 font-medium">Score</th>
                  <th className="text-left p-3 font-medium">Source</th>
                </tr>
              </thead>
              <tbody>
                {data.items.map((item: RecommendationItem, index: number) => (
                  <tr 
                    key={item.id} 
                    className="border-t hover:bg-muted cursor-pointer"
                    onClick={() => setSelectedItem(item)}
                  >
                    <td className="p-3">{index + 1}</td>
                    <td className="p-3">
                      <div>
                        <div className="font-medium">{item.name}</div>
                        {item.averageRating && (
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                            <span>{item.averageRating.toFixed(1)}</span>
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="p-3">{item.category}</td>
                    <td className="p-3">{formatPrice(item.price)}</td>
                    <td className="p-3">
                      <Badge variant="outline" className={getScoreColor(item.score)}>
                        {(item.score * 100).toFixed(0)}%
                      </Badge>
                    </td>
                    <td className="p-3">
                      <Badge variant="secondary" className="text-xs">
                        {item.source || 'rayuela'}
                      </Badge>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {viewMode === 'metrics' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <Target className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">Avg Score</span>
              </div>
              <div className="text-2xl font-bold">
                {(data.items.reduce((sum: number, item: RecommendationItem) => sum + item.score, 0) / data.items.length * 100).toFixed(1)}%
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <DollarSign className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">Avg Price</span>
              </div>
              <div className="text-2xl font-bold">
                {formatPrice(
                  data.items
                    .filter((item: RecommendationItem) => item.price)
                    .reduce((sum: number, item: RecommendationItem) => sum + (item.price || 0), 0) / 
                  data.items.filter((item: RecommendationItem) => item.price).length
                )}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <Award className="h-4 w-4 text-purple-500" />
                <span className="text-sm font-medium">Categories</span>
              </div>
              <div className="text-2xl font-bold">
                {new Set(data.items.map((item: RecommendationItem) => item.category)).size}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );

  const renderBusinessMetrics = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Business Impact Metrics</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-4 w-4 text-green-500" />
              <span className="text-sm font-medium">CTR Lift</span>
            </div>
            <div className="text-2xl font-bold text-green-600">
              {((data.ctr_lift || 0) * 100).toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">vs baseline</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <ShoppingCart className="h-4 w-4 text-blue-500" />
              <span className="text-sm font-medium">CVR Lift</span>
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {((data.cvr_lift || 0) * 100).toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">vs baseline</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <DollarSign className="h-4 w-4 text-green-500" />
              <span className="text-sm font-medium">Revenue</span>
            </div>
            <div className="text-2xl font-bold">
              {formatPrice(data.revenue_attribution || 0)}
            </div>
            <p className="text-xs text-muted-foreground">attributed</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Users className="h-4 w-4 text-purple-500" />
              <span className="text-sm font-medium">Engagement</span>
            </div>
            <div className="text-2xl font-bold">
              {((data.engagement_score || 0) * 100).toFixed(0)}
            </div>
            <p className="text-xs text-muted-foreground">score</p>
          </CardContent>
        </Card>
      </div>

      {data.recommendations?.next_steps && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Recommended Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {data.recommendations.next_steps.map((step: string, index: number) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">
                    {index + 1}
                  </span>
                  <span className="text-sm">{step}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );

  const renderAbTestResults = () => (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <h3 className="text-lg font-semibold">A/B Test Results</h3>
        <Badge variant="secondary">
          {data.variant || 'Unknown'} Group
        </Badge>
      </div>
      
      {data.control && data.treatment ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Control (Baseline)</CardTitle>
              <CardDescription>Popular products baseline</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm">Users:</span>
                <span className="font-medium">{data.control.users}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">CTR:</span>
                <span className="font-medium">{(data.control.ctr * 100).toFixed(2)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">CVR:</span>
                <span className="font-medium">{(data.control.cvr * 100).toFixed(2)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Revenue:</span>
                <span className="font-medium">{formatPrice(data.control.revenue)}</span>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Treatment (Rayuela)</CardTitle>
              <CardDescription>Personalized recommendations</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm">Users:</span>
                <span className="font-medium">{data.treatment.users}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">CTR:</span>
                <span className="font-medium">{(data.treatment.ctr * 100).toFixed(2)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">CVR:</span>
                <span className="font-medium">{(data.treatment.cvr * 100).toFixed(2)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Revenue:</span>
                <span className="font-medium">{formatPrice(data.treatment.revenue)}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-muted-foreground">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>A/B test results will appear here once enough data is collected</p>
            </div>
          </CardContent>
        </Card>
      )}

      {data.lifts && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Performance Lift</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {data.lifts.ctrLiftPercentage || '+0%'}
                </div>
                <p className="text-sm text-muted-foreground">CTR Improvement</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {data.lifts.cvrLiftPercentage || '+0%'}
                </div>
                <p className="text-sm text-muted-foreground">CVR Improvement</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Response Visualization
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge className={getStatusColor(response.status)}>
              {response.status}
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {response.responseTime}ms
            </Badge>
          </div>
        </div>
        <CardDescription>
          Interactive visualization of API response data
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="visualization">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="visualization">Visualization</TabsTrigger>
            <TabsTrigger value="raw">Raw Data</TabsTrigger>
          </TabsList>
          
          <TabsContent value="visualization" className="mt-6">
            {isRecommendations && renderRecommendationCards()}
            {isMetrics && renderBusinessMetrics()}
            {isAbTest && data.control && renderAbTestResults()}
            
            {!isRecommendations && !isMetrics && !isAbTest && (
              <div className="text-center text-muted-foreground">
                <Info className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>This response type doesn't have a custom visualization yet</p>
                <p className="text-sm">Check the Raw Data tab to see the full response</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="raw" className="mt-6">
            <pre className="bg-muted p-4 rounded-lg overflow-x-auto text-sm max-h-96">
              {JSON.stringify(data, null, 2)}
            </pre>
          </TabsContent>
        </Tabs>

        {/* Selected Item Details */}
        {selectedItem && (
          <Card className="mt-6 border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="text-base">Selected Item Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">{selectedItem.name}</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Category:</span>
                      <span>{selectedItem.category}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Price:</span>
                      <span>{formatPrice(selectedItem.price)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Score:</span>
                      <span className={getScoreColor(selectedItem.score)}>
                        {(selectedItem.score * 100).toFixed(1)}%
                      </span>
                    </div>
                    {selectedItem.averageRating && (
                      <div className="flex justify-between">
                        <span>Rating:</span>
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                          <span>{selectedItem.averageRating.toFixed(1)}</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                <div>
                  {selectedItem.explanation && (
                    <div>
                      <h5 className="font-medium mb-2 flex items-center gap-1">
                        <Lightbulb className="h-4 w-4" />
                        Why recommended?
                      </h5>
                      <p className="text-sm text-blue-700">{selectedItem.explanation}</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  );
}
