import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowLeft, Package, CheckCircle } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'Inicio Rápido - Node.js',
  description: 'Guía completa para integrar Rayuela en aplicaciones Node.js. Instala dependencias, configura autenticación y obtén tu primera recomendación en menos de 5 minutos.',
  path: '/docs/quickstart/nodejs',
  keywords: ['nodejs', 'javascript', 'inicio rápido', 'tutorial', 'API', 'recomendaciones', 'integración'],
});

export default function NodejsQuickstartPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="mb-8">
          <Button variant="ghost" asChild className="mb-4">
            <Link href="/docs">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver a Documentación
            </Link>
          </Button>
          
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            Inicio Rápido - Node.js
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl">
            Integra Rayuela en tu aplicación Node.js y obtén tu primera recomendación en menos de 5 minutos.
          </p>
        </div>

        {/* Prerequisites */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="w-5 h-5" />
              Requisitos Previos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-muted-foreground">
              <li>• Node.js 14 o superior</li>
              <li>• npm o yarn</li>
              <li>• Una cuenta en Rayuela (regístrate en <Link href="/register" className="text-primary hover:underline">rayuela.ai</Link>)</li>
              <li>• Tu API Key (disponible en el dashboard después del registro)</li>
            </ul>
          </CardContent>
        </Card>

        {/* Step 1: Installation */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Paso 1: Instalación</CardTitle>
            <CardDescription>
              Instala las dependencias necesarias para interactuar con la API de Rayuela.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
              <pre className="text-sm font-mono overflow-x-auto">
{`# Con npm
npm install axios

# Con yarn  
yarn add axios`}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Step 2: Setup */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Paso 2: Configuración Inicial</CardTitle>
            <CardDescription>
              Crea un cliente Node.js para interactuar con la API de Rayuela.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
              <pre className="text-sm font-mono overflow-x-auto">
{`const axios = require('axios');

class RayuelaClient {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseURL = 'https://api.rayuela.ai/api/v1';
    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'X-API-Key': apiKey,
        'Content-Type': 'application/json'
      }
    });
  }

  async testConnection() {
    try {
      const response = await this.client.get('/health/auth');
      return response.status === 200;
    } catch (error) {
      console.error('Error de conexión:', error.message);
      return false;
    }
  }
}

// Inicializar cliente
const client = new RayuelaClient('sk_prod_tu_api_key_aqui');`}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Step 3: Test Connection */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Paso 3: Verificar Conexión</CardTitle>
            <CardDescription>
              Prueba que tu API Key funciona correctamente.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
              <pre className="text-sm font-mono overflow-x-auto">
{`// Probar conexión
async function main() {
  const isConnected = await client.testConnection();
  
  if (isConnected) {
    console.log('✅ Conexión exitosa!');
  } else {
    console.log('❌ Error de conexión. Verifica tu API Key.');
  }
}

main();`}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Step 4: Get Recommendations */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Paso 4: Obtener Recomendaciones</CardTitle>
            <CardDescription>
              Solicita recomendaciones personalizadas para un usuario.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
              <pre className="text-sm font-mono overflow-x-auto">
{`async getRecommendations(userId, options = {}) {
  const payload = {
    user_external_id: userId,
    limit: options.limit || 5,
    filters: options.filters || {}
  };

  try {
    const response = await this.client.post('/recommendations/personalized/query', payload);
    return response.data;
  } catch (error) {
    console.error('Error obteniendo recomendaciones:', error.message);
    throw error;
  }
}

// Ejemplo de uso
const recommendations = await client.getRecommendations('user_123', {
  limit: 5,
  filters: { category: 'electronics' }
});

console.log('Recomendaciones:', recommendations);`}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-success" />
              ¡Listo! Siguientes Pasos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                Ya tienes configurado el cliente Node.js básico. Ahora puedes:
              </p>
              <div className="grid gap-4 md:grid-cols-2">
                <Button asChild variant="outline">
                  <Link href="/docs/api/recommendations">
                    Ver API de Recomendaciones
                  </Link>
                </Button>
                <Button asChild variant="outline">
                  <Link href="/docs/guides/data-ingestion">
                    Guía de Ingesta de Datos
                  </Link>
                </Button>
                <Button asChild variant="outline">
                  <Link href="/docs/api/batch">
                    Carga Masiva de Datos
                  </Link>
                </Button>
                <Button asChild variant="outline">
                  <Link href="/docs/examples/nodejs">
                    Ejemplos Avanzados
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
