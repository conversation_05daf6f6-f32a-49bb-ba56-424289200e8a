{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, elevation = \"soft\", ...props }: React.ComponentProps<\"div\"> & { elevation?: \"none\" | \"sm\" | \"soft\" | \"medium\" | \"glow\" }) {\n  const shadowMap: Record<string, string> = {\n    none: \"shadow-none\",\n    sm: \"shadow-sm\",\n    soft: \"shadow-soft\",\n    medium: \"shadow-medium\",\n    glow: \"shadow-glow\",\n  }\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-lg border\",\n        shadowMap[elevation] ?? \"shadow-soft\",\n        \"rayuela-card-gradient rayuela-card-hover\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, accent = false, ...props }: React.ComponentProps<\"div\"> & { accent?: boolean }) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"text-subheading\", accent ? \"rayuela-accent\" : \"text-foreground\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-caption\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,YAAY,MAAM,EAAE,GAAG,OAAiG;IACjJ,MAAM,YAAoC;QACxC,MAAM;QACN,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM;IACR;IACA,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA,SAAS,CAAC,UAAU,IAAI,eACxB,4CACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,SAAS,KAAK,EAAE,GAAG,OAA2D;IAC5G,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB,SAAS,mBAAmB,mBAAmB;QAC/E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC7B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 active:scale-95\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80 active:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 active:bg-destructive/90\",\n        success:\n          \"border-transparent bg-success text-success-foreground hover:bg-success/80 active:bg-success/90 dark:bg-success/20 dark:text-success dark:border-success/40\",\n        warning:\n          \"border-transparent bg-warning text-warning-foreground hover:bg-warning/80 active:bg-warning/90 dark:bg-warning/20 dark:text-warning dark:border-warning/40\",\n        info:\n          \"border-transparent bg-info text-info-foreground hover:bg-info/80 active:bg-info/90 dark:bg-info/20 dark:text-info dark:border-info/40\",\n        outline: \"text-foreground hover:bg-accent hover:text-accent-foreground\",\n        \"outline-success\": \"border-success/40 text-success hover:bg-success/15 active:bg-success/25 dark:border-success/50 dark:hover:bg-success/20\",\n        \"outline-warning\": \"border-warning/40 text-warning hover:bg-warning/15 active:bg-warning/25 dark:border-warning/50 dark:hover:bg-warning/20\",\n        \"outline-info\": \"border-info/40 text-info hover:bg-info/15 active:bg-info/25 dark:border-info/50 dark:hover:bg-info/20\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,uLACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;YACF,SACE;YACF,MACE;YACF,SAAS;YACT,mBAAmB;YACnB,mBAAmB;YACnB,gBAAgB;QAClB;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/components/ui/spacing-system.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\n// Consistent spacing constants for micro-interactions\nexport const SPACING = {\n  // Icon-text relationships\n  iconText: {\n    sm: \"gap-1.5\", // 6px - for small icons with caption text\n    md: \"gap-2\",   // 8px - for standard icons with body text\n    lg: \"gap-3\",   // 12px - for larger icons or headings\n  },\n  // Element grouping\n  elementGroup: {\n    tight: \"gap-1\",     // 4px - for tightly related elements\n    normal: \"gap-2\",    // 8px - for normal element spacing\n    loose: \"gap-3\",     // 12px - for loosely related elements\n  },\n  // Vertical stacking\n  stack: {\n    xs: \"space-y-1\",    // 4px - for very tight vertical stacking\n    sm: \"space-y-1.5\",  // 6px - for compact lists\n    md: \"space-y-2\",    // 8px - for normal vertical spacing\n    lg: \"space-y-3\",    // 12px - for section spacing\n    xl: \"space-y-4\",    // 16px - for major section breaks\n  },\n  // Container spacing\n  container: {\n    tight: \"py-4 px-3\",   // Compact containers\n    normal: \"py-6 px-4\",  // Standard containers (matches Card)\n    loose: \"py-8 px-6\",   // Spacious containers\n  }\n} as const\n\n// Icon-Text component for consistent alignment\ninterface IconTextProps extends React.HTMLAttributes<HTMLDivElement> {\n  icon: React.ReactNode\n  size?: keyof typeof SPACING.iconText\n  align?: 'start' | 'center' | 'end'\n}\n\nexport function IconText({ \n  icon, \n  children, \n  size = 'md', \n  align = 'center',\n  className, \n  ...props \n}: IconTextProps) {\n  const alignmentClass = {\n    start: 'items-start',\n    center: 'items-center', \n    end: 'items-end'\n  }[align]\n\n  return (\n    <div \n      className={cn(\n        \"flex\", \n        SPACING.iconText[size], \n        alignmentClass,\n        className\n      )} \n      {...props}\n    >\n      <span className=\"shrink-0 flex items-center\">\n        {icon}\n      </span>\n      <span className=\"min-w-0\">\n        {children}\n      </span>\n    </div>\n  )\n}\n\n// Vertical Stack component for consistent vertical spacing\ninterface StackProps extends React.HTMLAttributes<HTMLDivElement> {\n  spacing?: keyof typeof SPACING.stack\n}\n\nexport function Stack({ \n  spacing = 'md', \n  className, \n  children, \n  ...props \n}: StackProps) {\n  return (\n    <div \n      className={cn(\n        SPACING.stack[spacing],\n        className\n      )} \n      {...props}\n    >\n      {children}\n    </div>\n  )\n}\n\n// Horizontal Group component for consistent horizontal spacing\ninterface GroupProps extends React.HTMLAttributes<HTMLDivElement> {\n  spacing?: keyof typeof SPACING.elementGroup\n  align?: 'start' | 'center' | 'end' | 'stretch'\n  wrap?: boolean\n}\n\nexport function Group({ \n  spacing = 'normal', \n  align = 'center',\n  wrap = false,\n  className, \n  children, \n  ...props \n}: GroupProps) {\n  const alignmentClass = {\n    start: 'items-start',\n    center: 'items-center',\n    end: 'items-end', \n    stretch: 'items-stretch'\n  }[align]\n\n  return (\n    <div \n      className={cn(\n        \"flex\", \n        SPACING.elementGroup[spacing], \n        alignmentClass,\n        wrap && \"flex-wrap\",\n        className\n      )} \n      {...props}\n    >\n      {children}\n    </div>\n  )\n}\n\n// Container wrapper with consistent spacing\ninterface ContainerProps extends React.HTMLAttributes<HTMLDivElement> {\n  spacing?: keyof typeof SPACING.container\n  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'\n}\n\nexport function Container({ \n  spacing = 'normal',\n  maxWidth = 'full',\n  className, \n  children, \n  ...props \n}: ContainerProps) {\n  const maxWidthClass = maxWidth === 'full' ? '' : `max-w-${maxWidth}`\n  \n  return (\n    <div \n      className={cn(\n        \"mx-auto\",\n        maxWidthClass,\n        SPACING.container[spacing],\n        className\n      )} \n      {...props}\n    >\n      {children}\n    </div>\n  )\n} \n"], "names": [], "mappings": ";;;;;;;;AACA;;;AAGO,MAAM,UAAU;IACrB,0BAA0B;IAC1B,UAAU;QACR,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IACA,mBAAmB;IACnB,cAAc;QACZ,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IACA,oBAAoB;IACpB,OAAO;QACL,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IACA,oBAAoB;IACpB,WAAW;QACT,OAAO;QACP,QAAQ;QACR,OAAO;IACT;AACF;AASO,SAAS,SAAS,EACvB,IAAI,EACJ,QAAQ,EACR,OAAO,IAAI,EACX,QAAQ,QAAQ,EAChB,SAAS,EACT,GAAG,OACW;IACd,MAAM,iBAAiB;QACrB,OAAO;QACP,QAAQ;QACR,KAAK;IACP,CAAC,CAAC,MAAM;IAER,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,QACA,QAAQ,QAAQ,CAAC,KAAK,EACtB,gBACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,8OAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;AAOO,SAAS,MAAM,EACpB,UAAU,IAAI,EACd,SAAS,EACT,QAAQ,EACR,GAAG,OACQ;IACX,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,QAAQ,KAAK,CAAC,QAAQ,EACtB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AASO,SAAS,MAAM,EACpB,UAAU,QAAQ,EAClB,QAAQ,QAAQ,EAChB,OAAO,KAAK,EACZ,SAAS,EACT,QAAQ,EACR,GAAG,OACQ;IACX,MAAM,iBAAiB;QACrB,OAAO;QACP,QAAQ;QACR,KAAK;QACL,SAAS;IACX,CAAC,CAAC,MAAM;IAER,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,QACA,QAAQ,YAAY,CAAC,QAAQ,EAC7B,gBACA,QAAQ,aACR;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AAQO,SAAS,UAAU,EACxB,UAAU,QAAQ,EAClB,WAAW,MAAM,EACjB,SAAS,EACT,QAAQ,EACR,GAAG,OACY;IACf,MAAM,gBAAgB,aAAa,SAAS,KAAK,CAAC,MAAM,EAAE,UAAU;IAEpE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,WACA,eACA,QAAQ,SAAS,CAAC,QAAQ,EAC1B;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/cloned_repos/rayuela/rayuela_frontend/src/app/%28public%29/pricing/page.tsx"], "sourcesContent": ["import { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport Link from \"next/link\";\nimport { generateMetadata as generateSEOMetadata } from '@/lib/seo';\nimport { Check, Star } from \"lucide-react\";\nimport { Container, Stack, IconText } from '@/components/ui/spacing-system';\n\nexport const metadata = generateSEOMetadata({\n  title: 'Inversión Estratégica en Ventaja Competitiva - Planes Rayuela.ai',\n  description: 'La decisión que separa líderes de seguidores. Planes diseñados para CTOs que entienden que la personalización IA no es un gasto, es poder de mercado.',\n  path: '/pricing',\n  keywords: ['inversión estratégica', 'ventaja competitiva', 'personalización enterprise', 'liderazgo tecnológico', 'poder de mercado'],\n});\n\nconst plans = [\n  {\n    id: \"sandbox\",\n    name: \"Developer Sandbox\",\n    price: \"Gratis\",\n    period: \"para siempre\",\n    description: \"Explorá el potencial de nuestra API sin costo y con tus propios datos\",\n    features: [\n      \"1,000 Llamadas API de Recomendación/mes\",\n      \"10 MB de almacenamiento\",\n      \"Modelos básicos de recomendación\",\n      \"Entrenamiento manual (1/mes)\",\n      \"Analíticas básicas\",\n      \"Soporte por email estándar\",\n      \"🧪 Ideal para validar el impacto\",\n      \"🔄 Reset de datos incluido\"\n    ],\n    cta: \"Probar Gratis\",\n    href: \"/register\",\n    popular: false\n  },\n  {\n    id: \"starter\",\n    name: \"Starter\",\n    price: \"ARS $49.000\",\n    period: \"/mes\",\n    description: \"Funcionalidades esenciales para comenzar a personalizar tu catálogo con IA\",\n    features: [\n      \"100,000 Llamadas API de Recomendación/mes\",\n      \"1 GB de almacenamiento\",\n      \"Modelos básicos e híbridos\",\n      \"Entrenamiento mensual automático\",\n      \"Analíticas básicas\",\n      \"Webhooks y callbacks\",\n      \"Soporte por email prioritario\"\n    ],\n    cta: \"Elegir Starter\",\n    href: \"/register\",\n    popular: false\n  },\n  {\n    id: \"pro\",\n    name: \"Pro\",\n    price: \"ARS $199.000\",\n    period: \"/mes\",\n    description: \"Capacidades avanzadas para escalar personalización y análisis\",\n    features: [\n      \"500,000 Llamadas API de Recomendación/mes\",\n      \"5 GB de almacenamiento\",\n      \"Todos los modelos (LTR, XAI)\",\n      \"Entrenamiento semanal automático\",\n      \"Analíticas detalladas\",\n      \"Streaming Data Ingest\",\n      \"Soporte chat/email rápido\"\n    ],\n    cta: \"Escalar Ahora\",\n    href: \"/register\",\n    popular: true\n  },\n  {\n    id: \"enterprise\",\n    name: \"Enterprise\",\n    price: \"Precio personalizado\",\n    period: \"\",\n    description: \"Arquitectura dedicada y soporte experto para requisitos empresariales\",\n    features: [\n      \"Llamadas API ilimitadas\",\n      \"Almacenamiento ilimitado\",\n      \"Todos los modelos + personalizados\",\n      \"Entrenamiento bajo demanda/continuo\",\n      \"Analíticas avanzadas + consultoría\",\n      \"Infraestructura dedicada\",\n      \"Integración personalizada\",\n      \"Soporte dedicado 24/7\",\n      \"SLA 99.99%\"\n    ],\n    cta: \"Contactar Ventas\",\n    href: \"/contact-sales\",\n    popular: false\n  }\n];\n\n// Helper functions for styling\nconst getPriceStyles = (plan: typeof plans[0]) => {\n  if (plan.id === 'sandbox') return 'text-success text-3xl';\n  if (plan.id === 'enterprise') return 'text-accent text-2xl';\n  return 'text-primary text-4xl';\n};\n\nconst getButtonStyles = (plan: typeof plans[0]) => {\n  if (plan.popular) return 'bg-primary hover:bg-primary/90 shadow-glow-primary text-white';\n  if (plan.id === 'sandbox') return 'bg-success hover:bg-success/90 text-white border-success';\n  if (plan.id === 'enterprise') return 'bg-accent hover:bg-accent/90 text-white border-accent';\n  return '';\n};\n\nconst getButtonVariant = (plan: typeof plans[0]) => {\n  if (plan.popular || plan.id === 'sandbox' || plan.id === 'enterprise') return 'default';\n  return 'outline';\n};\n\nexport default function PricingPage() {\n  const offerSchema = {\n    '@context': 'https://schema.org',\n    '@type': 'Product',\n    name: 'Rayuela Recommendation API',\n    description: 'Sistema de recomendaciones como servicio',\n    offers: plans.map(plan => ({\n      '@type': 'Offer',\n      name: plan.name,\n      description: plan.description,\n      price: plan.price === 'Gratis' ? '0' : plan.price.replace('$', ''),\n      priceCurrency: 'ARS',\n      availability: 'https://schema.org/InStock',\n    })),\n  };\n\n  return (\n    <>\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{\n          __html: JSON.stringify(offerSchema),\n        }}\n      />\n\n      <div className=\"min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end\">\n        <Container spacing=\"loose\" maxWidth=\"full\">\n          <Stack spacing=\"xl\">\n            {/* Hero Section */}\n            <div className=\"text-center\">\n              <Stack spacing=\"lg\" className=\"relative\">\n                <div className=\"inline-flex items-center gap-2 bg-primary/10 border border-primary/20 rounded-full px-6 py-3 mb-10\">\n                  <span className=\"text-primary font-semibold text-sm\">\n                    Planes flexibles para cada etapa\n                  </span>\n                </div>\n\n                <h1 className=\"text-display-lg md:text-display-xl text-foreground leading-extra-tight\">\n                  <span className=\"font-extrabold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n                    Planes que Crecen\n                  </span>\n                  <br />\n                  <span className=\"font-light text-muted-foreground\">\n                    con tu Negocio\n                  </span>\n                </h1>\n\n                <p className=\"text-body-lg text-muted-foreground max-w-4xl mx-auto leading-relaxed\">\n                  <strong className=\"text-foreground\">Desplegá personalización con IA en días, no meses.</strong>{' '}\n                  Desde una prueba sin costo hasta una arquitectura dedicada, elegí exactamente lo que tu equipo necesita.\n                </p>\n\n                {/* Value Proposition */}\n                <div className=\"bg-gradient-to-r from-primary/10 to-accent/10 rounded-lg p-6 max-w-4xl mx-auto mt-8\">\n                  <p className=\"text-body-medium text-foreground mb-4\">\n                    <strong>💡 Valor Cuantificable:</strong> Nuestras recomendaciones personalizadas en tiempo real aumentan la conversión,\n                    el ticket promedio y el engagement de tus usuarios.\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                    <div className=\"text-center\">\n                      <div className=\"text-success font-semibold\">📈 +15-30%</div>\n                      <div className=\"text-muted-foreground\">Conversión</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-success font-semibold\">💰 +20-40%</div>\n                      <div className=\"text-muted-foreground\">Ticket Promedio</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-success font-semibold\">⚡ +25-50%</div>\n                      <div className=\"text-muted-foreground\">Engagement</div>\n                    </div>\n                  </div>\n                </div>\n\n              </Stack>\n            </div>\n\n            {/* Pricing Cards */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {plans.map((plan) => (\n              <Card key={plan.id} className={`relative h-full transition-all duration-300 hover:scale-105 ${\n                plan.popular\n                  ? 'border-primary shadow-glow-primary scale-105 bg-gradient-to-br from-primary/5 to-accent/5'\n                  : 'border-border hover:border-primary/50 hover:shadow-medium'\n              }`}>\n                {plan.popular && (\n                  <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 z-10\">\n                    <Badge className=\"bg-gradient-to-r from-primary to-accent text-white px-6 py-2 shadow-glow-primary\">\n                      <IconText icon={<Star className=\"w-4 h-4\" />} size=\"sm\">\n                        🏆 Más Popular\n                      </IconText>\n                    </Badge>\n                  </div>\n                )}\n\n                {plan.id === 'sandbox' && (\n                  <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 z-10\">\n                    <Badge className=\"bg-gradient-to-r from-gradient-semantic-success-start to-gradient-semantic-success-end text-white px-6 py-2 shadow-soft\">\n                      <IconText icon={<span>🚀</span>} size=\"sm\">\n                        Empezar Aquí\n                      </IconText>\n                    </Badge>\n                  </div>\n                )}\n\n                {plan.id === 'enterprise' && (\n                  <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 z-10\">\n                    <Badge className=\"bg-gradient-to-r from-gradient-semantic-brand-start to-gradient-semantic-brand-end text-white px-6 py-2 shadow-soft\">\n                      <IconText icon={<span>👑</span>} size=\"sm\">\n                        Enterprise\n                      </IconText>\n                    </Badge>\n                  </div>\n                )}\n\n                <CardHeader className=\"text-center pb-6 pt-12\">\n                  <CardTitle className=\"text-heading-lg font-bold text-foreground mb-4\">\n                    {plan.name}\n                  </CardTitle>\n                  <div className=\"mb-4\">\n                    <div className=\"flex items-baseline justify-center gap-1\">\n                      <span className={`font-bold ${getPriceStyles(plan)}`}>\n                        {plan.price}\n                      </span>\n                      {plan.period && (\n                        <span className=\"text-muted-foreground text-sm\">\n                          {plan.period}\n                        </span>\n                      )}\n                    </div>\n                    {plan.id === 'sandbox' && (\n                      <div className=\"text-xs text-success font-semibold mt-1\">\n                        Ideal para validar ROI\n                      </div>\n                    )}\n                    {plan.id === 'pro' && (\n                      <div className=\"text-xs text-primary font-semibold mt-1\">\n                        Para equipos en crecimiento\n                      </div>\n                    )}\n                  </div>\n                  <CardDescription className=\"text-muted-foreground leading-relaxed\">\n                    {plan.description}\n                  </CardDescription>\n                </CardHeader>\n\n                <CardContent className=\"flex-1 pt-0\">\n                  <Stack spacing=\"sm\" className=\"mb-8\">\n                    {plan.features.map((feature, featureIndex) => (\n                      <IconText\n                        key={`${plan.id}-feature-${featureIndex}`}\n                        icon={<Check className=\"w-4 h-4 text-success flex-shrink-0\" />}\n                        align=\"start\"\n                        size=\"sm\"\n                      >\n                        <span className=\"text-foreground text-sm leading-relaxed\">{feature}</span>\n                      </IconText>\n                    ))}\n                  </Stack>\n\n                  <Button\n                    asChild\n                    className={`w-full font-semibold ${getButtonStyles(plan)} ${plan.id === 'sandbox' ? 'bg-success text-white' : ''}`}\n                    variant={getButtonVariant(plan)}\n                    size=\"lg\"\n                  >\n                    <Link href={plan.href} className=\"flex items-center justify-center gap-2\">\n                      {plan.id === 'sandbox' && '🚀'}\n                      {plan.id === 'pro' && '⚡'}\n                      {plan.id === 'enterprise' && '👑'}\n                      {plan.cta}\n                    </Link>\n                  </Button>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n\n          {/* Pricing FAQ */}\n          <div className=\"bg-card rounded-lg p-8 shadow-lg\">\n            <h2 className=\"text-heading-large text-center text-foreground mb-8\">\n              Preguntas Frecuentes sobre Precios\n            </h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\">\n              <div className=\"space-y-4\">\n                <div>\n                  <h3 className=\"font-semibold text-foreground mb-2\">\n                    ¿Qué sucede si excedo mi límite?\n                  </h3>\n                  <p className=\"text-muted-foreground text-sm\">\n                    Recibirás notificaciones automáticas al alcanzar el 80% de tu límite. Si lo excedes,\n                    las solicitudes se pausarán temporalmente hasta que actualices tu plan o el próximo ciclo de facturación.\n                  </p>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-foreground mb-2\">\n                    ¿Puedo cambiar de plan en cualquier momento?\n                  </h3>\n                  <p className=\"text-muted-foreground text-sm\">\n                    Sí, puedes actualizar o degradar tu plan desde tu dashboard en cualquier momento.\n                    Los cambios se aplican inmediatamente.\n                  </p>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-foreground mb-2\">\n                    ¿Qué métodos de pago aceptan?\n                  </h3>\n                  <p className=\"text-muted-foreground text-sm\">\n                    Aceptamos todas las tarjetas de crédito y débito a través de Mercado Pago,\n                    incluyendo transferencias bancarias y otros métodos locales.\n                  </p>\n                </div>\n              </div>\n              <div className=\"space-y-4\">\n                <div>\n                  <h3 className=\"font-semibold text-foreground mb-2\">\n                    ¿Cómo se calculan los costos de almacenamiento?\n                  </h3>\n                  <p className=\"text-muted-foreground text-sm\">\n                    El almacenamiento incluye todos tus datos: productos, usuarios, interacciones y modelos entrenados.\n                    Se mide el total de datos almacenados en tu cuenta.\n                  </p>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-foreground mb-2\">\n                    ¿Qué tan seguido se entrenan los modelos?\n                  </h3>\n                  <p className=\"text-muted-foreground text-sm\">\n                    Sandbox: Manual, Starter: Mensual, Pro: Semanal, Enterprise: Bajo demanda.\n                    Entrenamientos más frecuentes mejoran la precisión de las recomendaciones.\n                  </p>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-foreground mb-2\">\n                    ¿Los precios pueden cambiar?\n                  </h3>\n                  <p className=\"text-muted-foreground text-sm\">\n                    Los precios en ARS pueden ajustarse periódicamente debido a la inflación.\n                    Te notificaremos con 30 días de anticipación sobre cualquier cambio.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Strategic Investment Section */}\n          <div className=\"bg-card rounded-lg p-8 shadow-lg\">\n            <h2 className=\"text-heading-large text-center text-foreground mb-8\">\n              Decisiones Estratégicas Inteligentes\n            </h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              <div>\n                <h3 className=\"text-subheading text-foreground mb-2\">\n                  ¿Por qué no construir esto internamente?\n                </h3>\n                <p className=\"text-muted-foreground\">\n                  Desarrollar un motor de recomendaciones robusto suele demandar meses de trabajo y perfiles de machine learning difíciles de contratar. Rayuela te brinda esa capacidad lista para usar, de modo que tu equipo pueda centrarse en estrategia y experiencia de cliente.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-subheading text-foreground mb-2\">\n                  ¿Qué ROI puedo esperar?\n                </h3>\n                <p className=\"text-muted-foreground\">\n                  En proyectos recientes, las empresas que usan Rayuela han logrado incrementos de 25-40 % en conversión y 20-35 % en ticket promedio. El retorno estimado ronda 300-500 % en los primeros seis meses.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-subheading text-foreground mb-2\">\n                  ¿Cómo me aseguro de que funcione para mi negocio?\n                </h3>\n                <p className=\"text-muted-foreground\">\n                  El <strong>Developer Sandbox</strong> es gratis para siempre. Prueba con tus datos reales, ve el impacto, y luego escala. Los planes pagos incluyen 30 días de prueba sin riesgo. Validamos el poder antes de la inversión.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-subheading text-foreground mb-2\">\n                  ¿Qué pasa si mis competidores también lo usan?\n                </h3>\n                <p className=\"text-muted-foreground\">\n                  Nuestro programa de early adopters está limitado para ofrecer acompañamiento cercano y aprendizaje compartido. Cada implementación pionera nos ayuda a mejorar la plataforma para toda la comunidad.\n                </p>\n              </div>\n            </div>\n          </div>\n          </Stack>\n        </Container>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;;AAEO,MAAM,WAAW,CAAA,GAAA,iHAAA,CAAA,mBAAmB,AAAD,EAAE;IAC1C,OAAO;IACP,aAAa;IACb,MAAM;IACN,UAAU;QAAC;QAAyB;QAAuB;QAA8B;QAAyB;KAAmB;AACvI;AAEA,MAAM,QAAQ;IACZ;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,MAAM;QACN,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,MAAM;QACN,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,MAAM;QACN,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,MAAM;QACN,SAAS;IACX;CACD;AAED,+BAA+B;AAC/B,MAAM,iBAAiB,CAAC;IACtB,IAAI,KAAK,EAAE,KAAK,WAAW,OAAO;IAClC,IAAI,KAAK,EAAE,KAAK,cAAc,OAAO;IACrC,OAAO;AACT;AAEA,MAAM,kBAAkB,CAAC;IACvB,IAAI,KAAK,OAAO,EAAE,OAAO;IACzB,IAAI,KAAK,EAAE,KAAK,WAAW,OAAO;IAClC,IAAI,KAAK,EAAE,KAAK,cAAc,OAAO;IACrC,OAAO;AACT;AAEA,MAAM,mBAAmB,CAAC;IACxB,IAAI,KAAK,OAAO,IAAI,KAAK,EAAE,KAAK,aAAa,KAAK,EAAE,KAAK,cAAc,OAAO;IAC9E,OAAO;AACT;AAEe,SAAS;IACtB,MAAM,cAAc;QAClB,YAAY;QACZ,SAAS;QACT,MAAM;QACN,aAAa;QACb,QAAQ,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACzB,SAAS;gBACT,MAAM,KAAK,IAAI;gBACf,aAAa,KAAK,WAAW;gBAC7B,OAAO,KAAK,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,CAAC,OAAO,CAAC,KAAK;gBAC/D,eAAe;gBACf,cAAc;YAChB,CAAC;IACH;IAEA,qBACE;;0BACE,8OAAC;gBACC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC;gBACzB;;;;;;0BAGF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6IAAA,CAAA,YAAS;oBAAC,SAAQ;oBAAQ,UAAS;8BAClC,cAAA,8OAAC,6IAAA,CAAA,QAAK;wBAAC,SAAQ;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6IAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAK,WAAU;;sDAC5B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;sDAKvD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAuF;;;;;;8DAGvG,8OAAC;;;;;8DACD,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAKrD,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAO,WAAU;8DAAkB;;;;;;gDAA4D;gDAAI;;;;;;;sDAKtG,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAgC;;;;;;;8DAG1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASjD,8OAAC;gCAAI,WAAU;0CACd,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,gIAAA,CAAA,OAAI;wCAAe,WAAW,CAAC,4DAA4D,EAC1F,KAAK,OAAO,GACR,8FACA,6DACJ;;4CACC,KAAK,OAAO,kBACX,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DACf,cAAA,8OAAC,6IAAA,CAAA,WAAQ;wDAAC,oBAAM,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAc,MAAK;kEAAK;;;;;;;;;;;;;;;;4CAO7D,KAAK,EAAE,KAAK,2BACX,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DACf,cAAA,8OAAC,6IAAA,CAAA,WAAQ;wDAAC,oBAAM,8OAAC;sEAAK;;;;;;wDAAW,MAAK;kEAAK;;;;;;;;;;;;;;;;4CAOhD,KAAK,EAAE,KAAK,8BACX,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DACf,cAAA,8OAAC,6IAAA,CAAA,WAAQ;wDAAC,oBAAM,8OAAC;sEAAK;;;;;;wDAAW,MAAK;kEAAK;;;;;;;;;;;;;;;;0DAOjD,8OAAC,gIAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,KAAK,IAAI;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAW,CAAC,UAAU,EAAE,eAAe,OAAO;kFACjD,KAAK,KAAK;;;;;;oEAEZ,KAAK,MAAM,kBACV,8OAAC;wEAAK,WAAU;kFACb,KAAK,MAAM;;;;;;;;;;;;4DAIjB,KAAK,EAAE,KAAK,2BACX,8OAAC;gEAAI,WAAU;0EAA0C;;;;;;4DAI1D,KAAK,EAAE,KAAK,uBACX,8OAAC;gEAAI,WAAU;0EAA0C;;;;;;;;;;;;kEAK7D,8OAAC,gIAAA,CAAA,kBAAe;wDAAC,WAAU;kEACxB,KAAK,WAAW;;;;;;;;;;;;0DAIrB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC,6IAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAK,WAAU;kEAC3B,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,8OAAC,6IAAA,CAAA,WAAQ;gEAEP,oBAAM,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEACvB,OAAM;gEACN,MAAK;0EAEL,cAAA,8OAAC;oEAAK,WAAU;8EAA2C;;;;;;+DALtD,GAAG,KAAK,EAAE,CAAC,SAAS,EAAE,cAAc;;;;;;;;;;kEAU/C,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO;wDACP,WAAW,CAAC,qBAAqB,EAAE,gBAAgB,MAAM,CAAC,EAAE,KAAK,EAAE,KAAK,YAAY,0BAA0B,IAAI;wDAClH,SAAS,iBAAiB;wDAC1B,MAAK;kEAEL,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,KAAK,IAAI;4DAAE,WAAU;;gEAC9B,KAAK,EAAE,KAAK,aAAa;gEACzB,KAAK,EAAE,KAAK,SAAS;gEACrB,KAAK,EAAE,KAAK,gBAAgB;gEAC5B,KAAK,GAAG;;;;;;;;;;;;;;;;;;;uCA1FN,KAAK,EAAE;;;;;;;;;;0CAmGtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDAGpE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAqC;;;;;;0EAGnD,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAK/C,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAqC;;;;;;0EAGnD,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAK/C,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAqC;;;;;;0EAGnD,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAMjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAqC;;;;;;0EAGnD,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAK/C,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAqC;;;;;;0EAGnD,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAK/C,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAqC;;;;;;0EAGnD,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDAIpE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEAGrD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAKvC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEAGrD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAKvC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEAGrD,8OAAC;wDAAE,WAAU;;4DAAwB;0EAChC,8OAAC;0EAAO;;;;;;4DAA0B;;;;;;;;;;;;;0DAIzC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEAGrD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrD", "debugId": null}}]}