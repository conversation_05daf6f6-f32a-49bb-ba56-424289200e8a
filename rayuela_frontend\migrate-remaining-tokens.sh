#!/bin/bash

# Script para migrar tokens restantes a sistema semántico
# Ejecutar desde la raíz del proyecto rayuela_frontend

echo "🔄 Iniciando migración de tokens restantes..."

# Función para aplicar migraciones de texto
migrate_text_tokens() {
    echo "📝 Migrando tokens de texto..."
    
    # Texto principal
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-gray-900 dark:text-white/text-foreground/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-gray-800 dark:text-white/text-foreground/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-gray-700 dark:text-gray-300/text-foreground/g' {} \;
    
    # Texto secundario
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-gray-600 dark:text-gray-300/text-muted-foreground/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-gray-600 dark:text-gray-400/text-muted-foreground/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-gray-500 dark:text-gray-400/text-muted-foreground/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-gray-500 dark:text-gray-300/text-muted-foreground/g' {} \;
    
    # Texto muy sutil
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-gray-400 dark:text-gray-500/text-muted-foreground\/70/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-gray-400 dark:text-gray-600/text-muted-foreground\/70/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-gray-300 dark:text-gray-600/text-muted-foreground\/50/g' {} \;
    
    # Casos específicos de solo modo claro
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-gray-900/text-foreground/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-gray-800/text-foreground/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-gray-700/text-foreground/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-gray-600/text-muted-foreground/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-gray-500/text-muted-foreground/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-gray-400/text-muted-foreground\/70/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-gray-300/text-muted-foreground\/50/g' {} \;
}

# Función para aplicar migraciones de fondo
migrate_background_tokens() {
    echo "🎨 Migrando tokens de fondo..."
    
    # Fondos principales
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-gray-50 dark:bg-gray-900/bg-muted/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-gray-100 dark:bg-gray-800/bg-muted/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-gray-50 dark:bg-gray-800/bg-muted/g' {} \;
    
    # Fondos de hover
    find ./src -name "*.tsx" -type f -exec sed -i 's/hover:bg-gray-50 dark:hover:bg-gray-800/hover:bg-muted\/50/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/hover:bg-gray-100 dark:hover:bg-gray-700/hover:bg-muted\/70/g' {} \;
    
    # Casos específicos
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-gray-50/bg-muted/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-gray-100/bg-muted/g' {} \;
}

# Función para aplicar migraciones de borde
migrate_border_tokens() {
    echo "🔲 Migrando tokens de borde..."
    
    # Bordes principales
    find ./src -name "*.tsx" -type f -exec sed -i 's/border-gray-200 dark:border-gray-700/border-border/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/border-gray-200 dark:border-gray-800/border-border/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/border-gray-300 dark:border-gray-700/border-border/g' {} \;
    
    # Casos específicos
    find ./src -name "*.tsx" -type f -exec sed -i 's/border-gray-200/border-border/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/border-gray-300/border-border/g' {} \;
}

# Función para validar migraciones
validate_migrations() {
    echo "🔍 Validando migraciones..."
    
    REMAINING_TEXT=$(grep -r "text-gray-[0-9]" src/ --include="*.tsx" | wc -l)
    REMAINING_BG=$(grep -r "bg-gray-[0-9]" src/ --include="*.tsx" | wc -l)
    REMAINING_BORDER=$(grep -r "border-gray-[0-9]" src/ --include="*.tsx" | wc -l)
    
    echo "📊 Resultados de la migración:"
    echo "   Tokens de texto restantes: $REMAINING_TEXT"
    echo "   Tokens de fondo restantes: $REMAINING_BG"
    echo "   Tokens de borde restantes: $REMAINING_BORDER"
    
    TOTAL_REMAINING=$((REMAINING_TEXT + REMAINING_BG + REMAINING_BORDER))
    
    if [ $TOTAL_REMAINING -lt 50 ]; then
        echo "🎉 ¡Migración exitosa! Quedan menos de 50 tokens por migrar"
        echo "✅ Sistema de tokens semánticos implementado"
    else
        echo "⚠️  Quedan $TOTAL_REMAINING tokens por migrar"
        echo "📋 Archivos con más tokens restantes:"
        grep -r "text-gray-[0-9]\|bg-gray-[0-9]\|border-gray-[0-9]" src/ --include="*.tsx" -l | head -10
    fi
}

# Ejecutar migraciones
migrate_text_tokens
migrate_background_tokens
migrate_border_tokens

# Validar resultados
validate_migrations

echo "✅ Migración de tokens completada!"
echo "🚀 Servidor listo para reiniciar con tokens semánticos"
