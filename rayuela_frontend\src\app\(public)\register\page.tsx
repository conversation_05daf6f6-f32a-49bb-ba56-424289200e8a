import { Metadata } from "next";
import RegisterForm from "@/components/auth/RegisterForm";

export const metadata: Metadata = {
  title: "Registrarse | Rayuela.ai",
  description: "Crea una cuenta en Rayuela.ai para empezar a usar nuestras funcionalidades de recomendación."
};

export default function RegisterPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-heading-lg font-bold text-foreground mb-2">
            Crear cuenta
          </h1>
          <p className="text-muted-foreground">
            Plataforma de recomendaciones inteligentes para equipos de desarrollo
          </p>
        </div>

        <div className="bg-card/95 backdrop-blur-sm rounded-2xl border border-border/50 shadow-soft p-8">
          <RegisterForm showHeader={false} />

          <div className="mt-6 text-center">
            <p className="text-sm text-muted-foreground">
              ¿Ya tienes cuenta?{' '}
              <a href="/login" className="text-primary hover:text-primary/80 font-medium underline underline-offset-2">
                Inicia sesión
              </a>
            </p>
          </div>
        </div>

        {/* Trust indicators */}
        <div className="text-center mt-8">
          <p className="text-xs text-muted-foreground">
            Infraestructura Google Cloud • Cumplimiento SOC 2 • Sin tarjeta de crédito
          </p>
        </div>
      </div>
    </div>
  );
}
