import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowLeft, Database, Upload, Users, Package, Activity } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'Guía de Ingesta de Datos - Rayuela',
  description: 'Guía completa para cargar datos en Rayuela. Aprende a estructurar productos, usuarios e interacciones para obtener las mejores recomendaciones.',
  path: '/docs/guides/data-ingestion',
  keywords: ['ingesta', 'datos', 'productos', 'usuarios', 'interacciones', 'batch', 'CSV'],
});

export default function DataIngestionGuidePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="mb-8">
          <Button variant="ghost" asChild className="mb-4">
            <Link href="/docs">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver a Documentación
            </Link>
          </Button>
          
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            Guía de Ingesta de Datos
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl">
            Aprende a estructurar y cargar tus datos en Rayuela para obtener las mejores recomendaciones posibles.
          </p>
        </div>

        {/* Overview */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="w-5 h-5" />
              Tipos de Datos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Rayuela necesita tres tipos principales de datos para generar recomendaciones efectivas:
            </p>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="p-4 border rounded-lg">
                <Package className="w-8 h-8 text-primary mb-2" />
                <h4 className="font-semibold mb-2">Productos</h4>
                <p className="text-sm text-muted-foreground">
                  Catálogo de productos con características, precios y metadatos.
                </p>
              </div>
              <div className="p-4 border rounded-lg">
                <Users className="w-8 h-8 text-primary mb-2" />
                <h4 className="font-semibold mb-2">Usuarios</h4>
                <p className="text-sm text-muted-foreground">
                  Perfiles de usuarios con preferencias y características demográficas.
                </p>
              </div>
              <div className="p-4 border rounded-lg">
                <Activity className="w-8 h-8 text-primary mb-2" />
                <h4 className="font-semibold mb-2">Interacciones</h4>
                <p className="text-sm text-muted-foreground">
                  Historial de comportamiento: vistas, compras, ratings, etc.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Products Schema */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Estructura de Productos</CardTitle>
            <CardDescription>
              Campos requeridos y opcionales para productos
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <h4 className="font-semibold">Campos Requeridos</h4>
              <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
                <pre className="text-sm font-mono overflow-x-auto">
{`{
  "external_id": "prod_123",        // ID único en tu sistema
  "name": "Smartphone XYZ",        // Nombre del producto
  "description": "Descripción...",  // Descripción detallada
  "price": 599.99,                 // Precio actual
  "category": "electronics"        // Categoría principal
}`}
                </pre>
              </div>

              <h4 className="font-semibold">Campos Opcionales (Recomendados)</h4>
              <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
                <pre className="text-sm font-mono overflow-x-auto">
{`{
  "brand": "Apple",
  "image_url": "https://...",
  "in_stock": true,
  "rating": 4.5,
  "review_count": 1250,
  "tags": ["smartphone", "5g", "premium"],
  "attributes": {
    "color": "black",
    "storage": "128GB",
    "screen_size": "6.1",
    "weight": "174g"
  }
}`}
                </pre>
              </div>

              <div className="p-4 bg-info/10 border border-info/20 rounded-lg">
                <p className="text-sm text-info-foreground">
                  <strong>💡 Tip:</strong> Cuantos más atributos proporciones, mejores serán las recomendaciones basadas en contenido.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Users Schema */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Estructura de Usuarios</CardTitle>
            <CardDescription>
              Información de usuarios para personalización
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <h4 className="font-semibold">Campos Requeridos</h4>
              <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
                <pre className="text-sm font-mono overflow-x-auto">
{`{
  "external_id": "user_456"  // ID único en tu sistema
}`}
                </pre>
              </div>

              <h4 className="font-semibold">Campos Opcionales (Mejoran Personalización)</h4>
              <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
                <pre className="text-sm font-mono overflow-x-auto">
{`{
  "preferred_categories": ["electronics", "books"],
  "price_range_min": 50,
  "price_range_max": 1000,
  "attributes": {
    "age": 28,
    "gender": "M",
    "location": "Madrid",
    "interests": ["technology", "gaming"],
    "subscription_tier": "premium"
  }
}`}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Interactions Schema */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Estructura de Interacciones</CardTitle>
            <CardDescription>
              Eventos de comportamiento del usuario
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <h4 className="font-semibold">Campos Requeridos</h4>
              <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
                <pre className="text-sm font-mono overflow-x-auto">
{`{
  "user_external_id": "user_456",
  "product_external_id": "prod_123",
  "interaction_type": "view",  // view, purchase, rating, cart_add
  "value": 1.0                 // Intensidad (1.0 para view, 5.0 para rating)
}`}
                </pre>
              </div>

              <h4 className="font-semibold">Tipos de Interacción</h4>
              <div className="grid gap-3 md:grid-cols-2">
                <div className="p-3 border rounded">
                  <Badge variant="outline" className="mb-2">view</Badge>
                  <p className="text-sm text-muted-foreground">Usuario vio el producto (value: 1.0)</p>
                </div>
                <div className="p-3 border rounded">
                  <Badge variant="outline" className="mb-2">purchase</Badge>
                  <p className="text-sm text-muted-foreground">Usuario compró el producto (value: cantidad)</p>
                </div>
                <div className="p-3 border rounded">
                  <Badge variant="outline" className="mb-2">rating</Badge>
                  <p className="text-sm text-muted-foreground">Usuario calificó el producto (value: 1-5)</p>
                </div>
                <div className="p-3 border rounded">
                  <Badge variant="outline" className="mb-2">cart_add</Badge>
                  <p className="text-sm text-muted-foreground">Usuario agregó al carrito (value: 1.0)</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Batch Upload */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="w-5 h-5" />
              Carga Masiva
            </CardTitle>
            <div className="flex gap-2 mt-2">
              <Badge variant="default">POST</Badge>
              <Badge variant="outline">/ingestion/batch</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Para cargar grandes volúmenes de datos, usa el endpoint de ingesta masiva:
            </p>

            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
              <pre className="text-sm font-mono overflow-x-auto">
{`curl -X POST "https://api.rayuela.ai/api/v1/ingestion/batch" \\
     -H "X-API-Key: sk_prod_tu_api_key" \\
     -H "Content-Type: application/json" \\
     -d '{
       "products": [
         {
           "external_id": "prod_001",
           "name": "Producto 1",
           "price": 99.99,
           "category": "electronics"
         }
       ],
       "end_users": [
         {
           "external_id": "user_001",
           "preferred_categories": ["electronics"]
         }
       ],
       "interactions": [
         {
           "user_external_id": "user_001",
           "product_external_id": "prod_001",
           "interaction_type": "purchase",
           "value": 1.0
         }
       ]
     }'`}
              </pre>
            </div>

            <div className="mt-4 p-4 bg-warning/10 border border-warning/20 rounded-lg">
              <p className="text-sm text-warning-foreground">
                <strong>⚠️ Límites:</strong> Máximo 1000 elementos por tipo en cada request. Para volúmenes mayores, divide en múltiples requests.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Best Practices */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Mejores Prácticas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 border-l-4 border-success">
                <h4 className="font-semibold text-success mb-2">✅ Recomendado</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Usa IDs externos consistentes y únicos</li>
                  <li>• Incluye tantos atributos como sea posible</li>
                  <li>• Actualiza precios y stock regularmente</li>
                  <li>• Registra interacciones en tiempo real</li>
                  <li>• Usa categorías jerárquicas (ej: "electronics/smartphones")</li>
                </ul>
              </div>
              <div className="p-4 border-l-4 border-destructive">
                <h4 className="font-semibold text-destructive mb-2">❌ Evitar</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• IDs externos que cambien con el tiempo</li>
                  <li>• Productos sin categoría o con categorías muy genéricas</li>
                  <li>• Cargar solo interacciones de compra (incluye vistas)</li>
                  <li>• Atributos con valores inconsistentes</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Siguientes Pasos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <Button asChild variant="outline">
                <Link href="/docs/api/batch">
                  API de Ingesta Masiva
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/docs/quickstart/python">
                  Ejemplos de Código
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/docs/api/recommendations">
                  Obtener Recomendaciones
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/dashboard">
                  Ver Dashboard
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
