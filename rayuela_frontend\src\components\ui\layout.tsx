import * as React from "react"
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "./card"
import { TableRow } from "./table"
import { cn } from "@/lib/utils"

// Componente para agrupar secciones con fondo sutil
interface SectionGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  variant?: 'default' | 'subtle' | 'elevated'
}

export function SectionGroup({ 
  children, 
  variant = 'default', 
  className, 
  ...props 
}: SectionGroupProps) {
  const variants = {
    default: "",
    subtle: "bg-card/30 dark:bg-card/20 border border-border/50 rounded-lg p-6 rayuela-subtle-gradient",
    elevated: "bg-card border border-border shadow-sm rounded-lg p-6 rayuela-card-gradient"
  }

  return (
    <div 
      className={cn(variants[variant], className)} 
      {...props}
    >
      {children}
    </div>
  )
}

// Divider visual sutil para separar secciones
interface SectionDividerProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'line' | 'space' | 'gradient'
  spacing?: 'sm' | 'md' | 'lg'
}

export function SectionDivider({ 
  variant = 'line', 
  spacing = 'md',
  className, 
  ...props 
}: SectionDividerProps) {
  const spacingClasses = {
    sm: 'my-4',
    md: 'my-6', 
    lg: 'my-8'
  }

  const variants = {
    line: "border-t border-border/50",
    space: "h-px",
    gradient: "h-px bg-gradient-to-r from-transparent via-border/30 to-transparent"
  }

  return (
    <div 
      className={cn(
        spacingClasses[spacing],
        variants[variant],
        className
      )} 
      {...props}
    />
  )
}

// Wrapper para páginas densas con mejor jerarquía visual
interface DensePageLayoutProps {
  children: React.ReactNode
  title: string
  description?: string
  actions?: React.ReactNode
}

export function DensePageLayout({ 
  children, 
  title, 
  description, 
  actions 
}: DensePageLayoutProps) {
  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header mejorado con agrupación visual */}
      <SectionGroup variant="subtle">
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
            {description && (
              <p className="text-muted-foreground text-lg">{description}</p>
            )}
          </div>
          {actions && (
            <div className="flex items-center gap-2">
              {actions}
            </div>
          )}
        </div>
      </SectionGroup>

      <SectionDivider variant="gradient" spacing="lg" />

      {/* Contenido principal */}
      <div className="space-y-6">
        {children}
      </div>
    </div>
  )
}

// Card mejorada para contenido denso
interface DenseCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string
  description?: string
  icon?: React.ReactNode
  children: React.ReactNode
  headerActions?: React.ReactNode
}

export function DenseCard({ 
  title, 
  description, 
  icon, 
  children, 
  headerActions,
  className,
  ...props 
}: DenseCardProps) {
  return (
    <Card 
      className={cn(
        "shadow-sm border-border/50 overflow-hidden",
        className
      )} 
      {...props}
    >
      <CardHeader className="border-b border-border/20 bg-muted/10">
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <CardTitle className="flex items-center gap-2 text-xl">
              {icon && <span className="text-primary">{icon}</span>}
              {title}
            </CardTitle>
            {description && (
              <CardDescription className="text-base">
                {description}
              </CardDescription>
            )}
          </div>
          {headerActions && (
            <div className="flex items-center gap-2">
              {headerActions}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {children}
      </CardContent>
    </Card>
  )
}

// Componente para filtros agrupados
interface FilterGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string
  children: React.ReactNode
  density?: 'compact' | 'spacious'
}

export function FilterGroup({ 
  title, 
  children, 
  density = 'spacious',
  className, 
  ...props 
}: FilterGroupProps) {
  const padding = density === 'compact' ? 'p-4' : 'p-6'
  const gap = density === 'compact' ? 'gap-4' : 'gap-6'
  return (
    <div 
      className={cn(
        `bg-muted/20 border border-border/30 rounded-lg ${padding} space-y-3`,
        className
      )} 
      {...props}
    >
      {title && (
        <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
      )}
      <div className={`flex flex-wrap ${gap}`}>
        {children}
      </div>
    </div>
  )
}

// Tabla mejorada para contenido denso (única de visual-hierarchy-improvements)
interface DenseTableRowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  children: React.ReactNode
  index: number
}

export function DenseTableRow({
  children,
  index,
  className,
  ...props
}: DenseTableRowProps) {
  return (
    <TableRow
      index={index}
      className={cn(
        "border-b border-border/20 hover:bg-muted/30 transition-colors",
        className
      )}
      {...props}
    >
      {children}
    </TableRow>
  )
}