"use client";

import { useCallback, useMemo, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";

/**
 * Landing final:
 * - Hero con copy para CTO/PO + CTA (Quickstart/Pricing) e imagen /dashboard-hero.png
 * - Diferenciadores (XAI, LTR, Cold-start, RLS, LATAM-ready) con gradiente
 * - Cómo funciona (3 pasos) con círculos numerados centrados
 * - Snippets dinámicos (Python | Node | cURL) + botón Copiar
 * - Resultados que puedes esperar (KPIs claros, sin duplicación con el hero)
 * - Casos de éxito (3 cards con resultado + descripción breve)
 * - FAQ (objeciones típicas)
 * - CTA final
 */

type Lang = "python" | "node" | "curl";

export default function HomePage() {
  const [language, setLanguage] = useState<Lang>("python");
  const [copied, setCopied] = useState(false);

  const snippets: Record<Lang, string> = useMemo(
    () => ({
      python: `# pip install rayuela-sdk
from rayuela import Rayuela

client = Rayuela(api_key="YOUR_API_KEY")
recs = client.recommend("user-123", limit=10)
print(f"Got {len(recs.items)} recommendations!")`,
      node: `// npm i rayuela-sdk
import Rayuela from "rayuela-sdk";

const client = new Rayuela({ apiKey: "YOUR_API_KEY" });
const recs = await client.recommend("user-123", { limit: 10 });
console.log(\`Got \${recs.items.length} recommendations!\`);`,
      curl: `curl -X POST "https://api.rayuela.ai/api/v1/recommendations/ecommerce/homepage" \\
  -H "X-API-Key: YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{"external_user_id":"user-123","limit":10}'`,
    }),
    []
  );

  const onCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(snippets[language]);
      setCopied(true);
      setTimeout(() => setCopied(false), 1600);
    } catch {
      // noop
    }
  }, [language, snippets]);

  // Animations
  const fadeUp = {
    hidden: { opacity: 0, y: 24 },
    show: { opacity: 1, y: 0 },
  };

  const springCard = {
    hidden: { opacity: 0, y: 16 },
    show: { opacity: 1, y: 0 },
  };

  return (
    <main className="bg-white">
      {/* ===== HERO ===== */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6 grid md:grid-cols-2 gap-6 md:gap-8 items-center">
          {/* Columna de texto */}
          <motion.div
            variants={fadeUp}
            initial="hidden"
            animate="show"
            className="order-2 md:order-1 mb-8 md:mb-0"
          >
            <h1 className="text-4xl md:text-5xl font-bold leading-tight mb-4">
              Personalización con IA para E-commerce y Marketplaces
            </h1>
            <p className="text-lg text-muted-foreground mb-6">
              Rayuela es una API-first de recomendaciones: integración en <strong>horas</strong>, impacto en{" "}
              <strong>conversión y AOV</strong>, sin MLOps ni complejidad. Diseñada para CTOs, Product Leaders y devs.
            </p>
            <div className="flex flex-wrap gap-4">
              <Link href="/onboarding" className="btn-primary" aria-label="Setup en 10 minutos">
                Setup en 10 min
              </Link>
              <Link href="/api-explorer" className="btn-secondary" aria-label="Probar API Explorer">
                API Explorer
              </Link>
              <Link href="/roi-calculator" className="btn-secondary" aria-label="Calcular ROI">
                ROI Calculator
              </Link>
            </div>
            <div className="mt-4 flex flex-wrap gap-4">
              <Link href="/register" className="text-blue-600 hover:text-blue-800 font-medium" aria-label="Crear cuenta gratis">
                Crear cuenta gratis →
              </Link>
              <Link href="/community" className="text-blue-600 hover:text-blue-800 font-medium" aria-label="Ver comunidad">
                Comunidad (2,847+ devs) →
              </Link>
            </div>
            <div className="mt-2 text-xs text-foreground bg-muted px-4 py-3 rounded-lg inline-flex flex-wrap gap-x-4 gap-y-2">
              <span>🚀 Setup en 10 minutos</span>
              <span>🧪 A/B testing automático</span>
              <span>📊 +25% CTR promedio</span>
            </div>
          </motion.div>

          {/* Columna de imagen */}
          <motion.div
            className="order-1 md:order-2 flex justify-center"
            initial={{ opacity: 0, y: 32 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Image
              src="/dashboard-hero.png"
              alt="Rayuela dashboard: métricas y recomendaciones"
              width={430}
              height={420}
              priority
              className="hero-img"
            />
          </motion.div>
        </div>
      </section>

      {/* ===== DIFERENCIADORES ===== */}
      <section className="section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <motion.h2
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.3 }}
            className="text-3xl font-bold text-center mb-10"
          >
            Diferenciadores clave
          </motion.h2>

          <div className="grid md:grid-cols-3 gap-6">
            {[
              {
                t: "IA explicable (XAI)",
                d: "Entiende por qué se recomienda cada ítem. Transparencia para PMs, confianza para tu negocio.",
              },
              {
                t: "Optimizado con LTR",
                d: "Learning-to-Rank entrenado para maximizar métricas de negocio (conversión, AOV), no solo precisión.",
              },
              {
                t: "Cold-start sin fricción",
                d: "Fallback inteligente y señales iniciales para mostrar recomendaciones relevantes desde el día uno.",
              },
              {
                t: "Multi-tenant seguro (RLS)",
                d: "Aislamiento de datos por cuenta a nivel base de datos. Listo para producción.",
              },
              {
                t: "LATAM-ready",
                d: "Integración nativa con Mercado Pago y focus en latencia/regulación regional.",
              },
              {
                t: "DX impecable",
                d: "OpenAPI/SDKs, ejemplos copy-paste y Quickstart para reducir time-to-value.",
              },
            ].map((f, i) => (
              <motion.div
                key={f.t}
                className="card"
                variants={springCard}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.2 }}
                custom={i}
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 120, damping: 16 }}
              >
                <h3 className="text-lg font-semibold mb-2">{f.t}</h3>
                <p className="text-muted-foreground">{f.d}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* ===== NUEVAS FUNCIONALIDADES ===== */}
      <section className="py-14 md:py-16 bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.3 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold mb-4">
              Nuevas funcionalidades para acelerar tu adopción
            </h2>
            <p className="text-xl text-muted-foreground">
              Herramientas diseñadas para reducir el time-to-value de semanas a minutos
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                icon: "🚀",
                title: "Onboarding de 10 minutos",
                description: "Setup guiado desde signup hasta primera recomendación",
                link: "/onboarding",
                badge: "Nuevo",
                color: "blue"
              },
              {
                icon: "🔧",
                title: "API Explorer Interactivo",
                description: "Prueba endpoints en tiempo real y genera código automáticamente",
                link: "/api-explorer",
                badge: null,
                color: "purple"
              },
              {
                icon: "💰",
                title: "ROI Calculator",
                description: "Calcula impacto de negocio y genera business cases automáticos",
                link: "/roi-calculator",
                badge: "Popular",
                color: "green"
              },
              {
                icon: "👥",
                title: "Comunidad de Desarrolladores",
                description: "2,847+ devs compartiendo código, casos de uso y mejores prácticas",
                link: "/community",
                badge: null,
                color: "orange"
              }
            ].map((feature, i) => (
              <motion.div
                key={feature.title}
                variants={springCard}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.2 }}
                custom={i}
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 120, damping: 16 }}
              >
                <Link href={feature.link} className="block">
                  <div className="card h-full hover:shadow-lg transition-shadow">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center mb-4 ${
                      {
                        blue: 'bg-blue-100',
                        purple: 'bg-purple-100',
                        green: 'bg-green-100',
                        orange: 'bg-orange-100'
                      }[feature.color]
                    }`}>
                      <span className="text-2xl">{feature.icon}</span>
                    </div>
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-lg font-semibold">{feature.title}</h3>
                      {feature.badge && (
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          feature.badge === 'Nuevo' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                        }`}>
                          {feature.badge}
                        </span>
                      )}
                    </div>
                    <p className="text-muted-foreground mb-4">{feature.description}</p>
                    <div className="text-blue-600 font-medium text-sm">
                      Explorar →
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>

          <motion.div
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.3 }}
            className="text-center mt-12"
          >
            <div className="bg-white rounded-lg p-6 shadow-lg max-w-4xl mx-auto">
              <h3 className="text-xl font-bold mb-4">Resultados comprobados</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-1">+25%</div>
                  <div className="text-sm text-muted-foreground">CTR promedio vs baseline</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-1">10min</div>
                  <div className="text-sm text-muted-foreground">Time-to-first-value</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600 mb-1">2,847+</div>
                  <div className="text-sm text-muted-foreground">Desarrolladores activos</div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* ===== CÓMO FUNCIONA ===== */}
      <section className="section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <motion.h2
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.3 }}
            className="text-3xl font-bold text-center mb-10"
          >
            Cómo funciona
          </motion.h2>

          <div className="grid md:grid-cols-3 gap-6">
            {[
              {
                n: "1",
                t: "Ingesta tus datos",
                d: "Envía productos, usuarios e interacciones por API o lotes. Soporte de IDs externos.",
              },
              {
                n: "2",
                t: "Entrena el modelo",
                d: "Entrenamiento automático (híbrido) optimizado para tus KPIs. Sin MLOps.",
              },
              {
                n: "3",
                t: "Sirve recomendaciones",
                d: "Obtén recomendaciones personalizadas con latencia baja y explicación opcional (XAI).",
              },
            ].map((s, i) => (
              <motion.div
                key={s.t}
                className="card text-center"
                variants={springCard}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.2 }}
                custom={i}
                whileHover={{ scale: 1.02 }}
              >
                <div className="step-circle">{s.n}</div>
                <h3 className="text-lg font-semibold mb-2">{s.t}</h3>
                <p className="text-muted-foreground">{s.d}</p>
              </motion.div>
            ))}
          </div>

          <div className="flex justify-center mt-8">
            <Link href="/docs/quickstart/python" className="btn-primary" aria-label="Ir al Quickstart de Python">
              Integrar en 3 pasos
            </Link>
          </div>
        </div>
      </section>

      {/* ===== CODE SNIPPETS ===== */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-6">
          <motion.h2
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.3 }}
            className="text-3xl font-bold text-center mb-6"
          >
            Tu primera recomendación en minutos
          </motion.h2>

          <motion.div
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.2 }}
            className="flex flex-wrap items-center justify-center gap-3 mb-5"
          >
            {(["python", "node", "curl"] as Lang[]).map((lang) => (
              <button
                key={lang}
                type="button"
                onClick={() => setLanguage(lang)}
                className={`px-4 py-2 rounded transition ${
                  language === lang ? "bg-purple-600 text-white" : "bg-gray-200 text-foreground"
                }`}
                aria-pressed={language === lang ? "true" : "false"}
                aria-label={`Mostrar snippet ${lang}`}
              >
                {lang}
              </button>
            ))}
          </motion.div>
          <div className="relative bg-gray-900 text-green-300 rounded-lg p-4 font-mono text-sm overflow-auto">
            <button
              type="button"
              onClick={onCopy}
              className="absolute top-3 right-3 bg-gray-700 text-white text-xs px-3 py-1 rounded-md hover:bg-gray-600 transition"
              aria-label="Copiar snippet al portapapeles"
            >
              {copied ? "Copiado ✓" : "Copiar"}
            </button>

            <motion.pre
              key={language}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.25 }}
              className="bg-gray-900 text-green-400 rounded-lg p-6 text-left overflow-x-auto"
            >
            <code>{snippets[language]}</code>
            </motion.pre>
          </div>

          <div className="text-center mt-6">
            <Link href="/docs" className="btn-secondary" aria-label="Ir a la documentación completa">
              Ver documentación completa
            </Link>
          </div>
        </div>
      </section>

      {/* ===== RESULTADOS QUE PUEDES ESPERAR ===== */}
      <section className="section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <motion.h2
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.3 }}
            className="text-3xl font-bold text-center mb-10"
          >
            Resultados que puedes esperar
          </motion.h2>

          <div className="grid md:grid-cols-3 gap-6">
            {[
              { k: "+15%", t: "Conversión", d: "Incremento promedio tras 30-45 días." },
              { k: "+12%", t: "AOV", d: "Ticket promedio impulsado por recomendaciones." },
              { k: "-25%", t: "Tiempo de integración", d: "vs. construir un RecSys in-house." },
            ].map((m, i) => (
              <motion.div
                key={m.t}
                className="card text-center"
                variants={springCard}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.2 }}
                custom={i}
              >
                <div className="text-3xl font-extrabold mb-2">{m.k}</div>
                <div className="font-semibold mb-1">{m.t}</div>
                <p className="text-muted-foreground">{m.d}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* ===== OPORTUNIDADES DE NEGOCIO ===== */}
      <section className="section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.3 }}
            className="text-center mb-10"
          >
            <h2 className="text-3xl font-bold mb-4">
              Oportunidades de negocio por industria
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Descubre el potencial de las recomendaciones inteligentes en tu sector.{" "}
              <strong className="text-purple-600">Haz clic para explorar cada oportunidad.</strong>
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                title: "Marketplaces",
                industry: "Plataformas de Matching",
                opportunity: "Hasta +40% GMV",
                desc: "Conecta oferta y demanda de forma inteligente. Reduce tiempo de búsqueda y aumenta matches exitosos.",
                href: "/use-cases/marketplaces",
                potential: "Matching semántico, discovery automático"
              },
              {
                title: "E-commerce",
                industry: "Tiendas Online",
                opportunity: "Hasta +30% AOV",
                desc: "Personaliza la experiencia de compra. Aumenta ticket promedio y repeat purchases con cross-selling inteligente.",
                href: "/use-cases/ecommerce",
                potential: "Recomendaciones contextuales, upselling"
              },
              {
                title: "SaaS B2B",
                industry: "Software Empresarial",
                opportunity: "MVP con IA en 1 semana",
                desc: "Agrega inteligencia artificial sin equipo de ML. Diferénciate de la competencia con features avanzadas.",
                href: "/use-cases/saas-b2b",
                potential: "Ahorra $280k vs equipo interno"
              },
              {
                title: "Contenido Digital",
                industry: "Media & Entretenimiento",
                opportunity: "Hasta +50% engagement",
                desc: "Retén usuarios con feeds ultra-personalizados. Compite con TikTok y YouTube en engagement.",
                href: "/use-cases/content-digital",
                potential: "Algoritmos de retención avanzados"
              },
            ].map((c, i) => (
              <Link key={c.title} href={c.href} className="block group">
                <motion.div
                  className="card cursor-pointer transition-all duration-300 hover:shadow-xl hover:border-purple-200 group-hover:bg-gradient-to-br group-hover:from-white group-hover:to-purple-50"
                  variants={springCard}
                  initial="hidden"
                  whileInView="show"
                  viewport={{ once: true, amount: 0.2 }}
                  custom={i}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="flex items-center gap-3 mb-3">
                    <div>
                      <div className="text-sm font-medium text-purple-600 group-hover:text-purple-700">
                        {c.industry}
                      </div>
                      <div className="text-lg font-bold text-foreground group-hover:text-purple-900">
                        {c.title}
                      </div>
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-green-600 mb-2 group-hover:text-green-700">
                    {c.opportunity}
                  </div>
                  <p className="text-muted-foreground mb-3 group-hover:text-foreground leading-relaxed">
                    {c.desc}
                  </p>
                  <div className="text-xs text-muted-foreground mb-3 group-hover:text-muted-foreground">
                    {c.potential}
                  </div>
                  <div className="flex items-center text-purple-600 group-hover:text-purple-700 text-sm font-medium">
                    Explorar oportunidad
                    <svg className="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </motion.div>
              </Link>
            ))}
          </div>

          <div className="flex justify-center mt-8">
            <Link href="/register?utm_campaign=success-stories" className="btn-primary" aria-label="Crear cuenta">
              Únete como early adopter
            </Link>
          </div>
        </div>
      </section>

      {/* ===== FAQ ===== */}
      <section className="py-16 bg-white">
        <div className="max-w-5xl mx-auto px-6">
          <motion.h2
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.3 }}
            className="text-3xl font-bold text-center mb-10"
          >
            Preguntas frecuentes
          </motion.h2>

          <div className="grid md:grid-cols-2 gap-6">
            {[
              {
                q: "¿Necesito un equipo de ML para usar Rayuela?",
                a: "No. Rayuela abstrae el MLOps y ofrece defaults sólidos (híbrido + LTR). Si tienes Data Scientists/ML Engineers, pueden ajustar estrategias y explicar salidas con XAI.",
              },
              {
                q: "¿Cómo se maneja el cold-start?",
                a: "Usamos fallback inteligente (best-sellers, similitudes y señales iniciales). Aseguramos relevancia desde el primer día.",
              },
              {
                q: "¿Qué tan rápido puedo integrar?",
                a: "Con el Quickstart, puedes servir recomendaciones en horas. Nuestra DX (SDKs, OpenAPI y ejemplos) reduce drásticamente el time-to-value.",
              },
              {
                q: "¿Es seguro para datos sensibles?",
                a: "Sí. Arquitectura multi-tenant con RLS y aislamiento por cuenta. Logs y auditoría a nivel de API.",
              },
            ].map((f, i) => (
              <motion.div
                key={f.q}
                className="card"
                variants={springCard}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.2 }}
                custom={i}
              >
                <h3 className="font-semibold mb-2">{f.q}</h3>
                <p className="text-muted-foreground">{f.a}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* ===== CTA FINAL ===== */}
      <section className="section-gradient-strong">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <motion.h2
            variants={fadeUp}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.3 }}
            className="text-3xl font-bold mb-4"
          >
            Empieza gratis y llega a tu primera recomendación hoy
          </motion.h2>
          <p className="text-muted-foreground mb-6">
            Crea tu cuenta, genera tu API Key y sigue el Quickstart. Si necesitas ayuda, te acompañamos en la integración.
          </p>
          <div className="flex justify-center gap-4">
            <Link href="/register" className="btn-primary" aria-label="Crear cuenta">
              Crear cuenta
            </Link>
            <Link href="/docs/quickstart/python" className="btn-secondary" aria-label="Ir al Quickstart">
              Ver Quickstart
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}
