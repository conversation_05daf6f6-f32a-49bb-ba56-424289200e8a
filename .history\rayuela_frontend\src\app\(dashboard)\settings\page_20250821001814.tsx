"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { InfoIcon, BellIcon, WrenchIcon, KeyIcon, UserIcon, MailIcon, CalendarIcon } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
// import { toast } from "sonner"; // No usado actualmente
import { handleApiError } from "@/lib/error-handler";
import { useAuth } from "@/lib/auth";
import { getMyAccount } from '@/lib/api';
import type { AccountResponse } from '@/lib/generated/rayuelaAPI';

export default function SettingsPage() {
  const { user, token, apiKey } = useAuth();
  // const [saving, setSaving] = useState(false); // No usado actualmente
  const [isLoading, setIsLoading] = useState(true);
  const [accountData, setAccountData] = useState<AccountResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Obtener datos de la cuenta
  useEffect(() => {
    const fetchAccountData = async () => {
      if (!token || !apiKey) {
        setIsLoading(false);
        return;
      }

      try {
        const data = await getMyAccount();
        setAccountData(data);
      } catch (err: unknown) {
        console.error("Error al obtener datos de la cuenta:", err);
        setError((err as Error).message || "Error al cargar los datos de la cuenta");
      } finally {
        setIsLoading(false);
      }
    };

    fetchAccountData();
  }, [token, apiKey]);

  // const handleSave = () => {
  //   setSaving(true);
  //   // Simulate API call
  //   setTimeout(() => {
  //     setSaving(false);
  //     toast.success("Cambios guardados correctamente");
  //   }, 1000);
  // };

  const handleError = () => {
    try {
      // Simular un error de límite excedido
      throw {
        status: 429,
        error_code: "RESOURCE_LIMIT_EXCEEDED",
        message: "Alcanzaste tu límite gratuito. Desbloqueá más recomendaciones desde 99 USD/mes."
      };
    } catch (error) {
      handleApiError(error, "Error al guardar los cambios");
    }
  };

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6 text-foreground">
        Settings
      </h1>
      <p className="text-muted-foreground mb-4">
        Manage your account settings and preferences.
      </p>

              <Alert variant="warning">
                  <InfoIcon className="h-4 w-4" />
          <AlertTitle>Funcionalidad en desarrollo</AlertTitle>
                  <AlertDescription>
          <p>Estamos trabajando en mejorar esta sección. Algunas funcionalidades están en desarrollo y estarán disponibles próximamente.</p>
          <p className="mt-2">Actualmente, solo la pestaña "Account" está parcialmente funcional. Las demás pestañas estarán disponibles en futuras actualizaciones.</p>
        </AlertDescription>
      </Alert>

      <Tabs defaultValue="account" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="api">API Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="account">
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
              <CardDescription>
                Update your account details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {isLoading ? (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                </div>
              ) : error ? (
                <div className="text-red-500 p-4 border border-red-200 rounded-md bg-red-50 dark:bg-red-900/20 dark:border-red-800">
                  {error}
                </div>
              ) : (
                <>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <UserIcon className="h-4 w-4 text-muted-foreground mr-2" />
                      <Label htmlFor="name">Nombre de Usuario</Label>
                    </div>
                    <Input
                      id="name"
                      placeholder="Tu nombre"
                      value={user?.email?.split('@')[0] || ''}
                      disabled
                      className="bg-muted"
                    />
                    <p className="text-xs text-muted-foreground">El nombre de usuario se deriva de tu email.</p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center">
                      <MailIcon className="h-4 w-4 text-gray-500 mr-2" />
                      <Label htmlFor="email">Email</Label>
                    </div>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Tu email"
                      value={user?.email || ''}
                      disabled
                      className="bg-muted"
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400">Tu email de inicio de sesión.</p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center">
                      <KeyIcon className="h-4 w-4 text-gray-500 mr-2" />
                      <Label htmlFor="company">Nombre de la Cuenta</Label>
                    </div>
                    <Input
                      id="company"
                      placeholder="Tu empresa"
                      value={accountData?.name || ''}
                      disabled
                      className="bg-muted"
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400">El nombre de tu cuenta o empresa.</p>
                  </div>
                </>
              )}
            </CardContent>
            <CardFooter>
              <div className="flex gap-2">
                <Button asChild variant="outline">
                  <a
                    href="https://docs.rayuela.ai/account/change-password"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center"
                  >
                    <KeyIcon className="mr-2 h-4 w-4" />
                    Cambiar Contraseña
                  </a>
                </Button>
                <Button
                  variant="outline"
                  onClick={handleError}
                  className="text-warning border-warning/30 hover:bg-warning-light hover:text-warning"
                >
                  Simular Error
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>
                Configure how you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center justify-center py-8 px-4 text-center">
                <BellIcon className="h-12 w-12 text-gray-300 dark:text-gray-600 mb-4" />
                <p className="text-gray-500 dark:text-gray-400 mb-2">
                  Configuración de notificaciones disponible próximamente
                </p>
                <p className="text-sm text-gray-400 dark:text-gray-500">
                  Podrás configurar notificaciones por email, SMS y webhooks para eventos importantes como entrenamientos completados, límites de uso cercanos, etc.
                </p>
                <Badge variant="warning" className="mt-4">
                  <CalendarIcon className="h-3 w-3 mr-1" />
                  Disponible en Q4 2023
                </Badge>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="api">
          <Card>
            <CardHeader>
              <CardTitle>API Settings</CardTitle>
              <CardDescription>
                Configure API behavior and defaults
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center justify-center py-8 px-4 text-center">
                <WrenchIcon className="h-12 w-12 text-gray-300 dark:text-gray-600 mb-4" />
                <p className="text-gray-500 dark:text-gray-400 mb-2">
                  Configuración de API disponible próximamente
                </p>
                <p className="text-sm text-gray-400 dark:text-gray-500">
                  Podrás configurar comportamientos por defecto de la API, webhooks, límites personalizados, regiones preferidas, etc.
                </p>
                <Badge variant="warning" className="mt-4">
                  <CalendarIcon className="h-3 w-3 mr-1" />
                  Disponible en Q1 2024
                </Badge>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
