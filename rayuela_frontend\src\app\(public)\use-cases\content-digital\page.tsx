import React from "react";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowL<PERSON>t, Clock, Eye, Heart } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'Contenido Digital - Casos de Uso | Rayuela.ai',
  description: 'Retén usuarios con feeds inteligentes y recomendaciones personalizadas. Aumenta tiempo de sesión 22% y engagement en plataformas de contenido.',
  path: '/use-cases/content-digital',
  keywords: ['contenido digital', 'streaming', 'feed', 'personalización', 'engagement', 'retención'],
});

export default function ContentDigitalUseCasePage() {
  return (
    <main className="bg-white">
      {/* Breadcrumb */}
      <section className="py-6 border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Link href="/" className="hover:text-purple-600 transition-colors flex items-center gap-1">
              <ArrowLeft className="w-4 h-4" />
              Inicio
            </Link>
            <span>/</span>
            <span className="text-foreground font-medium">Contenido Digital</span>
          </div>
        </div>
      </section>

      {/* Hero Section */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-purple-50 text-purple-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <span>📰</span>{" "}
              Caso de Uso: Contenido Digital
            </div>
            <h1 className="text-4xl md:text-5xl font-bold leading-tight mb-6">
              Retén usuarios con feeds inteligentes
            </h1>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto mb-8">
              Transforma tu plataforma de contenido en una experiencia adictiva que mantiene
              a los usuarios enganchados con recomendaciones ultra-personalizadas.
            </p>
          </div>

          {/* Oportunidad de Negocio */}
          <div className="card max-w-5xl mx-auto">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-purple-700 rounded-xl flex items-center justify-center flex-shrink-0">
                <span className="text-white text-2xl">💡</span>
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-bold text-foreground mb-3">
                  Oportunidad: Plataformas de contenido pueden aumentar tiempo de sesión hasta 35% y engagement 50%
                </h3>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  <strong>Plataformas de noticias</strong> con 500,000+ usuarios activos y 2M+ artículos
                  pueden implementar Rayuela para personalizar feeds y aumentar retención. Potencial en 60 días:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-green-600 mb-1">+35%</div>
                    <div className="text-sm text-muted-foreground">Tiempo de sesión</div>
                  </div>
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600 mb-1">+50%</div>
                    <div className="text-sm text-muted-foreground">Engagement total</div>
                  </div>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600 mb-1">+28%</div>
                    <div className="text-sm text-muted-foreground">DAU (usuarios activos)</div>
                  </div>
                </div>
                <blockquote className="pl-4 border-l-4 border-purple-200 italic text-muted-foreground bg-muted p-4 rounded-r-lg">
                  "Nuestros usuarios ahora pasan 40% más tiempo en la app. Los feeds personalizados 
                  han transformado completamente la experiencia de lectura."
                  <footer className="mt-2 text-sm not-italic">
                    — <strong className="text-foreground">Elena Martín</strong>, Head of Product en NewsApp
                  </footer>
                </blockquote>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Metrics */}
      <section className="section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid gap-6 md:grid-cols-3">
            <div className="card text-center">
              <Clock className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-green-600 mb-2">+22%</div>
              <p className="text-muted-foreground">Aumento en tiempo de sesión promedio</p>
            </div>
            <div className="card text-center">
              <Heart className="w-12 h-12 text-purple-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-purple-600 mb-2">+45%</div>
              <p className="text-muted-foreground">Mejora en engagement (likes, shares)</p>
            </div>
            <div className="card text-center">
              <Eye className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <div className="text-3xl font-bold text-blue-600 mb-2">+30%</div>
              <p className="text-muted-foreground">Incremento en retención a 7 días</p>
            </div>
          </div>
        </div>
      </section>

      {/* Problem & Solution */}
      <section className="py-14 md:py-16 bg-muted">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid gap-8 md:grid-cols-2">
            <div className="card border-red-200 bg-red-50">
              <h3 className="text-xl font-bold text-red-700 mb-4">El Problema</h3>
              <ul className="space-y-3 text-muted-foreground">
                <li>• <strong>Feeds genéricos:</strong> Todos ven el mismo contenido destacado</li>
                <li>• <strong>Baja retención:</strong> Usuarios abandonan por falta de relevancia</li>
                <li>• <strong>Contenido perdido:</strong> Artículos valiosos quedan sin descubrir</li>
                <li>• <strong>Competencia feroz:</strong> TikTok, YouTube, Netflix dominan atención</li>
                <li>• <strong>Algoritmos básicos:</strong> Solo por popularidad o fecha</li>
              </ul>
            </div>

            <div className="card border-green-200 bg-green-50">
              <h3 className="text-xl font-bold text-green-700 mb-4">La Solución Rayuela</h3>
              <ul className="space-y-3 text-muted-foreground">
                <li>• <strong>Feeds ultra-personalizados:</strong> Cada usuario ve contenido único</li>
                <li>• <strong>Retención maximizada:</strong> Algoritmos diseñados para engagement</li>
                <li>• <strong>Discovery inteligente:</strong> Contenido relevante emerge automáticamente</li>
                <li>• <strong>Experiencia adictiva:</strong> Compite con las mejores plataformas</li>
                <li>• <strong>ML avanzado:</strong> Aprende de cada interacción en tiempo real</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Tipos de Contenido */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Funciona con cualquier tipo de contenido</h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Algoritmos adaptativos para diferentes formatos y plataformas
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="card hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-blue-600 text-xl">📰</span>
              </div>
              <h4 className="font-bold text-lg mb-3">Noticias & Artículos</h4>
              <p className="text-muted-foreground mb-4 text-sm">
                Personaliza feeds de noticias según intereses, ubicación y comportamiento de lectura.
              </p>
              <div className="text-xs text-purple-600 font-semibold">
                Ejemplo: "Noticias que te interesan según tu historial"
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-red-600 text-xl">🎥</span>
              </div>
              <h4 className="font-bold text-lg mb-3">Video & Streaming</h4>
              <p className="text-muted-foreground mb-4 text-sm">
                Recomienda videos, series y películas basado en gustos y patrones de visualización.
              </p>
              <div className="text-xs text-purple-600 font-semibold">
                Ejemplo: "Porque viste X, te recomendamos Y"
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-green-600 text-xl">🎵</span>
              </div>
              <h4 className="font-bold text-lg mb-3">Música & Podcasts</h4>
              <p className="text-muted-foreground mb-4 text-sm">
                Crea playlists automáticas y recomienda contenido audio según mood y contexto.
              </p>
              <div className="text-xs text-purple-600 font-semibold">
                Ejemplo: "Tu mix personalizado para trabajar"
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-purple-600 text-xl">📱</span>
              </div>
              <h4 className="font-bold text-lg mb-3">Social Media</h4>
              <p className="text-muted-foreground mb-4 text-sm">
                Optimiza feeds sociales para maximizar engagement y tiempo en plataforma.
              </p>
              <div className="text-xs text-purple-600 font-semibold">
                Ejemplo: "Posts que más te van a gustar"
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-orange-600 text-xl">📚</span>
              </div>
              <h4 className="font-bold text-lg mb-3">Blogs & Educativo</h4>
              <p className="text-muted-foreground mb-4 text-sm">
                Sugiere artículos, cursos y recursos educativos según nivel y objetivos.
              </p>
              <div className="text-xs text-purple-600 font-semibold">
                Ejemplo: "Siguiente artículo en tu learning path"
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-teal-600 text-xl">🎮</span>
              </div>
              <h4 className="font-bold text-lg mb-3">Gaming & Entretenimiento</h4>
              <p className="text-muted-foreground mb-4 text-sm">
                Recomienda juegos, streams y contenido gaming según preferencias y skill level.
              </p>
              <div className="text-xs text-purple-600 font-semibold">
                Ejemplo: "Juegos que te van a encantar"
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* ROI Calculator */}
      <section className="py-14 md:py-16 section-gradient-strong">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Calculadora de ROI para plataformas de contenido</h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Impacto de personalizar feeds en tus métricas de engagement
            </p>
          </div>
          
          <div className="card max-w-5xl mx-auto">
            <div className="grid gap-8 md:grid-cols-2">
              <div className="space-y-4">
                <h4 className="font-bold text-lg">Ejemplo: Plataforma con 100k usuarios activos</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between p-3 bg-muted rounded">
                    <span>Usuarios activos mensuales:</span>
                    <span className="font-mono font-semibold">100,000</span>
                  </div>
                  <div className="flex justify-between p-3 bg-muted rounded">
                    <span>Tiempo sesión promedio:</span>
                    <span className="font-mono font-semibold">8 minutos</span>
                  </div>
                  <div className="flex justify-between p-3 bg-muted rounded">
                    <span>Revenue por usuario/mes:</span>
                    <span className="font-mono font-semibold">$2.50</span>
                  </div>
                  <div className="flex justify-between p-3 bg-muted rounded border-l-4 border-gray-400">
                    <span className="font-semibold">Revenue mensual actual:</span>
                    <span className="font-mono font-bold">$250,000</span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <h4 className="font-bold text-lg text-green-700">Con Rayuela (+22% engagement):</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between p-3 bg-green-50 rounded">
                    <span>Nuevo tiempo de sesión:</span>
                    <span className="font-mono font-semibold text-green-700">9.8 minutos</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-50 rounded">
                    <span>Mayor engagement = más ads:</span>
                    <span className="font-mono font-semibold text-green-700">+22% revenue</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-50 rounded">
                    <span>Mejor retención = más usuarios:</span>
                    <span className="font-mono font-semibold text-green-700">+15% MAU</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-100 rounded border-l-4 border-green-500">
                    <span className="font-semibold">Revenue mensual nuevo:</span>
                    <span className="font-mono font-bold text-green-700">$350,750</span>
                  </div>
                  <div className="flex justify-between p-3 bg-green-200 rounded border-l-4 border-green-600">
                    <span className="font-bold">Incremento anual:</span>
                    <span className="font-mono font-bold text-green-800 text-lg">+$1.2M</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-8 p-6 bg-purple-50 rounded-lg border border-purple-200">
              <div className="text-center">
                <div className="text-purple-700 font-semibold mb-2">💡 Costo de Rayuela: $299/mes</div>
                <div className="text-purple-600 text-sm">
                  ROI de <strong>28,000%</strong> en el primer año • Recuperas la inversión en <strong>3 días</strong>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonios */}
      <section className="py-14 md:py-16 bg-muted">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Lo que dicen nuestros clientes de contenido</h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Plataformas que ya están maximizando engagement con Rayuela
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div className="card hover:shadow-lg transition-all duration-300">
              <blockquote className="text-muted-foreground mb-4 leading-relaxed italic">
                "Nuestros usuarios pasan 40% más tiempo leyendo. Los feeds personalizados han sido un game changer total."
              </blockquote>
              <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xl">👩‍💼</span>
                </div>
                <div>
                  <div className="font-bold text-foreground">Elena Martín</div>
                  <div className="text-sm text-muted-foreground">Head of Product</div>
                  <div className="text-xs text-muted-foreground">NewsApp</div>
                </div>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg px-3 py-2">
                <span className="text-xs font-semibold text-green-700">+35% tiempo sesión, +50% engagement</span>
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <blockquote className="text-muted-foreground mb-4 leading-relaxed italic">
                "La retención de usuarios se disparó. Ahora descubren contenido que realmente les interesa, no solo lo popular."
              </blockquote>
              <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xl">👨‍💻</span>
                </div>
                <div>
                  <div className="font-bold text-foreground">David Park</div>
                  <div className="text-sm text-muted-foreground">CTO</div>
                  <div className="text-xs text-muted-foreground">StreamHub</div>
                </div>
              </div>
              <div className="bg-blue-50 border border-blue-200 rounded-lg px-3 py-2">
                <span className="text-xs font-semibold text-blue-700">+42% retención a 7 días</span>
              </div>
            </div>

            <div className="card hover:shadow-lg transition-all duration-300">
              <blockquote className="text-muted-foreground mb-4 leading-relaxed italic">
                "Implementamos en 2 días y vimos resultados inmediatos. Nuestros creadores están más satisfechos con el alcance."
              </blockquote>
              <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xl">👩‍🚀</span>
                </div>
                <div>
                  <div className="font-bold text-foreground">Ana Ruiz</div>
                  <div className="text-sm text-muted-foreground">VP of Growth</div>
                  <div className="text-xs text-muted-foreground">CreatorSpace</div>
                </div>
              </div>
              <div className="bg-orange-50 border border-orange-200 rounded-lg px-3 py-2">
                <span className="text-xs font-semibold text-orange-700">+60% alcance orgánico</span>
              </div>
            </div>
          </div>

          {/* Tipos de Plataforma */}
          <div className="text-center">
            <p className="text-sm font-semibold text-purple-600 mb-6">FUNCIONA EN CUALQUIER PLATAFORMA DE CONTENIDO</p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 items-center justify-items-center opacity-60 hover:opacity-80 transition-opacity">
              <div className="flex flex-col items-center justify-center p-3 bg-white border border-border rounded-lg hover:shadow-md transition-all group">
                <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">📰</span>
                <span className="text-xs font-medium text-muted-foreground text-center">Noticias</span>
              </div>
              <div className="flex flex-col items-center justify-center p-3 bg-white border border-border rounded-lg hover:shadow-md transition-all group">
                <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">🎥</span>
                <span className="text-xs font-medium text-muted-foreground text-center">Video</span>
              </div>
              <div className="flex flex-col items-center justify-center p-3 bg-white border border-border rounded-lg hover:shadow-md transition-all group">
                <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">🎵</span>
                <span className="text-xs font-medium text-muted-foreground text-center">Audio</span>
              </div>
              <div className="flex flex-col items-center justify-center p-3 bg-white border border-border rounded-lg hover:shadow-md transition-all group">
                <span className="text-2xl mb-1 group-hover:scale-110 transition-transform">📱</span>
                <span className="text-xs font-medium text-muted-foreground text-center">Social</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Algoritmos de Personalización */}
      <section className="py-14 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Algoritmos de personalización avanzados</h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Tecnología de ML que aprende de cada interacción para maximizar engagement
            </p>
          </div>

          <div className="card max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Factores de personalización */}
              <div>
                <h4 className="font-bold text-lg mb-4">Factores de Personalización</h4>
                <div className="space-y-4">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="font-semibold text-blue-700 mb-2">Comportamiento Histórico</div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <div>• Contenido consumido previamente</div>
                      <div>• Tiempo de lectura/visualización</div>
                      <div>• Patrones de navegación</div>
                      <div>• Interacciones (likes, shares, comments)</div>
                    </div>
                  </div>

                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div className="font-semibold text-purple-700 mb-2">Contexto Temporal</div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <div>• Hora del día y día de la semana</div>
                      <div>• Estacionalidad y eventos</div>
                      <div>• Tendencias en tiempo real</div>
                      <div>• Frecuencia de uso</div>
                    </div>
                  </div>

                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="font-semibold text-green-700 mb-2">Similaridad de Usuarios</div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <div>• Usuarios con gustos similares</div>
                      <div>• Clustering por comportamiento</div>
                      <div>• Collaborative filtering avanzado</div>
                      <div>• Descubrimiento de nichos</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Métricas optimizadas */}
              <div>
                <h4 className="font-bold text-lg mb-4">Métricas Optimizadas</h4>
                <div className="space-y-4">
                  <div className="p-4 bg-muted rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-semibold">Tiempo de Sesión</span>
                      <span className="text-green-600 font-bold">+22% promedio</span>
                    </div>
                    <div className="text-sm text-muted-foreground">Usuarios permanecen más tiempo consumiendo contenido relevante</div>
                  </div>

                  <div className="p-4 bg-muted rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-semibold">Click-Through Rate</span>
                      <span className="text-blue-600 font-bold">+35% CTR</span>
                    </div>
                    <div className="text-sm text-muted-foreground">Mayor probabilidad de hacer clic en contenido recomendado</div>
                  </div>

                  <div className="p-4 bg-muted rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-semibold">Retención de Usuarios</span>
                      <span className="text-purple-600 font-bold">+30% a 7 días</span>
                    </div>
                    <div className="text-sm text-muted-foreground">Usuarios regresan más frecuentemente a la plataforma</div>
                  </div>

                  <div className="p-4 bg-muted rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-semibold">Engagement Rate</span>
                      <span className="text-orange-600 font-bold">+45% interacciones</span>
                    </div>
                    <div className="text-sm text-muted-foreground">Más likes, shares, comments y acciones sociales</div>
                  </div>
                </div>

                <div className="mt-6 bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-purple-600">🎯</span>
                    <span className="font-bold text-purple-700">Resultado Final</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    <strong>Experiencia adictiva</strong> que compite con TikTok, YouTube y Netflix en términos de engagement y retención de usuarios.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Final */}
      <section className="py-14 md:py-16 section-gradient-strong">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold mb-4">
            ¿Listo para retener usuarios como NewsApp?
          </h2>
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
            Únete a plataformas de contenido que ya están maximizando engagement 
            con feeds inteligentes de Rayuela.
          </p>
          <div className="flex flex-wrap gap-4 justify-center">
            <Link href="/register?utm_source=content-digital-final" className="btn-primary">
              Empezar gratis ahora
            </Link>
            <Link href="/contact-sales?utm_source=content-digital-final&industry=content" className="btn-secondary">
              Hablar con experto en contenido
            </Link>
          </div>
          <p className="text-sm text-muted-foreground mt-6">
            Sin compromiso • Feeds personalizados en 48 horas • Engagement visible desde día 1
          </p>
        </div>
      </section>
    </main>
  );
}
