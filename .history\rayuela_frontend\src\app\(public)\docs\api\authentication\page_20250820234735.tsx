import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { ArrowLeft, Key, Shield, AlertTriangle, CheckCircle } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'Autenticación - API Reference',
  description: 'Documentación completa sobre autenticación en la API de Rayuela. API Keys, JWT tokens, registro de cuentas y mejores prácticas de seguridad.',
  path: '/docs/api/authentication',
  keywords: ['autenticación', 'API key', 'JWT', 'seguridad', 'registro', 'login'],
});

export default function AuthenticationApiPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="mb-8">
          <Button variant="ghost" asChild className="mb-4">
            <Link href="/docs">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver a Documentación
            </Link>
          </Button>
          
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            Autenticación
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl">
            Documentación completa sobre autenticación y seguridad en la API de Rayuela.
          </p>
        </div>

        {/* Overview */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Métodos de Autenticación
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Rayuela utiliza dos métodos de autenticación según el tipo de operación:
            </p>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="p-4 border rounded-lg">
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Key className="w-4 h-4" />
                  API Keys
                </h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Para llamadas de aplicación (recomendaciones, ingesta de datos)
                </p>
                <Badge variant="outline">Recomendado para producción</Badge>
              </div>
              <div className="p-4 border rounded-lg">
                <h4 className="font-semibold mb-2">JWT Tokens</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Para acceso al dashboard y gestión de cuenta
                </p>
                <Badge variant="outline">Solo para dashboard</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Keys */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>API Keys</CardTitle>
            <CardDescription>
              Autenticación principal para llamadas de aplicación
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-info/10 border border-info/20 rounded-lg">
                <p className="text-sm text-info-foreground">
                  <strong>💡 Recomendación:</strong> Usa API Keys para todas las llamadas de tu aplicación en producción.
                </p>
              </div>

              <h4 className="font-semibold">Formato de API Key</h4>
              <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
                <pre className="text-sm font-mono overflow-x-auto">
                  sk_prod_1234567890abcdef1234567890abcdef12345678
                </pre>
              </div>

              <h4 className="font-semibold">Uso en Headers</h4>
              <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
                <pre className="text-sm font-mono overflow-x-auto">
{`curl -X GET "https://api.rayuela.ai/api/v1/health/auth" \\
     -H "X-API-Key: sk_prod_tu_api_key_aqui"`}
                </pre>
              </div>

              <h4 className="font-semibold">Verificar API Key</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Usa este endpoint para verificar que tu API Key es válida:
              </p>
              <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
                <pre className="text-sm font-mono overflow-x-auto">
{`GET /health/auth

# Respuesta exitosa (200):
{
  "status": "authenticated",
  "account_id": 123,
  "plan": "professional"
}`}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Account Registration */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Registro de Cuenta</CardTitle>
            <div className="flex gap-2 mt-2">
              <Badge variant="default">POST</Badge>
              <Badge variant="outline">/auth/register</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Crea una nueva cuenta y obtén tu primera API Key automáticamente.
            </p>

            <h4 className="font-semibold mb-3">Request</h4>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm mb-4">
              <pre className="text-sm font-mono overflow-x-auto">
{`curl -X POST "https://api.rayuela.ai/api/v1/auth/register" \\
     -H "Content-Type: application/json" \\
     -d '{
       "name": "Mi Empresa",
       "email": "<EMAIL>",
       "password": "password_seguro_123"
     }'`}
              </pre>
            </div>

            <h4 className="font-semibold mb-3">Respuesta</h4>
            <div className="bg-slate-800 dark:bg-slate-900 text-slate-100 rounded-lg p-4 border border-slate-700 shadow-sm">
              <pre className="text-sm font-mono overflow-x-auto">
{`{
  "message": "Cuenta creada exitosamente",
  "account_id": 123,
  "api_key": "sk_prod_1234567890abcdef...",
  "verification_required": true
}`}
              </pre>
            </div>

            <div className="mt-4 p-4 bg-warning/10 border border-warning/20 rounded-lg">
              <p className="text-sm text-warning-foreground">
                <AlertTriangle className="w-4 h-4 inline mr-2" />
                <strong>Importante:</strong> Guarda tu API Key de forma segura. No se puede recuperar después del registro.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Security Best Practices */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Mejores Prácticas de Seguridad
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-success mt-0.5" />
                <div>
                  <h4 className="font-semibold">Variables de Entorno</h4>
                  <p className="text-sm text-muted-foreground">
                    Nunca hardcodees API Keys en tu código. Usa variables de entorno.
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-success mt-0.5" />
                <div>
                  <h4 className="font-semibold">HTTPS Obligatorio</h4>
                  <p className="text-sm text-muted-foreground">
                    Todas las llamadas deben usar HTTPS para proteger las API Keys en tránsito.
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-success mt-0.5" />
                <div>
                  <h4 className="font-semibold">Rotación de Keys</h4>
                  <p className="text-sm text-muted-foreground">
                    Rota tus API Keys periódicamente desde el dashboard.
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-success mt-0.5" />
                <div>
                  <h4 className="font-semibold">Monitoreo</h4>
                  <p className="text-sm text-muted-foreground">
                    Monitorea el uso de tus API Keys desde el dashboard para detectar uso anómalo.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error Codes */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Códigos de Error de Autenticación</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Badge variant="destructive">401</Badge>
                <span className="text-sm">Unauthorized - API Key inválida o faltante</span>
              </div>
              <div className="flex items-center gap-3">
                <Badge variant="destructive">403</Badge>
                <span className="text-sm">Forbidden - API Key válida pero sin permisos</span>
              </div>
              <div className="flex items-center gap-3">
                <Badge variant="destructive">429</Badge>
                <span className="text-sm">Rate Limit Exceeded - Demasiadas requests</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Siguientes Pasos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <Button asChild variant="outline">
                <Link href="/docs/quickstart/python">
                  Inicio Rápido
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/docs/api/recommendations">
                  API de Recomendaciones
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/docs/api/batch">
                  Ingesta de Datos
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/register">
                  Crear Cuenta
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
