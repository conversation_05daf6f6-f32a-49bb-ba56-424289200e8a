import * as React from "react"
import { cn } from "@/lib/utils"

// Consistent spacing constants for micro-interactions
export const SPACING = {
  // Icon-text relationships
  iconText: {
    sm: "gap-1.5", // 6px - for small icons with caption text
    md: "gap-2",   // 8px - for standard icons with body text
    lg: "gap-3",   // 12px - for larger icons or headings
  },
  // Element grouping
  elementGroup: {
    tight: "gap-1",     // 4px - for tightly related elements
    normal: "gap-2",    // 8px - for normal element spacing
    loose: "gap-3",     // 12px - for loosely related elements
  },
  // Vertical stacking
  stack: {
    xs: "space-y-1",    // 4px - for very tight vertical stacking
    sm: "space-y-1.5",  // 6px - for compact lists
    md: "space-y-2",    // 8px - for normal vertical spacing
    lg: "space-y-3",    // 12px - for section spacing
    xl: "space-y-4",    // 16px - for major section breaks
  },
  // Container spacing
  container: {
    tight: "py-4 px-3",   // Compact containers
    normal: "py-6 px-4",  // Standard containers (matches Card)
    loose: "py-8 px-6",   // Spacious containers
  }
} as const

// Icon-Text component for consistent alignment
interface IconTextProps extends React.HTMLAttributes<HTMLDivElement> {
  icon: React.ReactNode
  size?: keyof typeof SPACING.iconText
  align?: 'start' | 'center' | 'end'
}

export function IconText({ 
  icon, 
  children, 
  size = 'md', 
  align = 'center',
  className, 
  ...props 
}: IconTextProps) {
  const alignmentClass = {
    start: 'items-start',
    center: 'items-center', 
    end: 'items-end'
  }[align]

  return (
    <div 
      className={cn(
        "flex", 
        SPACING.iconText[size], 
        alignmentClass,
        className
      )} 
      {...props}
    >
      <span className="shrink-0 flex items-center">
        {icon}
      </span>
      <span className="min-w-0">
        {children}
      </span>
    </div>
  )
}

// Vertical Stack component for consistent vertical spacing
interface StackProps extends React.HTMLAttributes<HTMLDivElement> {
  spacing?: keyof typeof SPACING.stack
}

export function Stack({ 
  spacing = 'md', 
  className, 
  children, 
  ...props 
}: StackProps) {
  return (
    <div 
      className={cn(
        SPACING.stack[spacing],
        className
      )} 
      {...props}
    >
      {children}
    </div>
  )
}

// Horizontal Group component for consistent horizontal spacing
interface GroupProps extends React.HTMLAttributes<HTMLDivElement> {
  spacing?: keyof typeof SPACING.elementGroup
  align?: 'start' | 'center' | 'end' | 'stretch'
  wrap?: boolean
}

export function Group({ 
  spacing = 'normal', 
  align = 'center',
  wrap = false,
  className, 
  children, 
  ...props 
}: GroupProps) {
  const alignmentClass = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end', 
    stretch: 'items-stretch'
  }[align]

  return (
    <div 
      className={cn(
        "flex", 
        SPACING.elementGroup[spacing], 
        alignmentClass,
        wrap && "flex-wrap",
        className
      )} 
      {...props}
    >
      {children}
    </div>
  )
}

// Container wrapper with consistent spacing
interface ContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  spacing?: keyof typeof SPACING.container
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
}

export function Container({ 
  spacing = 'normal',
  maxWidth = 'full',
  className, 
  children, 
  ...props 
}: ContainerProps) {
  const maxWidthClass = maxWidth === 'full' ? '' : `max-w-${maxWidth}`
  
  return (
    <div 
      className={cn(
        "mx-auto",
        maxWidthClass,
        SPACING.container[spacing],
        className
      )} 
      {...props}
    >
      {children}
    </div>
  )
} 
